.machine	"any"
.abiversion	2
.text

.align	7
.LAES_Te:
	mflr	0
	bcl	20,31,$+4
	mflr	3
	addi	3,3,120
	mtlr	0
	blr	
.long	0
.byte	0,12,0x14,0,0,0,0,0
.space	28
.LAES_Td:
	mflr	0
	bcl	20,31,$+4
	mflr	3
	addi	3,3,2360
	mtlr	0
	blr	
.long	0
.byte	0,12,0x14,0,0,0,0,0
.space	28
.long	0xc66363a5,0xc66363a5
.long	0xf87c7c84,0xf87c7c84
.long	0xee777799,0xee777799
.long	0xf67b7b8d,0xf67b7b8d
.long	0xfff2f20d,0xfff2f20d
.long	0xd66b6bbd,0xd66b6bbd
.long	0xde6f6fb1,0xde6f6fb1
.long	0x91c5c554,0x91c5c554
.long	0x60303050,0x60303050
.long	0x02010103,0x02010103
.long	0xce6767a9,0xce6767a9
.long	0x562b2b7d,0x562b2b7d
.long	0xe7fefe19,0xe7fefe19
.long	0xb5d7d762,0xb5d7d762
.long	0x4dababe6,0x4dababe6
.long	0xec76769a,0xec76769a
.long	0x8fcaca45,0x8fcaca45
.long	0x1f82829d,0x1f82829d
.long	0x89c9c940,0x89c9c940
.long	0xfa7d7d87,0xfa7d7d87
.long	0xeffafa15,0xeffafa15
.long	0xb25959eb,0xb25959eb
.long	0x8e4747c9,0x8e4747c9
.long	0xfbf0f00b,0xfbf0f00b
.long	0x41adadec,0x41adadec
.long	0xb3d4d467,0xb3d4d467
.long	0x5fa2a2fd,0x5fa2a2fd
.long	0x45afafea,0x45afafea
.long	0x239c9cbf,0x239c9cbf
.long	0x53a4a4f7,0x53a4a4f7
.long	0xe4727296,0xe4727296
.long	0x9bc0c05b,0x9bc0c05b
.long	0x75b7b7c2,0x75b7b7c2
.long	0xe1fdfd1c,0xe1fdfd1c
.long	0x3d9393ae,0x3d9393ae
.long	0x4c26266a,0x4c26266a
.long	0x6c36365a,0x6c36365a
.long	0x7e3f3f41,0x7e3f3f41
.long	0xf5f7f702,0xf5f7f702
.long	0x83cccc4f,0x83cccc4f
.long	0x6834345c,0x6834345c
.long	0x51a5a5f4,0x51a5a5f4
.long	0xd1e5e534,0xd1e5e534
.long	0xf9f1f108,0xf9f1f108
.long	0xe2717193,0xe2717193
.long	0xabd8d873,0xabd8d873
.long	0x62313153,0x62313153
.long	0x2a15153f,0x2a15153f
.long	0x0804040c,0x0804040c
.long	0x95c7c752,0x95c7c752
.long	0x46232365,0x46232365
.long	0x9dc3c35e,0x9dc3c35e
.long	0x30181828,0x30181828
.long	0x379696a1,0x379696a1
.long	0x0a05050f,0x0a05050f
.long	0x2f9a9ab5,0x2f9a9ab5
.long	0x0e070709,0x0e070709
.long	0x24121236,0x24121236
.long	0x1b80809b,0x1b80809b
.long	0xdfe2e23d,0xdfe2e23d
.long	0xcdebeb26,0xcdebeb26
.long	0x4e272769,0x4e272769
.long	0x7fb2b2cd,0x7fb2b2cd
.long	0xea75759f,0xea75759f
.long	0x1209091b,0x1209091b
.long	0x1d83839e,0x1d83839e
.long	0x582c2c74,0x582c2c74
.long	0x341a1a2e,0x341a1a2e
.long	0x361b1b2d,0x361b1b2d
.long	0xdc6e6eb2,0xdc6e6eb2
.long	0xb45a5aee,0xb45a5aee
.long	0x5ba0a0fb,0x5ba0a0fb
.long	0xa45252f6,0xa45252f6
.long	0x763b3b4d,0x763b3b4d
.long	0xb7d6d661,0xb7d6d661
.long	0x7db3b3ce,0x7db3b3ce
.long	0x5229297b,0x5229297b
.long	0xdde3e33e,0xdde3e33e
.long	0x5e2f2f71,0x5e2f2f71
.long	0x13848497,0x13848497
.long	0xa65353f5,0xa65353f5
.long	0xb9d1d168,0xb9d1d168
.long	0x00000000,0x00000000
.long	0xc1eded2c,0xc1eded2c
.long	0x40202060,0x40202060
.long	0xe3fcfc1f,0xe3fcfc1f
.long	0x79b1b1c8,0x79b1b1c8
.long	0xb65b5bed,0xb65b5bed
.long	0xd46a6abe,0xd46a6abe
.long	0x8dcbcb46,0x8dcbcb46
.long	0x67bebed9,0x67bebed9
.long	0x7239394b,0x7239394b
.long	0x944a4ade,0x944a4ade
.long	0x984c4cd4,0x984c4cd4
.long	0xb05858e8,0xb05858e8
.long	0x85cfcf4a,0x85cfcf4a
.long	0xbbd0d06b,0xbbd0d06b
.long	0xc5efef2a,0xc5efef2a
.long	0x4faaaae5,0x4faaaae5
.long	0xedfbfb16,0xedfbfb16
.long	0x864343c5,0x864343c5
.long	0x9a4d4dd7,0x9a4d4dd7
.long	0x66333355,0x66333355
.long	0x11858594,0x11858594
.long	0x8a4545cf,0x8a4545cf
.long	0xe9f9f910,0xe9f9f910
.long	0x04020206,0x04020206
.long	0xfe7f7f81,0xfe7f7f81
.long	0xa05050f0,0xa05050f0
.long	0x783c3c44,0x783c3c44
.long	0x259f9fba,0x259f9fba
.long	0x4ba8a8e3,0x4ba8a8e3
.long	0xa25151f3,0xa25151f3
.long	0x5da3a3fe,0x5da3a3fe
.long	0x804040c0,0x804040c0
.long	0x058f8f8a,0x058f8f8a
.long	0x3f9292ad,0x3f9292ad
.long	0x219d9dbc,0x219d9dbc
.long	0x70383848,0x70383848
.long	0xf1f5f504,0xf1f5f504
.long	0x63bcbcdf,0x63bcbcdf
.long	0x77b6b6c1,0x77b6b6c1
.long	0xafdada75,0xafdada75
.long	0x42212163,0x42212163
.long	0x20101030,0x20101030
.long	0xe5ffff1a,0xe5ffff1a
.long	0xfdf3f30e,0xfdf3f30e
.long	0xbfd2d26d,0xbfd2d26d
.long	0x81cdcd4c,0x81cdcd4c
.long	0x180c0c14,0x180c0c14
.long	0x26131335,0x26131335
.long	0xc3ecec2f,0xc3ecec2f
.long	0xbe5f5fe1,0xbe5f5fe1
.long	0x359797a2,0x359797a2
.long	0x884444cc,0x884444cc
.long	0x2e171739,0x2e171739
.long	0x93c4c457,0x93c4c457
.long	0x55a7a7f2,0x55a7a7f2
.long	0xfc7e7e82,0xfc7e7e82
.long	0x7a3d3d47,0x7a3d3d47
.long	0xc86464ac,0xc86464ac
.long	0xba5d5de7,0xba5d5de7
.long	0x3219192b,0x3219192b
.long	0xe6737395,0xe6737395
.long	0xc06060a0,0xc06060a0
.long	0x19818198,0x19818198
.long	0x9e4f4fd1,0x9e4f4fd1
.long	0xa3dcdc7f,0xa3dcdc7f
.long	0x44222266,0x44222266
.long	0x542a2a7e,0x542a2a7e
.long	0x3b9090ab,0x3b9090ab
.long	0x0b888883,0x0b888883
.long	0x8c4646ca,0x8c4646ca
.long	0xc7eeee29,0xc7eeee29
.long	0x6bb8b8d3,0x6bb8b8d3
.long	0x2814143c,0x2814143c
.long	0xa7dede79,0xa7dede79
.long	0xbc5e5ee2,0xbc5e5ee2
.long	0x160b0b1d,0x160b0b1d
.long	0xaddbdb76,0xaddbdb76
.long	0xdbe0e03b,0xdbe0e03b
.long	0x64323256,0x64323256
.long	0x743a3a4e,0x743a3a4e
.long	0x140a0a1e,0x140a0a1e
.long	0x924949db,0x924949db
.long	0x0c06060a,0x0c06060a
.long	0x4824246c,0x4824246c
.long	0xb85c5ce4,0xb85c5ce4
.long	0x9fc2c25d,0x9fc2c25d
.long	0xbdd3d36e,0xbdd3d36e
.long	0x43acacef,0x43acacef
.long	0xc46262a6,0xc46262a6
.long	0x399191a8,0x399191a8
.long	0x319595a4,0x319595a4
.long	0xd3e4e437,0xd3e4e437
.long	0xf279798b,0xf279798b
.long	0xd5e7e732,0xd5e7e732
.long	0x8bc8c843,0x8bc8c843
.long	0x6e373759,0x6e373759
.long	0xda6d6db7,0xda6d6db7
.long	0x018d8d8c,0x018d8d8c
.long	0xb1d5d564,0xb1d5d564
.long	0x9c4e4ed2,0x9c4e4ed2
.long	0x49a9a9e0,0x49a9a9e0
.long	0xd86c6cb4,0xd86c6cb4
.long	0xac5656fa,0xac5656fa
.long	0xf3f4f407,0xf3f4f407
.long	0xcfeaea25,0xcfeaea25
.long	0xca6565af,0xca6565af
.long	0xf47a7a8e,0xf47a7a8e
.long	0x47aeaee9,0x47aeaee9
.long	0x10080818,0x10080818
.long	0x6fbabad5,0x6fbabad5
.long	0xf0787888,0xf0787888
.long	0x4a25256f,0x4a25256f
.long	0x5c2e2e72,0x5c2e2e72
.long	0x381c1c24,0x381c1c24
.long	0x57a6a6f1,0x57a6a6f1
.long	0x73b4b4c7,0x73b4b4c7
.long	0x97c6c651,0x97c6c651
.long	0xcbe8e823,0xcbe8e823
.long	0xa1dddd7c,0xa1dddd7c
.long	0xe874749c,0xe874749c
.long	0x3e1f1f21,0x3e1f1f21
.long	0x964b4bdd,0x964b4bdd
.long	0x61bdbddc,0x61bdbddc
.long	0x0d8b8b86,0x0d8b8b86
.long	0x0f8a8a85,0x0f8a8a85
.long	0xe0707090,0xe0707090
.long	0x7c3e3e42,0x7c3e3e42
.long	0x71b5b5c4,0x71b5b5c4
.long	0xcc6666aa,0xcc6666aa
.long	0x904848d8,0x904848d8
.long	0x06030305,0x06030305
.long	0xf7f6f601,0xf7f6f601
.long	0x1c0e0e12,0x1c0e0e12
.long	0xc26161a3,0xc26161a3
.long	0x6a35355f,0x6a35355f
.long	0xae5757f9,0xae5757f9
.long	0x69b9b9d0,0x69b9b9d0
.long	0x17868691,0x17868691
.long	0x99c1c158,0x99c1c158
.long	0x3a1d1d27,0x3a1d1d27
.long	0x279e9eb9,0x279e9eb9
.long	0xd9e1e138,0xd9e1e138
.long	0xebf8f813,0xebf8f813
.long	0x2b9898b3,0x2b9898b3
.long	0x22111133,0x22111133
.long	0xd26969bb,0xd26969bb
.long	0xa9d9d970,0xa9d9d970
.long	0x078e8e89,0x078e8e89
.long	0x339494a7,0x339494a7
.long	0x2d9b9bb6,0x2d9b9bb6
.long	0x3c1e1e22,0x3c1e1e22
.long	0x15878792,0x15878792
.long	0xc9e9e920,0xc9e9e920
.long	0x87cece49,0x87cece49
.long	0xaa5555ff,0xaa5555ff
.long	0x50282878,0x50282878
.long	0xa5dfdf7a,0xa5dfdf7a
.long	0x038c8c8f,0x038c8c8f
.long	0x59a1a1f8,0x59a1a1f8
.long	0x09898980,0x09898980
.long	0x1a0d0d17,0x1a0d0d17
.long	0x65bfbfda,0x65bfbfda
.long	0xd7e6e631,0xd7e6e631
.long	0x844242c6,0x844242c6
.long	0xd06868b8,0xd06868b8
.long	0x824141c3,0x824141c3
.long	0x299999b0,0x299999b0
.long	0x5a2d2d77,0x5a2d2d77
.long	0x1e0f0f11,0x1e0f0f11
.long	0x7bb0b0cb,0x7bb0b0cb
.long	0xa85454fc,0xa85454fc
.long	0x6dbbbbd6,0x6dbbbbd6
.long	0x2c16163a,0x2c16163a
.byte	0x63, 0x7c, 0x77, 0x7b, 0xf2, 0x6b, 0x6f, 0xc5
.byte	0x30, 0x01, 0x67, 0x2b, 0xfe, 0xd7, 0xab, 0x76
.byte	0xca, 0x82, 0xc9, 0x7d, 0xfa, 0x59, 0x47, 0xf0
.byte	0xad, 0xd4, 0xa2, 0xaf, 0x9c, 0xa4, 0x72, 0xc0
.byte	0xb7, 0xfd, 0x93, 0x26, 0x36, 0x3f, 0xf7, 0xcc
.byte	0x34, 0xa5, 0xe5, 0xf1, 0x71, 0xd8, 0x31, 0x15
.byte	0x04, 0xc7, 0x23, 0xc3, 0x18, 0x96, 0x05, 0x9a
.byte	0x07, 0x12, 0x80, 0xe2, 0xeb, 0x27, 0xb2, 0x75
.byte	0x09, 0x83, 0x2c, 0x1a, 0x1b, 0x6e, 0x5a, 0xa0
.byte	0x52, 0x3b, 0xd6, 0xb3, 0x29, 0xe3, 0x2f, 0x84
.byte	0x53, 0xd1, 0x00, 0xed, 0x20, 0xfc, 0xb1, 0x5b
.byte	0x6a, 0xcb, 0xbe, 0x39, 0x4a, 0x4c, 0x58, 0xcf
.byte	0xd0, 0xef, 0xaa, 0xfb, 0x43, 0x4d, 0x33, 0x85
.byte	0x45, 0xf9, 0x02, 0x7f, 0x50, 0x3c, 0x9f, 0xa8
.byte	0x51, 0xa3, 0x40, 0x8f, 0x92, 0x9d, 0x38, 0xf5
.byte	0xbc, 0xb6, 0xda, 0x21, 0x10, 0xff, 0xf3, 0xd2
.byte	0xcd, 0x0c, 0x13, 0xec, 0x5f, 0x97, 0x44, 0x17
.byte	0xc4, 0xa7, 0x7e, 0x3d, 0x64, 0x5d, 0x19, 0x73
.byte	0x60, 0x81, 0x4f, 0xdc, 0x22, 0x2a, 0x90, 0x88
.byte	0x46, 0xee, 0xb8, 0x14, 0xde, 0x5e, 0x0b, 0xdb
.byte	0xe0, 0x32, 0x3a, 0x0a, 0x49, 0x06, 0x24, 0x5c
.byte	0xc2, 0xd3, 0xac, 0x62, 0x91, 0x95, 0xe4, 0x79
.byte	0xe7, 0xc8, 0x37, 0x6d, 0x8d, 0xd5, 0x4e, 0xa9
.byte	0x6c, 0x56, 0xf4, 0xea, 0x65, 0x7a, 0xae, 0x08
.byte	0xba, 0x78, 0x25, 0x2e, 0x1c, 0xa6, 0xb4, 0xc6
.byte	0xe8, 0xdd, 0x74, 0x1f, 0x4b, 0xbd, 0x8b, 0x8a
.byte	0x70, 0x3e, 0xb5, 0x66, 0x48, 0x03, 0xf6, 0x0e
.byte	0x61, 0x35, 0x57, 0xb9, 0x86, 0xc1, 0x1d, 0x9e
.byte	0xe1, 0xf8, 0x98, 0x11, 0x69, 0xd9, 0x8e, 0x94
.byte	0x9b, 0x1e, 0x87, 0xe9, 0xce, 0x55, 0x28, 0xdf
.byte	0x8c, 0xa1, 0x89, 0x0d, 0xbf, 0xe6, 0x42, 0x68
.byte	0x41, 0x99, 0x2d, 0x0f, 0xb0, 0x54, 0xbb, 0x16
.long	0x51f4a750,0x51f4a750
.long	0x7e416553,0x7e416553
.long	0x1a17a4c3,0x1a17a4c3
.long	0x3a275e96,0x3a275e96
.long	0x3bab6bcb,0x3bab6bcb
.long	0x1f9d45f1,0x1f9d45f1
.long	0xacfa58ab,0xacfa58ab
.long	0x4be30393,0x4be30393
.long	0x2030fa55,0x2030fa55
.long	0xad766df6,0xad766df6
.long	0x88cc7691,0x88cc7691
.long	0xf5024c25,0xf5024c25
.long	0x4fe5d7fc,0x4fe5d7fc
.long	0xc52acbd7,0xc52acbd7
.long	0x26354480,0x26354480
.long	0xb562a38f,0xb562a38f
.long	0xdeb15a49,0xdeb15a49
.long	0x25ba1b67,0x25ba1b67
.long	0x45ea0e98,0x45ea0e98
.long	0x5dfec0e1,0x5dfec0e1
.long	0xc32f7502,0xc32f7502
.long	0x814cf012,0x814cf012
.long	0x8d4697a3,0x8d4697a3
.long	0x6bd3f9c6,0x6bd3f9c6
.long	0x038f5fe7,0x038f5fe7
.long	0x15929c95,0x15929c95
.long	0xbf6d7aeb,0xbf6d7aeb
.long	0x955259da,0x955259da
.long	0xd4be832d,0xd4be832d
.long	0x587421d3,0x587421d3
.long	0x49e06929,0x49e06929
.long	0x8ec9c844,0x8ec9c844
.long	0x75c2896a,0x75c2896a
.long	0xf48e7978,0xf48e7978
.long	0x99583e6b,0x99583e6b
.long	0x27b971dd,0x27b971dd
.long	0xbee14fb6,0xbee14fb6
.long	0xf088ad17,0xf088ad17
.long	0xc920ac66,0xc920ac66
.long	0x7dce3ab4,0x7dce3ab4
.long	0x63df4a18,0x63df4a18
.long	0xe51a3182,0xe51a3182
.long	0x97513360,0x97513360
.long	0x62537f45,0x62537f45
.long	0xb16477e0,0xb16477e0
.long	0xbb6bae84,0xbb6bae84
.long	0xfe81a01c,0xfe81a01c
.long	0xf9082b94,0xf9082b94
.long	0x70486858,0x70486858
.long	0x8f45fd19,0x8f45fd19
.long	0x94de6c87,0x94de6c87
.long	0x527bf8b7,0x527bf8b7
.long	0xab73d323,0xab73d323
.long	0x724b02e2,0x724b02e2
.long	0xe31f8f57,0xe31f8f57
.long	0x6655ab2a,0x6655ab2a
.long	0xb2eb2807,0xb2eb2807
.long	0x2fb5c203,0x2fb5c203
.long	0x86c57b9a,0x86c57b9a
.long	0xd33708a5,0xd33708a5
.long	0x302887f2,0x302887f2
.long	0x23bfa5b2,0x23bfa5b2
.long	0x02036aba,0x02036aba
.long	0xed16825c,0xed16825c
.long	0x8acf1c2b,0x8acf1c2b
.long	0xa779b492,0xa779b492
.long	0xf307f2f0,0xf307f2f0
.long	0x4e69e2a1,0x4e69e2a1
.long	0x65daf4cd,0x65daf4cd
.long	0x0605bed5,0x0605bed5
.long	0xd134621f,0xd134621f
.long	0xc4a6fe8a,0xc4a6fe8a
.long	0x342e539d,0x342e539d
.long	0xa2f355a0,0xa2f355a0
.long	0x058ae132,0x058ae132
.long	0xa4f6eb75,0xa4f6eb75
.long	0x0b83ec39,0x0b83ec39
.long	0x4060efaa,0x4060efaa
.long	0x5e719f06,0x5e719f06
.long	0xbd6e1051,0xbd6e1051
.long	0x3e218af9,0x3e218af9
.long	0x96dd063d,0x96dd063d
.long	0xdd3e05ae,0xdd3e05ae
.long	0x4de6bd46,0x4de6bd46
.long	0x91548db5,0x91548db5
.long	0x71c45d05,0x71c45d05
.long	0x0406d46f,0x0406d46f
.long	0x605015ff,0x605015ff
.long	0x1998fb24,0x1998fb24
.long	0xd6bde997,0xd6bde997
.long	0x894043cc,0x894043cc
.long	0x67d99e77,0x67d99e77
.long	0xb0e842bd,0xb0e842bd
.long	0x07898b88,0x07898b88
.long	0xe7195b38,0xe7195b38
.long	0x79c8eedb,0x79c8eedb
.long	0xa17c0a47,0xa17c0a47
.long	0x7c420fe9,0x7c420fe9
.long	0xf8841ec9,0xf8841ec9
.long	0x00000000,0x00000000
.long	0x09808683,0x09808683
.long	0x322bed48,0x322bed48
.long	0x1e1170ac,0x1e1170ac
.long	0x6c5a724e,0x6c5a724e
.long	0xfd0efffb,0xfd0efffb
.long	0x0f853856,0x0f853856
.long	0x3daed51e,0x3daed51e
.long	0x362d3927,0x362d3927
.long	0x0a0fd964,0x0a0fd964
.long	0x685ca621,0x685ca621
.long	0x9b5b54d1,0x9b5b54d1
.long	0x24362e3a,0x24362e3a
.long	0x0c0a67b1,0x0c0a67b1
.long	0x9357e70f,0x9357e70f
.long	0xb4ee96d2,0xb4ee96d2
.long	0x1b9b919e,0x1b9b919e
.long	0x80c0c54f,0x80c0c54f
.long	0x61dc20a2,0x61dc20a2
.long	0x5a774b69,0x5a774b69
.long	0x1c121a16,0x1c121a16
.long	0xe293ba0a,0xe293ba0a
.long	0xc0a02ae5,0xc0a02ae5
.long	0x3c22e043,0x3c22e043
.long	0x121b171d,0x121b171d
.long	0x0e090d0b,0x0e090d0b
.long	0xf28bc7ad,0xf28bc7ad
.long	0x2db6a8b9,0x2db6a8b9
.long	0x141ea9c8,0x141ea9c8
.long	0x57f11985,0x57f11985
.long	0xaf75074c,0xaf75074c
.long	0xee99ddbb,0xee99ddbb
.long	0xa37f60fd,0xa37f60fd
.long	0xf701269f,0xf701269f
.long	0x5c72f5bc,0x5c72f5bc
.long	0x44663bc5,0x44663bc5
.long	0x5bfb7e34,0x5bfb7e34
.long	0x8b432976,0x8b432976
.long	0xcb23c6dc,0xcb23c6dc
.long	0xb6edfc68,0xb6edfc68
.long	0xb8e4f163,0xb8e4f163
.long	0xd731dcca,0xd731dcca
.long	0x42638510,0x42638510
.long	0x13972240,0x13972240
.long	0x84c61120,0x84c61120
.long	0x854a247d,0x854a247d
.long	0xd2bb3df8,0xd2bb3df8
.long	0xaef93211,0xaef93211
.long	0xc729a16d,0xc729a16d
.long	0x1d9e2f4b,0x1d9e2f4b
.long	0xdcb230f3,0xdcb230f3
.long	0x0d8652ec,0x0d8652ec
.long	0x77c1e3d0,0x77c1e3d0
.long	0x2bb3166c,0x2bb3166c
.long	0xa970b999,0xa970b999
.long	0x119448fa,0x119448fa
.long	0x47e96422,0x47e96422
.long	0xa8fc8cc4,0xa8fc8cc4
.long	0xa0f03f1a,0xa0f03f1a
.long	0x567d2cd8,0x567d2cd8
.long	0x223390ef,0x223390ef
.long	0x87494ec7,0x87494ec7
.long	0xd938d1c1,0xd938d1c1
.long	0x8ccaa2fe,0x8ccaa2fe
.long	0x98d40b36,0x98d40b36
.long	0xa6f581cf,0xa6f581cf
.long	0xa57ade28,0xa57ade28
.long	0xdab78e26,0xdab78e26
.long	0x3fadbfa4,0x3fadbfa4
.long	0x2c3a9de4,0x2c3a9de4
.long	0x5078920d,0x5078920d
.long	0x6a5fcc9b,0x6a5fcc9b
.long	0x547e4662,0x547e4662
.long	0xf68d13c2,0xf68d13c2
.long	0x90d8b8e8,0x90d8b8e8
.long	0x2e39f75e,0x2e39f75e
.long	0x82c3aff5,0x82c3aff5
.long	0x9f5d80be,0x9f5d80be
.long	0x69d0937c,0x69d0937c
.long	0x6fd52da9,0x6fd52da9
.long	0xcf2512b3,0xcf2512b3
.long	0xc8ac993b,0xc8ac993b
.long	0x10187da7,0x10187da7
.long	0xe89c636e,0xe89c636e
.long	0xdb3bbb7b,0xdb3bbb7b
.long	0xcd267809,0xcd267809
.long	0x6e5918f4,0x6e5918f4
.long	0xec9ab701,0xec9ab701
.long	0x834f9aa8,0x834f9aa8
.long	0xe6956e65,0xe6956e65
.long	0xaaffe67e,0xaaffe67e
.long	0x21bccf08,0x21bccf08
.long	0xef15e8e6,0xef15e8e6
.long	0xbae79bd9,0xbae79bd9
.long	0x4a6f36ce,0x4a6f36ce
.long	0xea9f09d4,0xea9f09d4
.long	0x29b07cd6,0x29b07cd6
.long	0x31a4b2af,0x31a4b2af
.long	0x2a3f2331,0x2a3f2331
.long	0xc6a59430,0xc6a59430
.long	0x35a266c0,0x35a266c0
.long	0x744ebc37,0x744ebc37
.long	0xfc82caa6,0xfc82caa6
.long	0xe090d0b0,0xe090d0b0
.long	0x33a7d815,0x33a7d815
.long	0xf104984a,0xf104984a
.long	0x41ecdaf7,0x41ecdaf7
.long	0x7fcd500e,0x7fcd500e
.long	0x1791f62f,0x1791f62f
.long	0x764dd68d,0x764dd68d
.long	0x43efb04d,0x43efb04d
.long	0xccaa4d54,0xccaa4d54
.long	0xe49604df,0xe49604df
.long	0x9ed1b5e3,0x9ed1b5e3
.long	0x4c6a881b,0x4c6a881b
.long	0xc12c1fb8,0xc12c1fb8
.long	0x4665517f,0x4665517f
.long	0x9d5eea04,0x9d5eea04
.long	0x018c355d,0x018c355d
.long	0xfa877473,0xfa877473
.long	0xfb0b412e,0xfb0b412e
.long	0xb3671d5a,0xb3671d5a
.long	0x92dbd252,0x92dbd252
.long	0xe9105633,0xe9105633
.long	0x6dd64713,0x6dd64713
.long	0x9ad7618c,0x9ad7618c
.long	0x37a10c7a,0x37a10c7a
.long	0x59f8148e,0x59f8148e
.long	0xeb133c89,0xeb133c89
.long	0xcea927ee,0xcea927ee
.long	0xb761c935,0xb761c935
.long	0xe11ce5ed,0xe11ce5ed
.long	0x7a47b13c,0x7a47b13c
.long	0x9cd2df59,0x9cd2df59
.long	0x55f2733f,0x55f2733f
.long	0x1814ce79,0x1814ce79
.long	0x73c737bf,0x73c737bf
.long	0x53f7cdea,0x53f7cdea
.long	0x5ffdaa5b,0x5ffdaa5b
.long	0xdf3d6f14,0xdf3d6f14
.long	0x7844db86,0x7844db86
.long	0xcaaff381,0xcaaff381
.long	0xb968c43e,0xb968c43e
.long	0x3824342c,0x3824342c
.long	0xc2a3405f,0xc2a3405f
.long	0x161dc372,0x161dc372
.long	0xbce2250c,0xbce2250c
.long	0x283c498b,0x283c498b
.long	0xff0d9541,0xff0d9541
.long	0x39a80171,0x39a80171
.long	0x080cb3de,0x080cb3de
.long	0xd8b4e49c,0xd8b4e49c
.long	0x6456c190,0x6456c190
.long	0x7bcb8461,0x7bcb8461
.long	0xd532b670,0xd532b670
.long	0x486c5c74,0x486c5c74
.long	0xd0b85742,0xd0b85742
.byte	0x52, 0x09, 0x6a, 0xd5, 0x30, 0x36, 0xa5, 0x38
.byte	0xbf, 0x40, 0xa3, 0x9e, 0x81, 0xf3, 0xd7, 0xfb
.byte	0x7c, 0xe3, 0x39, 0x82, 0x9b, 0x2f, 0xff, 0x87
.byte	0x34, 0x8e, 0x43, 0x44, 0xc4, 0xde, 0xe9, 0xcb
.byte	0x54, 0x7b, 0x94, 0x32, 0xa6, 0xc2, 0x23, 0x3d
.byte	0xee, 0x4c, 0x95, 0x0b, 0x42, 0xfa, 0xc3, 0x4e
.byte	0x08, 0x2e, 0xa1, 0x66, 0x28, 0xd9, 0x24, 0xb2
.byte	0x76, 0x5b, 0xa2, 0x49, 0x6d, 0x8b, 0xd1, 0x25
.byte	0x72, 0xf8, 0xf6, 0x64, 0x86, 0x68, 0x98, 0x16
.byte	0xd4, 0xa4, 0x5c, 0xcc, 0x5d, 0x65, 0xb6, 0x92
.byte	0x6c, 0x70, 0x48, 0x50, 0xfd, 0xed, 0xb9, 0xda
.byte	0x5e, 0x15, 0x46, 0x57, 0xa7, 0x8d, 0x9d, 0x84
.byte	0x90, 0xd8, 0xab, 0x00, 0x8c, 0xbc, 0xd3, 0x0a
.byte	0xf7, 0xe4, 0x58, 0x05, 0xb8, 0xb3, 0x45, 0x06
.byte	0xd0, 0x2c, 0x1e, 0x8f, 0xca, 0x3f, 0x0f, 0x02
.byte	0xc1, 0xaf, 0xbd, 0x03, 0x01, 0x13, 0x8a, 0x6b
.byte	0x3a, 0x91, 0x11, 0x41, 0x4f, 0x67, 0xdc, 0xea
.byte	0x97, 0xf2, 0xcf, 0xce, 0xf0, 0xb4, 0xe6, 0x73
.byte	0x96, 0xac, 0x74, 0x22, 0xe7, 0xad, 0x35, 0x85
.byte	0xe2, 0xf9, 0x37, 0xe8, 0x1c, 0x75, 0xdf, 0x6e
.byte	0x47, 0xf1, 0x1a, 0x71, 0x1d, 0x29, 0xc5, 0x89
.byte	0x6f, 0xb7, 0x62, 0x0e, 0xaa, 0x18, 0xbe, 0x1b
.byte	0xfc, 0x56, 0x3e, 0x4b, 0xc6, 0xd2, 0x79, 0x20
.byte	0x9a, 0xdb, 0xc0, 0xfe, 0x78, 0xcd, 0x5a, 0xf4
.byte	0x1f, 0xdd, 0xa8, 0x33, 0x88, 0x07, 0xc7, 0x31
.byte	0xb1, 0x12, 0x10, 0x59, 0x27, 0x80, 0xec, 0x5f
.byte	0x60, 0x51, 0x7f, 0xa9, 0x19, 0xb5, 0x4a, 0x0d
.byte	0x2d, 0xe5, 0x7a, 0x9f, 0x93, 0xc9, 0x9c, 0xef
.byte	0xa0, 0xe0, 0x3b, 0x4d, 0xae, 0x2a, 0xf5, 0xb0
.byte	0xc8, 0xeb, 0xbb, 0x3c, 0x83, 0x53, 0x99, 0x61
.byte	0x17, 0x2b, 0x04, 0x7e, 0xba, 0x77, 0xd6, 0x26
.byte	0xe1, 0x69, 0x14, 0x63, 0x55, 0x21, 0x0c, 0x7d


.globl	AES_encrypt
.type	AES_encrypt,@function
.align	7
AES_encrypt:
.localentry	AES_encrypt,0

	stdu	1,-256(1)
	mflr	0

	std	4,104(1)
	std	14,112(1)
	std	15,120(1)
	std	16,128(1)
	std	17,136(1)
	std	18,144(1)
	std	19,152(1)
	std	20,160(1)
	std	21,168(1)
	std	22,176(1)
	std	23,184(1)
	std	24,192(1)
	std	25,200(1)
	std	26,208(1)
	std	27,216(1)
	std	28,224(1)
	std	29,232(1)
	std	30,240(1)
	std	31,248(1)
	std	0,272(1)

	andi.	12,3,3
	andi.	0,4,3
	or.	12,12,0
	bne	.Lenc_unaligned

.Lenc_unaligned_ok:
	lwz	12,0(3)
	lwz	0,4(3)
	lwz	14,8(3)
	lwz	15,12(3)
	rotlwi	8,12,8
	rotlwi	9,0,8
	rotlwi	10,14,8
	rotlwi	11,15,8
	rlwimi	8,12,24,0,7
	rlwimi	9,0,24,0,7
	rlwimi	10,14,24,0,7
	rlwimi	11,15,24,0,7
	rlwimi	8,12,24,16,23
	rlwimi	9,0,24,16,23
	rlwimi	10,14,24,16,23
	rlwimi	11,15,24,16,23
	bl	.LAES_Te
	bl	.Lppc_AES_encrypt_compact
	ld	4,104(1)
	rotlwi	12,8,8
	rotlwi	0,9,8
	rotlwi	14,10,8
	rotlwi	15,11,8
	rlwimi	12,8,24,0,7
	rlwimi	0,9,24,0,7
	rlwimi	14,10,24,0,7
	rlwimi	15,11,24,0,7
	rlwimi	12,8,24,16,23
	rlwimi	0,9,24,16,23
	rlwimi	14,10,24,16,23
	rlwimi	15,11,24,16,23
	stw	12,0(4)
	stw	0,4(4)
	stw	14,8(4)
	stw	15,12(4)
	b	.Lenc_done

.Lenc_unaligned:
	subfic	12,3,4096
	subfic	0,4,4096
	andi.	12,12,4096-16
	beq	.Lenc_xpage
	andi.	0,0,4096-16
	bne	.Lenc_unaligned_ok

.Lenc_xpage:
	lbz	16,0(3)
	lbz	17,1(3)
	lbz	18,2(3)
	lbz	8,3(3)
	lbz	20,4(3)
	lbz	21,5(3)
	lbz	22,6(3)
	lbz	9,7(3)
	lbz	24,8(3)
	lbz	25,9(3)
	lbz	26,10(3)
	insrwi	8,16,8,0
	lbz	10,11(3)
	insrwi	9,20,8,0
	lbz	28,12(3)
	insrwi	8,17,8,8
	lbz	29,13(3)
	insrwi	9,21,8,8
	lbz	30,14(3)
	insrwi	8,18,8,16
	lbz	11,15(3)
	insrwi	9,22,8,16
	insrwi	10,24,8,0
	insrwi	11,28,8,0
	insrwi	10,25,8,8
	insrwi	11,29,8,8
	insrwi	10,26,8,16
	insrwi	11,30,8,16

	bl	.LAES_Te
	bl	.Lppc_AES_encrypt_compact
	ld	4,104(1)

	extrwi	16,8,8,0
	extrwi	17,8,8,8
	stb	16,0(4)
	extrwi	18,8,8,16
	stb	17,1(4)
	stb	18,2(4)
	extrwi	20,9,8,0
	stb	8,3(4)
	extrwi	21,9,8,8
	stb	20,4(4)
	extrwi	22,9,8,16
	stb	21,5(4)
	stb	22,6(4)
	extrwi	24,10,8,0
	stb	9,7(4)
	extrwi	25,10,8,8
	stb	24,8(4)
	extrwi	26,10,8,16
	stb	25,9(4)
	stb	26,10(4)
	extrwi	28,11,8,0
	stb	10,11(4)
	extrwi	29,11,8,8
	stb	28,12(4)
	extrwi	30,11,8,16
	stb	29,13(4)
	stb	30,14(4)
	stb	11,15(4)

.Lenc_done:
	ld	0,272(1)
	ld	14,112(1)
	ld	15,120(1)
	ld	16,128(1)
	ld	17,136(1)
	ld	18,144(1)
	ld	19,152(1)
	ld	20,160(1)
	ld	21,168(1)
	ld	22,176(1)
	ld	23,184(1)
	ld	24,192(1)
	ld	25,200(1)
	ld	26,208(1)
	ld	27,216(1)
	ld	28,224(1)
	ld	29,232(1)
	ld	30,240(1)
	ld	31,248(1)
	mtlr	0
	addi	1,1,256
	blr	
.long	0
.byte	0,12,4,1,0x80,18,3,0
.long	0

.align	5
.Lppc_AES_encrypt:
	lwz	16,240(5)
	addi	6,3,3
	lwz	12,0(5)
	addi	7,3,2
	lwz	0,4(5)
	addi	4,3,1
	lwz	14,8(5)
	addi	16,16,-1
	lwz	15,12(5)
	addi	5,5,16
	xor	8,8,12
	xor	9,9,0
	xor	10,10,14
	xor	11,11,15
	mtctr	16
.align	4
.Lenc_loop:
	rlwinm	16,8,11,21,28
	rlwinm	17,9,11,21,28
	rlwinm	18,10,11,21,28
	rlwinm	19,11,11,21,28
	lwz	12,0(5)
	rlwinm	20,9,19,21,28
	lwz	0,4(5)
	rlwinm	21,10,19,21,28
	lwz	14,8(5)
	rlwinm	22,11,19,21,28
	lwz	15,12(5)
	rlwinm	23,8,19,21,28
	lwzx	16,3,16
	rlwinm	24,10,27,21,28
	lwzx	17,3,17
	rlwinm	25,11,27,21,28
	lwzx	18,3,18
	rlwinm	26,8,27,21,28
	lwzx	19,3,19
	rlwinm	27,9,27,21,28
	lwzx	20,6,20
	rlwinm	28,11,3,21,28
	lwzx	21,6,21
	rlwinm	29,8,3,21,28
	lwzx	22,6,22
	rlwinm	30,9,3,21,28
	lwzx	23,6,23
	rlwinm	31,10,3,21,28
	lwzx	24,7,24
	xor	12,12,16
	lwzx	25,7,25
	xor	0,0,17
	lwzx	26,7,26
	xor	14,14,18
	lwzx	27,7,27
	xor	15,15,19
	lwzx	28,4,28
	xor	12,12,20
	lwzx	29,4,29
	xor	0,0,21
	lwzx	30,4,30
	xor	14,14,22
	lwzx	31,4,31
	xor	15,15,23
	xor	12,12,24
	xor	0,0,25
	xor	14,14,26
	xor	15,15,27
	xor	8,12,28
	xor	9,0,29
	xor	10,14,30
	xor	11,15,31
	addi	5,5,16
	bdnz	.Lenc_loop

	addi	7,3,2048
	nop	
	lwz	12,0(5)
	rlwinm	16,8,8,24,31
	lwz	0,4(5)
	rlwinm	17,9,8,24,31
	lwz	14,8(5)
	rlwinm	18,10,8,24,31
	lwz	15,12(5)
	rlwinm	19,11,8,24,31
	lwz	24,2048(3)
	rlwinm	20,9,16,24,31
	lwz	25,2080(3)
	rlwinm	21,10,16,24,31
	lwz	26,2112(3)
	rlwinm	22,11,16,24,31
	lwz	27,2144(3)
	rlwinm	23,8,16,24,31
	lwz	28,2176(3)
	rlwinm	24,10,24,24,31
	lwz	29,2208(3)
	rlwinm	25,11,24,24,31
	lwz	30,2240(3)
	rlwinm	26,8,24,24,31
	lwz	31,2272(3)
	rlwinm	27,9,24,24,31
	lbzx	16,7,16
	rlwinm	28,11,0,24,31
	lbzx	17,7,17
	rlwinm	29,8,0,24,31
	lbzx	18,7,18
	rlwinm	30,9,0,24,31
	lbzx	19,7,19
	rlwinm	31,10,0,24,31
	lbzx	20,7,20
	rlwinm	8,16,24,0,7
	lbzx	21,7,21
	rlwinm	9,17,24,0,7
	lbzx	22,7,22
	rlwinm	10,18,24,0,7
	lbzx	23,7,23
	rlwinm	11,19,24,0,7
	lbzx	24,7,24
	rlwimi	8,20,16,8,15
	lbzx	25,7,25
	rlwimi	9,21,16,8,15
	lbzx	26,7,26
	rlwimi	10,22,16,8,15
	lbzx	27,7,27
	rlwimi	11,23,16,8,15
	lbzx	28,7,28
	rlwimi	8,24,8,16,23
	lbzx	29,7,29
	rlwimi	9,25,8,16,23
	lbzx	30,7,30
	rlwimi	10,26,8,16,23
	lbzx	31,7,31
	rlwimi	11,27,8,16,23
	or	8,8,28
	or	9,9,29
	or	10,10,30
	or	11,11,31
	xor	8,8,12
	xor	9,9,0
	xor	10,10,14
	xor	11,11,15
	blr	
.long	0
.byte	0,12,0x14,0,0,0,0,0

.align	4
.Lppc_AES_encrypt_compact:
	lwz	16,240(5)
	addi	6,3,2048
	lwz	12,0(5)
	lis	7,0x8080
	lwz	0,4(5)
	lis	4,0x1b1b
	lwz	14,8(5)
	ori	7,7,0x8080
	lwz	15,12(5)
	ori	4,4,0x1b1b
	addi	5,5,16
	mtctr	16
.align	4
.Lenc_compact_loop:
	xor	8,8,12
	xor	9,9,0
	rlwinm	16,8,8,24,31
	xor	10,10,14
	rlwinm	17,9,8,24,31
	xor	11,11,15
	rlwinm	18,10,8,24,31
	rlwinm	19,11,8,24,31
	rlwinm	20,9,16,24,31
	rlwinm	21,10,16,24,31
	rlwinm	22,11,16,24,31
	rlwinm	23,8,16,24,31
	lbzx	16,6,16
	rlwinm	24,10,24,24,31
	lbzx	17,6,17
	rlwinm	25,11,24,24,31
	lbzx	18,6,18
	rlwinm	26,8,24,24,31
	lbzx	19,6,19
	rlwinm	27,9,24,24,31
	lbzx	20,6,20
	rlwinm	28,11,0,24,31
	lbzx	21,6,21
	rlwinm	29,8,0,24,31
	lbzx	22,6,22
	rlwinm	30,9,0,24,31
	lbzx	23,6,23
	rlwinm	31,10,0,24,31
	lbzx	24,6,24
	rlwinm	8,16,24,0,7
	lbzx	25,6,25
	rlwinm	9,17,24,0,7
	lbzx	26,6,26
	rlwinm	10,18,24,0,7
	lbzx	27,6,27
	rlwinm	11,19,24,0,7
	lbzx	28,6,28
	rlwimi	8,20,16,8,15
	lbzx	29,6,29
	rlwimi	9,21,16,8,15
	lbzx	30,6,30
	rlwimi	10,22,16,8,15
	lbzx	31,6,31
	rlwimi	11,23,16,8,15
	rlwimi	8,24,8,16,23
	rlwimi	9,25,8,16,23
	rlwimi	10,26,8,16,23
	rlwimi	11,27,8,16,23
	lwz	12,0(5)
	or	8,8,28
	lwz	0,4(5)
	or	9,9,29
	lwz	14,8(5)
	or	10,10,30
	lwz	15,12(5)
	or	11,11,31

	addi	5,5,16
	bdz	.Lenc_compact_done

	and	16,8,7
	and	17,9,7
	and	18,10,7
	and	19,11,7
	srwi	20,16,7
	andc	24,8,7
	srwi	21,17,7
	andc	25,9,7
	srwi	22,18,7
	andc	26,10,7
	srwi	23,19,7
	andc	27,11,7
	sub	16,16,20
	sub	17,17,21
	sub	18,18,22
	sub	19,19,23
	add	24,24,24
	add	25,25,25
	add	26,26,26
	add	27,27,27
	and	16,16,4
	and	17,17,4
	and	18,18,4
	and	19,19,4
	xor	16,16,24
	xor	17,17,25
	rotlwi	28,8,16
	xor	18,18,26
	rotlwi	29,9,16
	xor	19,19,27
	rotlwi	30,10,16

	xor	8,8,16
	rotlwi	31,11,16
	xor	9,9,17
	rotrwi	8,8,24
	xor	10,10,18
	rotrwi	9,9,24
	xor	11,11,19
	rotrwi	10,10,24
	xor	8,8,16
	rotrwi	11,11,24
	xor	9,9,17
	xor	10,10,18
	xor	11,11,19
	rotlwi	24,28,8
	xor	8,8,28
	rotlwi	25,29,8
	xor	9,9,29
	rotlwi	26,30,8
	xor	10,10,30
	rotlwi	27,31,8
	xor	11,11,31
	xor	8,8,24
	xor	9,9,25
	xor	10,10,26
	xor	11,11,27

	b	.Lenc_compact_loop
.align	4
.Lenc_compact_done:
	xor	8,8,12
	xor	9,9,0
	xor	10,10,14
	xor	11,11,15
	blr	
.long	0
.byte	0,12,0x14,0,0,0,0,0
.size	AES_encrypt,.-AES_encrypt

.globl	AES_decrypt
.type	AES_decrypt,@function
.align	7
AES_decrypt:
.localentry	AES_decrypt,0

	stdu	1,-256(1)
	mflr	0

	std	4,104(1)
	std	14,112(1)
	std	15,120(1)
	std	16,128(1)
	std	17,136(1)
	std	18,144(1)
	std	19,152(1)
	std	20,160(1)
	std	21,168(1)
	std	22,176(1)
	std	23,184(1)
	std	24,192(1)
	std	25,200(1)
	std	26,208(1)
	std	27,216(1)
	std	28,224(1)
	std	29,232(1)
	std	30,240(1)
	std	31,248(1)
	std	0,272(1)

	andi.	12,3,3
	andi.	0,4,3
	or.	12,12,0
	bne	.Ldec_unaligned

.Ldec_unaligned_ok:
	lwz	12,0(3)
	lwz	0,4(3)
	lwz	14,8(3)
	lwz	15,12(3)
	rotlwi	8,12,8
	rotlwi	9,0,8
	rotlwi	10,14,8
	rotlwi	11,15,8
	rlwimi	8,12,24,0,7
	rlwimi	9,0,24,0,7
	rlwimi	10,14,24,0,7
	rlwimi	11,15,24,0,7
	rlwimi	8,12,24,16,23
	rlwimi	9,0,24,16,23
	rlwimi	10,14,24,16,23
	rlwimi	11,15,24,16,23
	bl	.LAES_Td
	bl	.Lppc_AES_decrypt_compact
	ld	4,104(1)
	rotlwi	12,8,8
	rotlwi	0,9,8
	rotlwi	14,10,8
	rotlwi	15,11,8
	rlwimi	12,8,24,0,7
	rlwimi	0,9,24,0,7
	rlwimi	14,10,24,0,7
	rlwimi	15,11,24,0,7
	rlwimi	12,8,24,16,23
	rlwimi	0,9,24,16,23
	rlwimi	14,10,24,16,23
	rlwimi	15,11,24,16,23
	stw	12,0(4)
	stw	0,4(4)
	stw	14,8(4)
	stw	15,12(4)
	b	.Ldec_done

.Ldec_unaligned:
	subfic	12,3,4096
	subfic	0,4,4096
	andi.	12,12,4096-16
	beq	.Ldec_xpage
	andi.	0,0,4096-16
	bne	.Ldec_unaligned_ok

.Ldec_xpage:
	lbz	16,0(3)
	lbz	17,1(3)
	lbz	18,2(3)
	lbz	8,3(3)
	lbz	20,4(3)
	lbz	21,5(3)
	lbz	22,6(3)
	lbz	9,7(3)
	lbz	24,8(3)
	lbz	25,9(3)
	lbz	26,10(3)
	insrwi	8,16,8,0
	lbz	10,11(3)
	insrwi	9,20,8,0
	lbz	28,12(3)
	insrwi	8,17,8,8
	lbz	29,13(3)
	insrwi	9,21,8,8
	lbz	30,14(3)
	insrwi	8,18,8,16
	lbz	11,15(3)
	insrwi	9,22,8,16
	insrwi	10,24,8,0
	insrwi	11,28,8,0
	insrwi	10,25,8,8
	insrwi	11,29,8,8
	insrwi	10,26,8,16
	insrwi	11,30,8,16

	bl	.LAES_Td
	bl	.Lppc_AES_decrypt_compact
	ld	4,104(1)

	extrwi	16,8,8,0
	extrwi	17,8,8,8
	stb	16,0(4)
	extrwi	18,8,8,16
	stb	17,1(4)
	stb	18,2(4)
	extrwi	20,9,8,0
	stb	8,3(4)
	extrwi	21,9,8,8
	stb	20,4(4)
	extrwi	22,9,8,16
	stb	21,5(4)
	stb	22,6(4)
	extrwi	24,10,8,0
	stb	9,7(4)
	extrwi	25,10,8,8
	stb	24,8(4)
	extrwi	26,10,8,16
	stb	25,9(4)
	stb	26,10(4)
	extrwi	28,11,8,0
	stb	10,11(4)
	extrwi	29,11,8,8
	stb	28,12(4)
	extrwi	30,11,8,16
	stb	29,13(4)
	stb	30,14(4)
	stb	11,15(4)

.Ldec_done:
	ld	0,272(1)
	ld	14,112(1)
	ld	15,120(1)
	ld	16,128(1)
	ld	17,136(1)
	ld	18,144(1)
	ld	19,152(1)
	ld	20,160(1)
	ld	21,168(1)
	ld	22,176(1)
	ld	23,184(1)
	ld	24,192(1)
	ld	25,200(1)
	ld	26,208(1)
	ld	27,216(1)
	ld	28,224(1)
	ld	29,232(1)
	ld	30,240(1)
	ld	31,248(1)
	mtlr	0
	addi	1,1,256
	blr	
.long	0
.byte	0,12,4,1,0x80,18,3,0
.long	0

.align	5
.Lppc_AES_decrypt:
	lwz	16,240(5)
	addi	6,3,3
	lwz	12,0(5)
	addi	7,3,2
	lwz	0,4(5)
	addi	4,3,1
	lwz	14,8(5)
	addi	16,16,-1
	lwz	15,12(5)
	addi	5,5,16
	xor	8,8,12
	xor	9,9,0
	xor	10,10,14
	xor	11,11,15
	mtctr	16
.align	4
.Ldec_loop:
	rlwinm	16,8,11,21,28
	rlwinm	17,9,11,21,28
	rlwinm	18,10,11,21,28
	rlwinm	19,11,11,21,28
	lwz	12,0(5)
	rlwinm	20,11,19,21,28
	lwz	0,4(5)
	rlwinm	21,8,19,21,28
	lwz	14,8(5)
	rlwinm	22,9,19,21,28
	lwz	15,12(5)
	rlwinm	23,10,19,21,28
	lwzx	16,3,16
	rlwinm	24,10,27,21,28
	lwzx	17,3,17
	rlwinm	25,11,27,21,28
	lwzx	18,3,18
	rlwinm	26,8,27,21,28
	lwzx	19,3,19
	rlwinm	27,9,27,21,28
	lwzx	20,6,20
	rlwinm	28,9,3,21,28
	lwzx	21,6,21
	rlwinm	29,10,3,21,28
	lwzx	22,6,22
	rlwinm	30,11,3,21,28
	lwzx	23,6,23
	rlwinm	31,8,3,21,28
	lwzx	24,7,24
	xor	12,12,16
	lwzx	25,7,25
	xor	0,0,17
	lwzx	26,7,26
	xor	14,14,18
	lwzx	27,7,27
	xor	15,15,19
	lwzx	28,4,28
	xor	12,12,20
	lwzx	29,4,29
	xor	0,0,21
	lwzx	30,4,30
	xor	14,14,22
	lwzx	31,4,31
	xor	15,15,23
	xor	12,12,24
	xor	0,0,25
	xor	14,14,26
	xor	15,15,27
	xor	8,12,28
	xor	9,0,29
	xor	10,14,30
	xor	11,15,31
	addi	5,5,16
	bdnz	.Ldec_loop

	addi	7,3,2048
	nop	
	lwz	12,0(5)
	rlwinm	16,8,8,24,31
	lwz	0,4(5)
	rlwinm	17,9,8,24,31
	lwz	14,8(5)
	rlwinm	18,10,8,24,31
	lwz	15,12(5)
	rlwinm	19,11,8,24,31
	lwz	24,2048(3)
	rlwinm	20,11,16,24,31
	lwz	25,2080(3)
	rlwinm	21,8,16,24,31
	lwz	26,2112(3)
	lbzx	16,7,16
	lwz	27,2144(3)
	lbzx	17,7,17
	lwz	28,2176(3)
	rlwinm	22,9,16,24,31
	lwz	29,2208(3)
	rlwinm	23,10,16,24,31
	lwz	30,2240(3)
	rlwinm	24,10,24,24,31
	lwz	31,2272(3)
	rlwinm	25,11,24,24,31
	lbzx	18,7,18
	rlwinm	26,8,24,24,31
	lbzx	19,7,19
	rlwinm	27,9,24,24,31
	lbzx	20,7,20
	rlwinm	28,9,0,24,31
	lbzx	21,7,21
	rlwinm	29,10,0,24,31
	lbzx	22,7,22
	rlwinm	30,11,0,24,31
	lbzx	23,7,23
	rlwinm	31,8,0,24,31
	lbzx	24,7,24
	rlwinm	8,16,24,0,7
	lbzx	25,7,25
	rlwinm	9,17,24,0,7
	lbzx	26,7,26
	rlwinm	10,18,24,0,7
	lbzx	27,7,27
	rlwinm	11,19,24,0,7
	lbzx	28,7,28
	rlwimi	8,20,16,8,15
	lbzx	29,7,29
	rlwimi	9,21,16,8,15
	lbzx	30,7,30
	rlwimi	10,22,16,8,15
	lbzx	31,7,31
	rlwimi	11,23,16,8,15
	rlwimi	8,24,8,16,23
	rlwimi	9,25,8,16,23
	rlwimi	10,26,8,16,23
	rlwimi	11,27,8,16,23
	or	8,8,28
	or	9,9,29
	or	10,10,30
	or	11,11,31
	xor	8,8,12
	xor	9,9,0
	xor	10,10,14
	xor	11,11,15
	blr	
.long	0
.byte	0,12,0x14,0,0,0,0,0

.align	4
.Lppc_AES_decrypt_compact:
	lwz	16,240(5)
	addi	6,3,2048
	lwz	12,0(5)
	lis	7,0x8080
	lwz	0,4(5)
	lis	4,0x1b1b
	lwz	14,8(5)
	ori	7,7,0x8080
	lwz	15,12(5)
	ori	4,4,0x1b1b
	addi	5,5,16
	insrdi	7,7,32,0
	insrdi	4,4,32,0
	mtctr	16
.align	4
.Ldec_compact_loop:
	xor	8,8,12
	xor	9,9,0
	rlwinm	16,8,8,24,31
	xor	10,10,14
	rlwinm	17,9,8,24,31
	xor	11,11,15
	rlwinm	18,10,8,24,31
	rlwinm	19,11,8,24,31
	rlwinm	20,11,16,24,31
	rlwinm	21,8,16,24,31
	rlwinm	22,9,16,24,31
	rlwinm	23,10,16,24,31
	lbzx	16,6,16
	rlwinm	24,10,24,24,31
	lbzx	17,6,17
	rlwinm	25,11,24,24,31
	lbzx	18,6,18
	rlwinm	26,8,24,24,31
	lbzx	19,6,19
	rlwinm	27,9,24,24,31
	lbzx	20,6,20
	rlwinm	28,9,0,24,31
	lbzx	21,6,21
	rlwinm	29,10,0,24,31
	lbzx	22,6,22
	rlwinm	30,11,0,24,31
	lbzx	23,6,23
	rlwinm	31,8,0,24,31
	lbzx	24,6,24
	rlwinm	8,16,24,0,7
	lbzx	25,6,25
	rlwinm	9,17,24,0,7
	lbzx	26,6,26
	rlwinm	10,18,24,0,7
	lbzx	27,6,27
	rlwinm	11,19,24,0,7
	lbzx	28,6,28
	rlwimi	8,20,16,8,15
	lbzx	29,6,29
	rlwimi	9,21,16,8,15
	lbzx	30,6,30
	rlwimi	10,22,16,8,15
	lbzx	31,6,31
	rlwimi	11,23,16,8,15
	rlwimi	8,24,8,16,23
	rlwimi	9,25,8,16,23
	rlwimi	10,26,8,16,23
	rlwimi	11,27,8,16,23
	lwz	12,0(5)
	or	8,8,28
	lwz	0,4(5)
	or	9,9,29
	lwz	14,8(5)
	or	10,10,30
	lwz	15,12(5)
	or	11,11,31

	addi	5,5,16
	bdz	.Ldec_compact_done

	insrdi	8,9,32,0
	insrdi	10,11,32,0

	and	16,8,7
	and	18,10,7
	srdi	20,16,7
	srdi	22,18,7
	andc	24,8,7
	andc	26,10,7
	sub	16,16,20
	sub	18,18,22
	add	24,24,24
	add	26,26,26
	and	16,16,4
	and	18,18,4
	xor	16,16,24
	xor	18,18,26

	and	20,16,7
	and	22,18,7
	srdi	24,20,7
	srdi	26,22,7
	andc	28,16,7
	andc	30,18,7
	sub	20,20,24
	sub	22,22,26
	add	28,28,28
	add	30,30,30
	and	20,20,4
	and	22,22,4
	xor	20,20,28
	xor	22,22,30

	and	24,20,7
	and	26,22,7
	srdi	28,24,7
	srdi	30,26,7
	sub	24,24,28
	sub	26,26,30
	andc	28,20,7
	andc	30,22,7
	add	28,28,28
	add	30,30,30
	and	24,24,4
	and	26,26,4
	xor	24,24,28
	xor	26,26,30

	xor	16,16,8
	xor	18,18,10
	xor	20,20,8
	xor	22,22,10

	rldicl	17,16,32,32
	rldicl	19,18,32,32
	rldicl	21,20,32,32
	rldicl	23,22,32,32
	rldicl	25,24,32,32
	rldicl	27,26,32,32
	rotrwi	8,8,8
	rotrwi	9,9,8
	xor	8,8,16
	rotrwi	10,10,8
	xor	9,9,17
	rotrwi	11,11,8
	xor	10,10,18
	xor	11,11,19
	xor	16,16,24
	xor	17,17,25
	xor	18,18,26
	xor	19,19,27
	xor	8,8,20
	rotrwi	16,16,24
	xor	9,9,21
	rotrwi	17,17,24
	xor	10,10,22
	rotrwi	18,18,24
	xor	11,11,23
	rotrwi	19,19,24
	xor	20,20,24
	xor	21,21,25
	xor	22,22,26
	xor	23,23,27
	xor	8,8,24
	rotrwi	20,20,16
	xor	9,9,25
	rotrwi	21,21,16
	xor	10,10,26
	rotrwi	22,22,16
	xor	11,11,27
	rotrwi	23,23,16
	xor	8,8,16
	rotrwi	24,24,8
	xor	9,9,17
	rotrwi	25,25,8
	xor	10,10,18
	rotrwi	26,26,8
	xor	11,11,19
	rotrwi	27,27,8
	xor	8,8,20
	xor	9,9,21
	xor	10,10,22
	xor	11,11,23
	xor	8,8,24
	xor	9,9,25
	xor	10,10,26
	xor	11,11,27

	b	.Ldec_compact_loop
.align	4
.Ldec_compact_done:
	xor	8,8,12
	xor	9,9,0
	xor	10,10,14
	xor	11,11,15
	blr	
.long	0
.byte	0,12,0x14,0,0,0,0,0
.size	AES_decrypt,.-AES_decrypt

.byte	65,69,83,32,102,111,114,32,80,80,67,44,67,82,89,80,84,79,71,65,77,83,32,98,121,32,60,97,112,112,114,111,64,111,112,101,110,115,115,108,46,111,114,103,62,0
.align	2
.align	7
