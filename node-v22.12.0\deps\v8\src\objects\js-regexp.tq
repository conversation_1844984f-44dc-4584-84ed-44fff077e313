// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

bitfield struct JSRegExpFlags extends uint31 {
  global: bool: 1 bit;
  ignore_case: bool: 1 bit;
  multiline: bool: 1 bit;
  sticky: bool: 1 bit;
  unicode: bool: 1 bit;
  dot_all: bool: 1 bit;
  linear: bool: 1 bit;
  has_indices: bool: 1 bit;
  unicode_sets: bool: 1 bit;
}

extern class JSRegExp extends JSObject {
  data: FixedArray|Undefined;
  source: String|Undefined;
  flags: SmiTagged<JSRegExpFlags>|Undefined;
}

// Note: Although a condition for a FastJSRegExp is having a positive smi
// lastIndex (see RegExpBuiltinsAssembler::BranchIfFastRegExp), it is possible
// for this to change without transitioning the transient type. As a precaution,
// validate the lastIndex is positive smi when used in fast paths.
transient type FastJSRegExp extends JSRegExp;

extern operator '.global' macro RegExpBuiltinsAssembler::FastFlagGetterGlobal(
    FastJSRegExp): bool;
extern operator '.unicode' macro RegExpBuiltinsAssembler::FastFlagGetterUnicode(
    FastJSRegExp): bool;
extern operator '.unicodeSets' macro
    RegExpBuiltinsAssembler::FastFlagGetterUnicodeSets(FastJSRegExp): bool;
extern operator '.lastIndex' macro RegExpBuiltinsAssembler::FastLoadLastIndex(
    FastJSRegExp): Smi;
extern operator '.lastIndex=' macro RegExpBuiltinsAssembler::FastStoreLastIndex(
    FastJSRegExp, Smi): void;

@doNotGenerateCast
extern class JSRegExpConstructor extends JSFunction
    generates 'TNode<JSFunction>';

extern shape JSRegExpResult extends JSArray {
  // In-object properties:
  // The below fields are externally exposed.
  index: JSAny;
  input: JSAny;
  groups: JSAny;

  // The below fields are for internal use only.
  names: FixedArray|Undefined;
  regexp_input: String;
  regexp_last_index: Smi;
}

extern shape JSRegExpResultWithIndices extends JSRegExpResult {
  indices: JSAny;
}

extern shape JSRegExpResultIndices extends JSArray {
  // In-object properties:
  // The groups field is externally exposed.
  groups: JSAny;
}
