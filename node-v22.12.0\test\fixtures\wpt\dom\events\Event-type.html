<!DOCTYPE html>
<title>Event.type</title>
<link rel="author" title="Ms2ger" href="mailto:<EMAIL>">
<link rel="help" href="https://dom.spec.whatwg.org/#dom-event-type">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<div id="log"></div>
<script>
test(function() {
  var e = document.createEvent("Event")
  assert_equals(e.type, "");
}, "Event.type should initially be the empty string");
test(function() {
  var e = document.createEvent("Event")
  e.initEvent("foo", false, false)
  assert_equals(e.type, "foo")
}, "Event.type should be initialized by initEvent");
test(function() {
  var e = new Event("bar")
  assert_equals(e.type, "bar")
}, "Event.type should be initialized by the constructor");
</script>
