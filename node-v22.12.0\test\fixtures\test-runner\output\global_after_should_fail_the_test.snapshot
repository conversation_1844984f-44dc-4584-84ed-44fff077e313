this is a test
TAP version 13
# Subtest: this is a test
ok 1 - this is a test
  ---
  duration_ms: *
  ...
not ok 2 - /test/fixtures/test-runner/output/global_after_should_fail_the_test.js
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/global_after_should_fail_the_test.js:(LINE):1'
  failureType: 'hookFailed'
  error: 'this should fail the test'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
    *
    *
  ...
1..1
# tests 1
# suites 0
# pass 1
# fail 0
# cancelled 0
# skipped 0
# todo 0
# duration_ms *
