<!DOCTYPE html>
<html class="test-wait">
<meta charset="utf-8">
<iframe id="iframe"></iframe>
<script>
  const srcdoc = `
    <!DOCTYPE html>
    <meta charset="utf-8">
    <script>
      const xhr = new XMLHttpRequest()
      setTimeout(() => {
        xhr.open('GET', '/', false)
        xhr.send()
        AbortSignal.timeout(41.62684667994843)
      }, 1)
      setTimeout(() => {
        location.href = "about:blank"
        parent.document.documentElement.classList.remove("test-wait")
      }, 0)
    </` + "script>";
  iframe.srcdoc = srcdoc;
</script>
