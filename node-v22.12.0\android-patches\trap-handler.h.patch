--- trap-handler.h	2022-08-11 09:01:23.384000000 +0800
+++ fixed-trap-handler.h	2022-08-11 09:09:15.352000000 +0800
@@ -17,23 +17,7 @@
 namespace internal {
 namespace trap_handler {
 
-// X64 on Linux, Windows, MacOS, FreeBSD.
-#if V8_HOST_ARCH_X64 && V8_TARGET_ARCH_X64 &&                        \
-    ((V8_OS_LINUX && !V8_OS_ANDROID) || V8_OS_WIN || V8_OS_DARWIN || \
-     V8_OS_FREEBSD)
-#define V8_TRAP_HANDLER_SUPPORTED true
-// Arm64 (non-simulator) on Mac.
-#elif V8_TARGET_ARCH_ARM64 && V8_HOST_ARCH_ARM64 && V8_OS_DARWIN
-#define V8_TRAP_HANDLER_SUPPORTED true
-// Arm64 simulator on x64 on Linux, Mac, or Windows.
-#elif V8_TARGET_ARCH_ARM64 && V8_HOST_ARCH_X64 && \
-    (V8_OS_LINUX || V8_OS_DARWIN)
-#define V8_TRAP_HANDLER_VIA_SIMULATOR
-#define V8_TRAP_HANDLER_SUPPORTED true
-// Everything else is unsupported.
-#else
 #define V8_TRAP_HANDLER_SUPPORTED false
-#endif
 
 // Setup for shared library export.
 #if defined(BUILDING_V8_SHARED) && defined(V8_OS_WIN)