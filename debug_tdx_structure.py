#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试通达信数据结构
"""

from pytdx.hq import TdxHq_API

def debug_tdx_structure():
    """调试通达信数据结构"""
    api = TdxHq_API()
    
    try:
        # 连接到服务器
        print("连接到通达信服务器...")
        result = api.connect('sztdx.gtjas.com', 7709)
        
        if result:
            print("\n=== 上海市场分析 ===")
            # 分段查看上海市场的股票
            for start in [0, 1000, 2000, 3000, 4000, 5000, 10000, 15000]:
                stocks = api.get_security_list(0, start)
                if stocks:
                    print(f"\n上海市场 {start}-{start+len(stocks)}:")
                    # 查看前10个
                    for i, stock in enumerate(stocks[:10]):
                        print(f"  {stock['code']} - {stock['name']}")
                    
                    # 统计代码开头
                    code_prefixes = {}
                    for stock in stocks:
                        prefix = stock['code'][:1]
                        code_prefixes[prefix] = code_prefixes.get(prefix, 0) + 1
                    print(f"  代码前缀统计: {code_prefixes}")
                    
                    # 查找6开头的股票
                    six_stocks = [s for s in stocks if s['code'].startswith('6')]
                    if six_stocks:
                        print(f"  6开头股票: {len(six_stocks)}只")
                        for stock in six_stocks[:5]:
                            print(f"    {stock['code']} - {stock['name']}")
                else:
                    print(f"上海市场 {start}: 无数据")
                    break
            
            print("\n=== 深圳市场分析 ===")
            # 分段查看深圳市场的股票
            for start in [0, 1000, 2000, 3000, 4000, 5000, 10000, 15000, 20000]:
                stocks = api.get_security_list(1, start)
                if stocks:
                    print(f"\n深圳市场 {start}-{start+len(stocks)}:")
                    # 查看前10个
                    for i, stock in enumerate(stocks[:10]):
                        print(f"  {stock['code']} - {stock['name']}")

                    # 统计代码开头
                    code_prefixes = {}
                    for stock in stocks:
                        prefix = stock['code'][:3]
                        code_prefixes[prefix] = code_prefixes.get(prefix, 0) + 1
                    print(f"  代码前缀统计: {dict(list(code_prefixes.items())[:10])}")

                    # 查找A股代码
                    a_stocks = [s for s in stocks if s['code'].startswith(('000', '001', '002', '003', '300'))]
                    if a_stocks:
                        print(f"  A股代码: {len(a_stocks)}只")
                        for stock in a_stocks[:5]:
                            print(f"    {stock['code']} - {stock['name']}")
                else:
                    print(f"深圳市场 {start}: 无数据")

            # 特别分析上海市场1000-2000区间的股票分类
            print("\n=== 上海市场1000-2000详细分析 ===")
            stocks = api.get_security_list(0, 1000)
            if stocks:
                # 按代码开头分类
                by_prefix = {}
                for stock in stocks:
                    prefix = stock['code'][:3]
                    if prefix not in by_prefix:
                        by_prefix[prefix] = []
                    by_prefix[prefix].append(stock)

                for prefix, stocks_list in sorted(by_prefix.items()):
                    print(f"\n{prefix}开头股票 ({len(stocks_list)}只):")
                    for stock in stocks_list[:5]:
                        print(f"  {stock['code']} - {stock['name']}")
                    if len(stocks_list) > 5:
                        print(f"  ... 还有{len(stocks_list)-5}只")
        
        api.disconnect()
        
    except Exception as e:
        print(f"调试失败: {str(e)}")
        api.disconnect()

if __name__ == "__main__":
    debug_tdx_structure()
