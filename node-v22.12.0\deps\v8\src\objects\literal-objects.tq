// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@cppObjectDefinition
extern class ObjectBoilerplateDescription extends HeapObject {
  const capacity: Smi;
  backing_store_size: Smi;
  flags: Smi;
  raw_entries[capacity]: Object;
}

extern class ArrayBoilerplateDescription extends Struct {
  flags: Smi;
  constant_elements: FixedArrayBase;
}

extern class RegExpBoilerplateDescription extends Struct {
  data: FixedArray;
  source: String;
  flags: SmiTagged<JSRegExpFlags>;
}

@cppObjectDefinition
extern class ClassBoilerplate extends Struct {
  arguments_count: Smi;
  static_properties_template: Object;
  static_elements_template: Object;
  static_computed_properties: FixedArray;
  instance_properties_template: Object;
  instance_elements_template: Object;
  instance_computed_properties: FixedArray;
}
