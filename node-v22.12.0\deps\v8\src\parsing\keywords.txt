%struct-type
%language=C++
%global-table
%define initializer-suffix ,Token::kIdentifier
%define hash-function-name Hash
%define lookup-function-name GetToken
%define class-name PerfectKeywordHash
%define word-array-name kPerfectKeywordHashTable
%define length-table-name kPerfectKeywordLengthTable
%7bit
%compare-lengths
%enum
%readonly-tables
%compare-strncmp

struct PerfectKeywordHashTableEntry { const char* name; Token::Value value; };
%%
async, Token::kAsync
await, Token::kAwait
break, Token::kBreak
case, Token::kCase
catch, Token::kCatch
class, Token::kClass
const, Token::kConst
continue, Token::kContinue
debugger, Token::kDebugger
default, Token::kDefault
delete, Token::kDelete
do, Token::kDo
else, Token::kElse
enum, Token::kEnum
export, Token::kExport
extends, Token::kExtends
false, Token::kFalseLiteral
finally, Token::kFinally
for, Token::kFor
function, Token::kFunction
get, Token::kGet
if, Token::kIf
implements, Token::kFutureStrictReservedWord
import, Token::kImport
in, Token::kIn
instanceof, Token::kInstanceOf
interface, Token::kFutureStrictReservedWord
let, Token::kLet
new, Token::kNew
null, Token::kNullLiteral
of, Token::kOf
package, Token::kFutureStrictReservedWord
private, Token::kFutureStrictReservedWord
protected, Token::kFutureStrictReservedWord
public, Token::kFutureStrictReservedWord
return, Token::kReturn
set, Token::kSet
static, Token::kStatic
super, Token::kSuper
switch, Token::kSwitch
this, Token::kThis
throw, Token::kThrow
true, Token::kTrueLiteral
try, Token::kTry
typeof, Token::kTypeOf
using, Token::kUsing
var, Token::kVar
void, Token::kVoid
while, Token::kWhile
with, Token::kWith
yield, Token::kYield
