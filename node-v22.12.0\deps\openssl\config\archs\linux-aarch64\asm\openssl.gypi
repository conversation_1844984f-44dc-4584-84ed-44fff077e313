{
  'variables': {
    'openssl_sources': [
      'openssl/ssl/bio_ssl.c',
      'openssl/ssl/d1_lib.c',
      'openssl/ssl/d1_msg.c',
      'openssl/ssl/d1_srtp.c',
      'openssl/ssl/methods.c',
      'openssl/ssl/pqueue.c',
      'openssl/ssl/s3_enc.c',
      'openssl/ssl/s3_lib.c',
      'openssl/ssl/s3_msg.c',
      'openssl/ssl/ssl_asn1.c',
      'openssl/ssl/ssl_cert.c',
      'openssl/ssl/ssl_ciph.c',
      'openssl/ssl/ssl_conf.c',
      'openssl/ssl/ssl_err.c',
      'openssl/ssl/ssl_err_legacy.c',
      'openssl/ssl/ssl_init.c',
      'openssl/ssl/ssl_lib.c',
      'openssl/ssl/ssl_mcnf.c',
      'openssl/ssl/ssl_quic.c',
      'openssl/ssl/ssl_rsa.c',
      'openssl/ssl/ssl_rsa_legacy.c',
      'openssl/ssl/ssl_sess.c',
      'openssl/ssl/ssl_stat.c',
      'openssl/ssl/ssl_txt.c',
      'openssl/ssl/ssl_utst.c',
      'openssl/ssl/t1_enc.c',
      'openssl/ssl/t1_lib.c',
      'openssl/ssl/t1_trce.c',
      'openssl/ssl/tls13_enc.c',
      'openssl/ssl/tls_depr.c',
      'openssl/ssl/tls_srp.c',
      'openssl/ssl/record/dtls1_bitmap.c',
      'openssl/ssl/record/rec_layer_d1.c',
      'openssl/ssl/record/rec_layer_s3.c',
      'openssl/ssl/record/ssl3_buffer.c',
      'openssl/ssl/record/ssl3_record.c',
      'openssl/ssl/record/ssl3_record_tls13.c',
      'openssl/ssl/statem/extensions.c',
      'openssl/ssl/statem/extensions_clnt.c',
      'openssl/ssl/statem/extensions_cust.c',
      'openssl/ssl/statem/extensions_srvr.c',
      'openssl/ssl/statem/statem.c',
      'openssl/ssl/statem/statem_clnt.c',
      'openssl/ssl/statem/statem_dtls.c',
      'openssl/ssl/statem/statem_lib.c',
      'openssl/ssl/statem/statem_quic.c',
      'openssl/ssl/statem/statem_srvr.c',
      'openssl/crypto/aes/aes_cbc.c',
      'openssl/crypto/aes/aes_cfb.c',
      'openssl/crypto/aes/aes_core.c',
      'openssl/crypto/aes/aes_ecb.c',
      'openssl/crypto/aes/aes_ige.c',
      'openssl/crypto/aes/aes_misc.c',
      'openssl/crypto/aes/aes_ofb.c',
      'openssl/crypto/aes/aes_wrap.c',
      'openssl/crypto/aria/aria.c',
      'openssl/crypto/asn1/a_bitstr.c',
      'openssl/crypto/asn1/a_d2i_fp.c',
      'openssl/crypto/asn1/a_digest.c',
      'openssl/crypto/asn1/a_dup.c',
      'openssl/crypto/asn1/a_gentm.c',
      'openssl/crypto/asn1/a_i2d_fp.c',
      'openssl/crypto/asn1/a_int.c',
      'openssl/crypto/asn1/a_mbstr.c',
      'openssl/crypto/asn1/a_object.c',
      'openssl/crypto/asn1/a_octet.c',
      'openssl/crypto/asn1/a_print.c',
      'openssl/crypto/asn1/a_sign.c',
      'openssl/crypto/asn1/a_strex.c',
      'openssl/crypto/asn1/a_strnid.c',
      'openssl/crypto/asn1/a_time.c',
      'openssl/crypto/asn1/a_type.c',
      'openssl/crypto/asn1/a_utctm.c',
      'openssl/crypto/asn1/a_utf8.c',
      'openssl/crypto/asn1/a_verify.c',
      'openssl/crypto/asn1/ameth_lib.c',
      'openssl/crypto/asn1/asn1_err.c',
      'openssl/crypto/asn1/asn1_gen.c',
      'openssl/crypto/asn1/asn1_item_list.c',
      'openssl/crypto/asn1/asn1_lib.c',
      'openssl/crypto/asn1/asn1_parse.c',
      'openssl/crypto/asn1/asn_mime.c',
      'openssl/crypto/asn1/asn_moid.c',
      'openssl/crypto/asn1/asn_mstbl.c',
      'openssl/crypto/asn1/asn_pack.c',
      'openssl/crypto/asn1/bio_asn1.c',
      'openssl/crypto/asn1/bio_ndef.c',
      'openssl/crypto/asn1/d2i_param.c',
      'openssl/crypto/asn1/d2i_pr.c',
      'openssl/crypto/asn1/d2i_pu.c',
      'openssl/crypto/asn1/evp_asn1.c',
      'openssl/crypto/asn1/f_int.c',
      'openssl/crypto/asn1/f_string.c',
      'openssl/crypto/asn1/i2d_evp.c',
      'openssl/crypto/asn1/n_pkey.c',
      'openssl/crypto/asn1/nsseq.c',
      'openssl/crypto/asn1/p5_pbe.c',
      'openssl/crypto/asn1/p5_pbev2.c',
      'openssl/crypto/asn1/p5_scrypt.c',
      'openssl/crypto/asn1/p8_pkey.c',
      'openssl/crypto/asn1/t_bitst.c',
      'openssl/crypto/asn1/t_pkey.c',
      'openssl/crypto/asn1/t_spki.c',
      'openssl/crypto/asn1/tasn_dec.c',
      'openssl/crypto/asn1/tasn_enc.c',
      'openssl/crypto/asn1/tasn_fre.c',
      'openssl/crypto/asn1/tasn_new.c',
      'openssl/crypto/asn1/tasn_prn.c',
      'openssl/crypto/asn1/tasn_scn.c',
      'openssl/crypto/asn1/tasn_typ.c',
      'openssl/crypto/asn1/tasn_utl.c',
      'openssl/crypto/asn1/x_algor.c',
      'openssl/crypto/asn1/x_bignum.c',
      'openssl/crypto/asn1/x_info.c',
      'openssl/crypto/asn1/x_int64.c',
      'openssl/crypto/asn1/x_long.c',
      'openssl/crypto/asn1/x_pkey.c',
      'openssl/crypto/asn1/x_sig.c',
      'openssl/crypto/asn1/x_spki.c',
      'openssl/crypto/asn1/x_val.c',
      'openssl/crypto/async/arch/async_null.c',
      'openssl/crypto/async/arch/async_posix.c',
      'openssl/crypto/async/arch/async_win.c',
      'openssl/crypto/async/async.c',
      'openssl/crypto/async/async_err.c',
      'openssl/crypto/async/async_wait.c',
      'openssl/crypto/bf/bf_cfb64.c',
      'openssl/crypto/bf/bf_ecb.c',
      'openssl/crypto/bf/bf_enc.c',
      'openssl/crypto/bf/bf_ofb64.c',
      'openssl/crypto/bf/bf_skey.c',
      'openssl/crypto/bio/bf_buff.c',
      'openssl/crypto/bio/bf_lbuf.c',
      'openssl/crypto/bio/bf_nbio.c',
      'openssl/crypto/bio/bf_null.c',
      'openssl/crypto/bio/bf_prefix.c',
      'openssl/crypto/bio/bf_readbuff.c',
      'openssl/crypto/bio/bio_addr.c',
      'openssl/crypto/bio/bio_cb.c',
      'openssl/crypto/bio/bio_dump.c',
      'openssl/crypto/bio/bio_err.c',
      'openssl/crypto/bio/bio_lib.c',
      'openssl/crypto/bio/bio_meth.c',
      'openssl/crypto/bio/bio_print.c',
      'openssl/crypto/bio/bio_sock.c',
      'openssl/crypto/bio/bio_sock2.c',
      'openssl/crypto/bio/bss_acpt.c',
      'openssl/crypto/bio/bss_bio.c',
      'openssl/crypto/bio/bss_conn.c',
      'openssl/crypto/bio/bss_core.c',
      'openssl/crypto/bio/bss_dgram.c',
      'openssl/crypto/bio/bss_fd.c',
      'openssl/crypto/bio/bss_file.c',
      'openssl/crypto/bio/bss_log.c',
      'openssl/crypto/bio/bss_mem.c',
      'openssl/crypto/bio/bss_null.c',
      'openssl/crypto/bio/bss_sock.c',
      'openssl/crypto/bio/ossl_core_bio.c',
      'openssl/crypto/bn/bn_add.c',
      'openssl/crypto/bn/bn_asm.c',
      'openssl/crypto/bn/bn_blind.c',
      'openssl/crypto/bn/bn_const.c',
      'openssl/crypto/bn/bn_conv.c',
      'openssl/crypto/bn/bn_ctx.c',
      'openssl/crypto/bn/bn_depr.c',
      'openssl/crypto/bn/bn_dh.c',
      'openssl/crypto/bn/bn_div.c',
      'openssl/crypto/bn/bn_err.c',
      'openssl/crypto/bn/bn_exp.c',
      'openssl/crypto/bn/bn_exp2.c',
      'openssl/crypto/bn/bn_gcd.c',
      'openssl/crypto/bn/bn_gf2m.c',
      'openssl/crypto/bn/bn_intern.c',
      'openssl/crypto/bn/bn_kron.c',
      'openssl/crypto/bn/bn_lib.c',
      'openssl/crypto/bn/bn_mod.c',
      'openssl/crypto/bn/bn_mont.c',
      'openssl/crypto/bn/bn_mpi.c',
      'openssl/crypto/bn/bn_mul.c',
      'openssl/crypto/bn/bn_nist.c',
      'openssl/crypto/bn/bn_prime.c',
      'openssl/crypto/bn/bn_print.c',
      'openssl/crypto/bn/bn_rand.c',
      'openssl/crypto/bn/bn_recp.c',
      'openssl/crypto/bn/bn_rsa_fips186_4.c',
      'openssl/crypto/bn/bn_shift.c',
      'openssl/crypto/bn/bn_sqr.c',
      'openssl/crypto/bn/bn_sqrt.c',
      'openssl/crypto/bn/bn_srp.c',
      'openssl/crypto/bn/bn_word.c',
      'openssl/crypto/bn/bn_x931p.c',
      'openssl/crypto/buffer/buf_err.c',
      'openssl/crypto/buffer/buffer.c',
      'openssl/crypto/camellia/camellia.c',
      'openssl/crypto/camellia/cmll_cbc.c',
      'openssl/crypto/camellia/cmll_cfb.c',
      'openssl/crypto/camellia/cmll_ctr.c',
      'openssl/crypto/camellia/cmll_ecb.c',
      'openssl/crypto/camellia/cmll_misc.c',
      'openssl/crypto/camellia/cmll_ofb.c',
      'openssl/crypto/cast/c_cfb64.c',
      'openssl/crypto/cast/c_ecb.c',
      'openssl/crypto/cast/c_enc.c',
      'openssl/crypto/cast/c_ofb64.c',
      'openssl/crypto/cast/c_skey.c',
      'openssl/crypto/cmac/cmac.c',
      'openssl/crypto/cmp/cmp_asn.c',
      'openssl/crypto/cmp/cmp_client.c',
      'openssl/crypto/cmp/cmp_ctx.c',
      'openssl/crypto/cmp/cmp_err.c',
      'openssl/crypto/cmp/cmp_hdr.c',
      'openssl/crypto/cmp/cmp_http.c',
      'openssl/crypto/cmp/cmp_msg.c',
      'openssl/crypto/cmp/cmp_protect.c',
      'openssl/crypto/cmp/cmp_server.c',
      'openssl/crypto/cmp/cmp_status.c',
      'openssl/crypto/cmp/cmp_util.c',
      'openssl/crypto/cmp/cmp_vfy.c',
      'openssl/crypto/cms/cms_asn1.c',
      'openssl/crypto/cms/cms_att.c',
      'openssl/crypto/cms/cms_cd.c',
      'openssl/crypto/cms/cms_dd.c',
      'openssl/crypto/cms/cms_dh.c',
      'openssl/crypto/cms/cms_ec.c',
      'openssl/crypto/cms/cms_enc.c',
      'openssl/crypto/cms/cms_env.c',
      'openssl/crypto/cms/cms_err.c',
      'openssl/crypto/cms/cms_ess.c',
      'openssl/crypto/cms/cms_io.c',
      'openssl/crypto/cms/cms_kari.c',
      'openssl/crypto/cms/cms_lib.c',
      'openssl/crypto/cms/cms_pwri.c',
      'openssl/crypto/cms/cms_rsa.c',
      'openssl/crypto/cms/cms_sd.c',
      'openssl/crypto/cms/cms_smime.c',
      'openssl/crypto/conf/conf_api.c',
      'openssl/crypto/conf/conf_def.c',
      'openssl/crypto/conf/conf_err.c',
      'openssl/crypto/conf/conf_lib.c',
      'openssl/crypto/conf/conf_mall.c',
      'openssl/crypto/conf/conf_mod.c',
      'openssl/crypto/conf/conf_sap.c',
      'openssl/crypto/conf/conf_ssl.c',
      'openssl/crypto/crmf/crmf_asn.c',
      'openssl/crypto/crmf/crmf_err.c',
      'openssl/crypto/crmf/crmf_lib.c',
      'openssl/crypto/crmf/crmf_pbm.c',
      'openssl/crypto/ct/ct_b64.c',
      'openssl/crypto/ct/ct_err.c',
      'openssl/crypto/ct/ct_log.c',
      'openssl/crypto/ct/ct_oct.c',
      'openssl/crypto/ct/ct_policy.c',
      'openssl/crypto/ct/ct_prn.c',
      'openssl/crypto/ct/ct_sct.c',
      'openssl/crypto/ct/ct_sct_ctx.c',
      'openssl/crypto/ct/ct_vfy.c',
      'openssl/crypto/ct/ct_x509v3.c',
      'openssl/crypto/des/cbc_cksm.c',
      'openssl/crypto/des/cbc_enc.c',
      'openssl/crypto/des/cfb64ede.c',
      'openssl/crypto/des/cfb64enc.c',
      'openssl/crypto/des/cfb_enc.c',
      'openssl/crypto/des/des_enc.c',
      'openssl/crypto/des/ecb3_enc.c',
      'openssl/crypto/des/ecb_enc.c',
      'openssl/crypto/des/fcrypt.c',
      'openssl/crypto/des/fcrypt_b.c',
      'openssl/crypto/des/ofb64ede.c',
      'openssl/crypto/des/ofb64enc.c',
      'openssl/crypto/des/ofb_enc.c',
      'openssl/crypto/des/pcbc_enc.c',
      'openssl/crypto/des/qud_cksm.c',
      'openssl/crypto/des/rand_key.c',
      'openssl/crypto/des/set_key.c',
      'openssl/crypto/des/str2key.c',
      'openssl/crypto/des/xcbc_enc.c',
      'openssl/crypto/dh/dh_ameth.c',
      'openssl/crypto/dh/dh_asn1.c',
      'openssl/crypto/dh/dh_backend.c',
      'openssl/crypto/dh/dh_check.c',
      'openssl/crypto/dh/dh_depr.c',
      'openssl/crypto/dh/dh_err.c',
      'openssl/crypto/dh/dh_gen.c',
      'openssl/crypto/dh/dh_group_params.c',
      'openssl/crypto/dh/dh_kdf.c',
      'openssl/crypto/dh/dh_key.c',
      'openssl/crypto/dh/dh_lib.c',
      'openssl/crypto/dh/dh_meth.c',
      'openssl/crypto/dh/dh_pmeth.c',
      'openssl/crypto/dh/dh_prn.c',
      'openssl/crypto/dh/dh_rfc5114.c',
      'openssl/crypto/dsa/dsa_ameth.c',
      'openssl/crypto/dsa/dsa_asn1.c',
      'openssl/crypto/dsa/dsa_backend.c',
      'openssl/crypto/dsa/dsa_check.c',
      'openssl/crypto/dsa/dsa_depr.c',
      'openssl/crypto/dsa/dsa_err.c',
      'openssl/crypto/dsa/dsa_gen.c',
      'openssl/crypto/dsa/dsa_key.c',
      'openssl/crypto/dsa/dsa_lib.c',
      'openssl/crypto/dsa/dsa_meth.c',
      'openssl/crypto/dsa/dsa_ossl.c',
      'openssl/crypto/dsa/dsa_pmeth.c',
      'openssl/crypto/dsa/dsa_prn.c',
      'openssl/crypto/dsa/dsa_sign.c',
      'openssl/crypto/dsa/dsa_vrf.c',
      'openssl/crypto/dso/dso_dl.c',
      'openssl/crypto/dso/dso_dlfcn.c',
      'openssl/crypto/dso/dso_err.c',
      'openssl/crypto/dso/dso_lib.c',
      'openssl/crypto/dso/dso_openssl.c',
      'openssl/crypto/dso/dso_vms.c',
      'openssl/crypto/dso/dso_win32.c',
      'openssl/crypto/ec/curve448/arch_32/f_impl32.c',
      'openssl/crypto/ec/curve448/arch_64/f_impl64.c',
      'openssl/crypto/ec/curve448/curve448.c',
      'openssl/crypto/ec/curve448/curve448_tables.c',
      'openssl/crypto/ec/curve448/eddsa.c',
      'openssl/crypto/ec/curve448/f_generic.c',
      'openssl/crypto/ec/curve448/scalar.c',
      'openssl/crypto/ec/curve25519.c',
      'openssl/crypto/ec/ec2_oct.c',
      'openssl/crypto/ec/ec2_smpl.c',
      'openssl/crypto/ec/ec_ameth.c',
      'openssl/crypto/ec/ec_asn1.c',
      'openssl/crypto/ec/ec_backend.c',
      'openssl/crypto/ec/ec_check.c',
      'openssl/crypto/ec/ec_curve.c',
      'openssl/crypto/ec/ec_cvt.c',
      'openssl/crypto/ec/ec_deprecated.c',
      'openssl/crypto/ec/ec_err.c',
      'openssl/crypto/ec/ec_key.c',
      'openssl/crypto/ec/ec_kmeth.c',
      'openssl/crypto/ec/ec_lib.c',
      'openssl/crypto/ec/ec_mult.c',
      'openssl/crypto/ec/ec_oct.c',
      'openssl/crypto/ec/ec_pmeth.c',
      'openssl/crypto/ec/ec_print.c',
      'openssl/crypto/ec/ecdh_kdf.c',
      'openssl/crypto/ec/ecdh_ossl.c',
      'openssl/crypto/ec/ecdsa_ossl.c',
      'openssl/crypto/ec/ecdsa_sign.c',
      'openssl/crypto/ec/ecdsa_vrf.c',
      'openssl/crypto/ec/eck_prn.c',
      'openssl/crypto/ec/ecp_mont.c',
      'openssl/crypto/ec/ecp_nist.c',
      'openssl/crypto/ec/ecp_nistz256.c',
      'openssl/crypto/ec/ecp_oct.c',
      'openssl/crypto/ec/ecp_smpl.c',
      'openssl/crypto/ec/ecx_backend.c',
      'openssl/crypto/ec/ecx_key.c',
      'openssl/crypto/ec/ecx_meth.c',
      'openssl/crypto/encode_decode/decoder_err.c',
      'openssl/crypto/encode_decode/decoder_lib.c',
      'openssl/crypto/encode_decode/decoder_meth.c',
      'openssl/crypto/encode_decode/decoder_pkey.c',
      'openssl/crypto/encode_decode/encoder_err.c',
      'openssl/crypto/encode_decode/encoder_lib.c',
      'openssl/crypto/encode_decode/encoder_meth.c',
      'openssl/crypto/encode_decode/encoder_pkey.c',
      'openssl/crypto/engine/eng_all.c',
      'openssl/crypto/engine/eng_cnf.c',
      'openssl/crypto/engine/eng_ctrl.c',
      'openssl/crypto/engine/eng_dyn.c',
      'openssl/crypto/engine/eng_err.c',
      'openssl/crypto/engine/eng_fat.c',
      'openssl/crypto/engine/eng_init.c',
      'openssl/crypto/engine/eng_lib.c',
      'openssl/crypto/engine/eng_list.c',
      'openssl/crypto/engine/eng_openssl.c',
      'openssl/crypto/engine/eng_pkey.c',
      'openssl/crypto/engine/eng_rdrand.c',
      'openssl/crypto/engine/eng_table.c',
      'openssl/crypto/engine/tb_asnmth.c',
      'openssl/crypto/engine/tb_cipher.c',
      'openssl/crypto/engine/tb_dh.c',
      'openssl/crypto/engine/tb_digest.c',
      'openssl/crypto/engine/tb_dsa.c',
      'openssl/crypto/engine/tb_eckey.c',
      'openssl/crypto/engine/tb_pkmeth.c',
      'openssl/crypto/engine/tb_rand.c',
      'openssl/crypto/engine/tb_rsa.c',
      'openssl/crypto/err/err.c',
      'openssl/crypto/err/err_all.c',
      'openssl/crypto/err/err_all_legacy.c',
      'openssl/crypto/err/err_blocks.c',
      'openssl/crypto/err/err_prn.c',
      'openssl/crypto/ess/ess_asn1.c',
      'openssl/crypto/ess/ess_err.c',
      'openssl/crypto/ess/ess_lib.c',
      'openssl/crypto/evp/asymcipher.c',
      'openssl/crypto/evp/bio_b64.c',
      'openssl/crypto/evp/bio_enc.c',
      'openssl/crypto/evp/bio_md.c',
      'openssl/crypto/evp/bio_ok.c',
      'openssl/crypto/evp/c_allc.c',
      'openssl/crypto/evp/c_alld.c',
      'openssl/crypto/evp/cmeth_lib.c',
      'openssl/crypto/evp/ctrl_params_translate.c',
      'openssl/crypto/evp/dh_ctrl.c',
      'openssl/crypto/evp/dh_support.c',
      'openssl/crypto/evp/digest.c',
      'openssl/crypto/evp/dsa_ctrl.c',
      'openssl/crypto/evp/e_aes.c',
      'openssl/crypto/evp/e_aes_cbc_hmac_sha1.c',
      'openssl/crypto/evp/e_aes_cbc_hmac_sha256.c',
      'openssl/crypto/evp/e_aria.c',
      'openssl/crypto/evp/e_bf.c',
      'openssl/crypto/evp/e_camellia.c',
      'openssl/crypto/evp/e_cast.c',
      'openssl/crypto/evp/e_chacha20_poly1305.c',
      'openssl/crypto/evp/e_des.c',
      'openssl/crypto/evp/e_des3.c',
      'openssl/crypto/evp/e_idea.c',
      'openssl/crypto/evp/e_null.c',
      'openssl/crypto/evp/e_old.c',
      'openssl/crypto/evp/e_rc2.c',
      'openssl/crypto/evp/e_rc4.c',
      'openssl/crypto/evp/e_rc4_hmac_md5.c',
      'openssl/crypto/evp/e_rc5.c',
      'openssl/crypto/evp/e_seed.c',
      'openssl/crypto/evp/e_sm4.c',
      'openssl/crypto/evp/e_xcbc_d.c',
      'openssl/crypto/evp/ec_ctrl.c',
      'openssl/crypto/evp/ec_support.c',
      'openssl/crypto/evp/encode.c',
      'openssl/crypto/evp/evp_cnf.c',
      'openssl/crypto/evp/evp_enc.c',
      'openssl/crypto/evp/evp_err.c',
      'openssl/crypto/evp/evp_fetch.c',
      'openssl/crypto/evp/evp_key.c',
      'openssl/crypto/evp/evp_lib.c',
      'openssl/crypto/evp/evp_pbe.c',
      'openssl/crypto/evp/evp_pkey.c',
      'openssl/crypto/evp/evp_rand.c',
      'openssl/crypto/evp/evp_utils.c',
      'openssl/crypto/evp/exchange.c',
      'openssl/crypto/evp/kdf_lib.c',
      'openssl/crypto/evp/kdf_meth.c',
      'openssl/crypto/evp/kem.c',
      'openssl/crypto/evp/keymgmt_lib.c',
      'openssl/crypto/evp/keymgmt_meth.c',
      'openssl/crypto/evp/legacy_blake2.c',
      'openssl/crypto/evp/legacy_md4.c',
      'openssl/crypto/evp/legacy_md5.c',
      'openssl/crypto/evp/legacy_md5_sha1.c',
      'openssl/crypto/evp/legacy_mdc2.c',
      'openssl/crypto/evp/legacy_ripemd.c',
      'openssl/crypto/evp/legacy_sha.c',
      'openssl/crypto/evp/legacy_wp.c',
      'openssl/crypto/evp/m_null.c',
      'openssl/crypto/evp/m_sigver.c',
      'openssl/crypto/evp/mac_lib.c',
      'openssl/crypto/evp/mac_meth.c',
      'openssl/crypto/evp/names.c',
      'openssl/crypto/evp/p5_crpt.c',
      'openssl/crypto/evp/p5_crpt2.c',
      'openssl/crypto/evp/p_dec.c',
      'openssl/crypto/evp/p_enc.c',
      'openssl/crypto/evp/p_legacy.c',
      'openssl/crypto/evp/p_lib.c',
      'openssl/crypto/evp/p_open.c',
      'openssl/crypto/evp/p_seal.c',
      'openssl/crypto/evp/p_sign.c',
      'openssl/crypto/evp/p_verify.c',
      'openssl/crypto/evp/pbe_scrypt.c',
      'openssl/crypto/evp/pmeth_check.c',
      'openssl/crypto/evp/pmeth_gn.c',
      'openssl/crypto/evp/pmeth_lib.c',
      'openssl/crypto/evp/signature.c',
      'openssl/crypto/ffc/ffc_backend.c',
      'openssl/crypto/ffc/ffc_dh.c',
      'openssl/crypto/ffc/ffc_key_generate.c',
      'openssl/crypto/ffc/ffc_key_validate.c',
      'openssl/crypto/ffc/ffc_params.c',
      'openssl/crypto/ffc/ffc_params_generate.c',
      'openssl/crypto/ffc/ffc_params_validate.c',
      'openssl/crypto/hmac/hmac.c',
      'openssl/crypto/http/http_client.c',
      'openssl/crypto/http/http_err.c',
      'openssl/crypto/http/http_lib.c',
      'openssl/crypto/idea/i_cbc.c',
      'openssl/crypto/idea/i_cfb64.c',
      'openssl/crypto/idea/i_ecb.c',
      'openssl/crypto/idea/i_ofb64.c',
      'openssl/crypto/idea/i_skey.c',
      'openssl/crypto/kdf/kdf_err.c',
      'openssl/crypto/lhash/lh_stats.c',
      'openssl/crypto/lhash/lhash.c',
      'openssl/crypto/armcap.c',
      'openssl/crypto/asn1_dsa.c',
      'openssl/crypto/bsearch.c',
      'openssl/crypto/context.c',
      'openssl/crypto/core_algorithm.c',
      'openssl/crypto/core_fetch.c',
      'openssl/crypto/core_namemap.c',
      'openssl/crypto/cpt_err.c',
      'openssl/crypto/cpuid.c',
      'openssl/crypto/cryptlib.c',
      'openssl/crypto/ctype.c',
      'openssl/crypto/cversion.c',
      'openssl/crypto/der_writer.c',
      'openssl/crypto/ebcdic.c',
      'openssl/crypto/ex_data.c',
      'openssl/crypto/getenv.c',
      'openssl/crypto/info.c',
      'openssl/crypto/init.c',
      'openssl/crypto/initthread.c',
      'openssl/crypto/mem.c',
      'openssl/crypto/mem_sec.c',
      'openssl/crypto/o_dir.c',
      'openssl/crypto/o_fopen.c',
      'openssl/crypto/o_init.c',
      'openssl/crypto/o_str.c',
      'openssl/crypto/o_time.c',
      'openssl/crypto/packet.c',
      'openssl/crypto/param_build.c',
      'openssl/crypto/param_build_set.c',
      'openssl/crypto/params.c',
      'openssl/crypto/params_dup.c',
      'openssl/crypto/params_from_text.c',
      'openssl/crypto/passphrase.c',
      'openssl/crypto/provider.c',
      'openssl/crypto/provider_child.c',
      'openssl/crypto/provider_conf.c',
      'openssl/crypto/provider_core.c',
      'openssl/crypto/provider_predefined.c',
      'openssl/crypto/punycode.c',
      'openssl/crypto/self_test_core.c',
      'openssl/crypto/sparse_array.c',
      'openssl/crypto/threads_lib.c',
      'openssl/crypto/threads_none.c',
      'openssl/crypto/threads_pthread.c',
      'openssl/crypto/threads_win.c',
      'openssl/crypto/trace.c',
      'openssl/crypto/uid.c',
      'openssl/crypto/md4/md4_dgst.c',
      'openssl/crypto/md4/md4_one.c',
      'openssl/crypto/md5/md5_dgst.c',
      'openssl/crypto/md5/md5_one.c',
      'openssl/crypto/md5/md5_sha1.c',
      'openssl/crypto/mdc2/mdc2_one.c',
      'openssl/crypto/mdc2/mdc2dgst.c',
      'openssl/crypto/modes/cbc128.c',
      'openssl/crypto/modes/ccm128.c',
      'openssl/crypto/modes/cfb128.c',
      'openssl/crypto/modes/ctr128.c',
      'openssl/crypto/modes/cts128.c',
      'openssl/crypto/modes/gcm128.c',
      'openssl/crypto/modes/ocb128.c',
      'openssl/crypto/modes/ofb128.c',
      'openssl/crypto/modes/siv128.c',
      'openssl/crypto/modes/wrap128.c',
      'openssl/crypto/modes/xts128.c',
      'openssl/crypto/objects/o_names.c',
      'openssl/crypto/objects/obj_dat.c',
      'openssl/crypto/objects/obj_err.c',
      'openssl/crypto/objects/obj_lib.c',
      'openssl/crypto/objects/obj_xref.c',
      'openssl/crypto/ocsp/ocsp_asn.c',
      'openssl/crypto/ocsp/ocsp_cl.c',
      'openssl/crypto/ocsp/ocsp_err.c',
      'openssl/crypto/ocsp/ocsp_ext.c',
      'openssl/crypto/ocsp/ocsp_http.c',
      'openssl/crypto/ocsp/ocsp_lib.c',
      'openssl/crypto/ocsp/ocsp_prn.c',
      'openssl/crypto/ocsp/ocsp_srv.c',
      'openssl/crypto/ocsp/ocsp_vfy.c',
      'openssl/crypto/ocsp/v3_ocsp.c',
      'openssl/crypto/pem/pem_all.c',
      'openssl/crypto/pem/pem_err.c',
      'openssl/crypto/pem/pem_info.c',
      'openssl/crypto/pem/pem_lib.c',
      'openssl/crypto/pem/pem_oth.c',
      'openssl/crypto/pem/pem_pk8.c',
      'openssl/crypto/pem/pem_pkey.c',
      'openssl/crypto/pem/pem_sign.c',
      'openssl/crypto/pem/pem_x509.c',
      'openssl/crypto/pem/pem_xaux.c',
      'openssl/crypto/pem/pvkfmt.c',
      'openssl/crypto/pkcs12/p12_add.c',
      'openssl/crypto/pkcs12/p12_asn.c',
      'openssl/crypto/pkcs12/p12_attr.c',
      'openssl/crypto/pkcs12/p12_crpt.c',
      'openssl/crypto/pkcs12/p12_crt.c',
      'openssl/crypto/pkcs12/p12_decr.c',
      'openssl/crypto/pkcs12/p12_init.c',
      'openssl/crypto/pkcs12/p12_key.c',
      'openssl/crypto/pkcs12/p12_kiss.c',
      'openssl/crypto/pkcs12/p12_mutl.c',
      'openssl/crypto/pkcs12/p12_npas.c',
      'openssl/crypto/pkcs12/p12_p8d.c',
      'openssl/crypto/pkcs12/p12_p8e.c',
      'openssl/crypto/pkcs12/p12_sbag.c',
      'openssl/crypto/pkcs12/p12_utl.c',
      'openssl/crypto/pkcs12/pk12err.c',
      'openssl/crypto/pkcs7/bio_pk7.c',
      'openssl/crypto/pkcs7/pk7_asn1.c',
      'openssl/crypto/pkcs7/pk7_attr.c',
      'openssl/crypto/pkcs7/pk7_doit.c',
      'openssl/crypto/pkcs7/pk7_lib.c',
      'openssl/crypto/pkcs7/pk7_mime.c',
      'openssl/crypto/pkcs7/pk7_smime.c',
      'openssl/crypto/pkcs7/pkcs7err.c',
      'openssl/crypto/poly1305/poly1305.c',
      'openssl/crypto/property/defn_cache.c',
      'openssl/crypto/property/property.c',
      'openssl/crypto/property/property_err.c',
      'openssl/crypto/property/property_parse.c',
      'openssl/crypto/property/property_query.c',
      'openssl/crypto/property/property_string.c',
      'openssl/crypto/rand/prov_seed.c',
      'openssl/crypto/rand/rand_deprecated.c',
      'openssl/crypto/rand/rand_err.c',
      'openssl/crypto/rand/rand_lib.c',
      'openssl/crypto/rand/rand_meth.c',
      'openssl/crypto/rand/rand_pool.c',
      'openssl/crypto/rand/randfile.c',
      'openssl/crypto/rc2/rc2_cbc.c',
      'openssl/crypto/rc2/rc2_ecb.c',
      'openssl/crypto/rc2/rc2_skey.c',
      'openssl/crypto/rc2/rc2cfb64.c',
      'openssl/crypto/rc2/rc2ofb64.c',
      'openssl/crypto/rc4/rc4_enc.c',
      'openssl/crypto/rc4/rc4_skey.c',
      'openssl/crypto/ripemd/rmd_dgst.c',
      'openssl/crypto/ripemd/rmd_one.c',
      'openssl/crypto/rsa/rsa_ameth.c',
      'openssl/crypto/rsa/rsa_asn1.c',
      'openssl/crypto/rsa/rsa_backend.c',
      'openssl/crypto/rsa/rsa_chk.c',
      'openssl/crypto/rsa/rsa_crpt.c',
      'openssl/crypto/rsa/rsa_depr.c',
      'openssl/crypto/rsa/rsa_err.c',
      'openssl/crypto/rsa/rsa_gen.c',
      'openssl/crypto/rsa/rsa_lib.c',
      'openssl/crypto/rsa/rsa_meth.c',
      'openssl/crypto/rsa/rsa_mp.c',
      'openssl/crypto/rsa/rsa_mp_names.c',
      'openssl/crypto/rsa/rsa_none.c',
      'openssl/crypto/rsa/rsa_oaep.c',
      'openssl/crypto/rsa/rsa_ossl.c',
      'openssl/crypto/rsa/rsa_pk1.c',
      'openssl/crypto/rsa/rsa_pmeth.c',
      'openssl/crypto/rsa/rsa_prn.c',
      'openssl/crypto/rsa/rsa_pss.c',
      'openssl/crypto/rsa/rsa_saos.c',
      'openssl/crypto/rsa/rsa_schemes.c',
      'openssl/crypto/rsa/rsa_sign.c',
      'openssl/crypto/rsa/rsa_sp800_56b_check.c',
      'openssl/crypto/rsa/rsa_sp800_56b_gen.c',
      'openssl/crypto/rsa/rsa_x931.c',
      'openssl/crypto/rsa/rsa_x931g.c',
      'openssl/crypto/seed/seed.c',
      'openssl/crypto/seed/seed_cbc.c',
      'openssl/crypto/seed/seed_cfb.c',
      'openssl/crypto/seed/seed_ecb.c',
      'openssl/crypto/seed/seed_ofb.c',
      'openssl/crypto/sha/sha1_one.c',
      'openssl/crypto/sha/sha1dgst.c',
      'openssl/crypto/sha/sha256.c',
      'openssl/crypto/sha/sha3.c',
      'openssl/crypto/sha/sha512.c',
      'openssl/crypto/siphash/siphash.c',
      'openssl/crypto/sm2/sm2_crypt.c',
      'openssl/crypto/sm2/sm2_err.c',
      'openssl/crypto/sm2/sm2_key.c',
      'openssl/crypto/sm2/sm2_sign.c',
      'openssl/crypto/sm3/legacy_sm3.c',
      'openssl/crypto/sm3/sm3.c',
      'openssl/crypto/sm4/sm4.c',
      'openssl/crypto/srp/srp_lib.c',
      'openssl/crypto/srp/srp_vfy.c',
      'openssl/crypto/stack/stack.c',
      'openssl/crypto/store/store_err.c',
      'openssl/crypto/store/store_init.c',
      'openssl/crypto/store/store_lib.c',
      'openssl/crypto/store/store_meth.c',
      'openssl/crypto/store/store_register.c',
      'openssl/crypto/store/store_result.c',
      'openssl/crypto/store/store_strings.c',
      'openssl/crypto/ts/ts_asn1.c',
      'openssl/crypto/ts/ts_conf.c',
      'openssl/crypto/ts/ts_err.c',
      'openssl/crypto/ts/ts_lib.c',
      'openssl/crypto/ts/ts_req_print.c',
      'openssl/crypto/ts/ts_req_utils.c',
      'openssl/crypto/ts/ts_rsp_print.c',
      'openssl/crypto/ts/ts_rsp_sign.c',
      'openssl/crypto/ts/ts_rsp_utils.c',
      'openssl/crypto/ts/ts_rsp_verify.c',
      'openssl/crypto/ts/ts_verify_ctx.c',
      'openssl/crypto/txt_db/txt_db.c',
      'openssl/crypto/ui/ui_err.c',
      'openssl/crypto/ui/ui_lib.c',
      'openssl/crypto/ui/ui_null.c',
      'openssl/crypto/ui/ui_openssl.c',
      'openssl/crypto/ui/ui_util.c',
      'openssl/crypto/whrlpool/wp_block.c',
      'openssl/crypto/whrlpool/wp_dgst.c',
      'openssl/crypto/x509/by_dir.c',
      'openssl/crypto/x509/by_file.c',
      'openssl/crypto/x509/by_store.c',
      'openssl/crypto/x509/pcy_cache.c',
      'openssl/crypto/x509/pcy_data.c',
      'openssl/crypto/x509/pcy_lib.c',
      'openssl/crypto/x509/pcy_map.c',
      'openssl/crypto/x509/pcy_node.c',
      'openssl/crypto/x509/pcy_tree.c',
      'openssl/crypto/x509/t_crl.c',
      'openssl/crypto/x509/t_req.c',
      'openssl/crypto/x509/t_x509.c',
      'openssl/crypto/x509/v3_addr.c',
      'openssl/crypto/x509/v3_admis.c',
      'openssl/crypto/x509/v3_akeya.c',
      'openssl/crypto/x509/v3_akid.c',
      'openssl/crypto/x509/v3_asid.c',
      'openssl/crypto/x509/v3_bcons.c',
      'openssl/crypto/x509/v3_bitst.c',
      'openssl/crypto/x509/v3_conf.c',
      'openssl/crypto/x509/v3_cpols.c',
      'openssl/crypto/x509/v3_crld.c',
      'openssl/crypto/x509/v3_enum.c',
      'openssl/crypto/x509/v3_extku.c',
      'openssl/crypto/x509/v3_genn.c',
      'openssl/crypto/x509/v3_ia5.c',
      'openssl/crypto/x509/v3_info.c',
      'openssl/crypto/x509/v3_int.c',
      'openssl/crypto/x509/v3_ist.c',
      'openssl/crypto/x509/v3_lib.c',
      'openssl/crypto/x509/v3_ncons.c',
      'openssl/crypto/x509/v3_pci.c',
      'openssl/crypto/x509/v3_pcia.c',
      'openssl/crypto/x509/v3_pcons.c',
      'openssl/crypto/x509/v3_pku.c',
      'openssl/crypto/x509/v3_pmaps.c',
      'openssl/crypto/x509/v3_prn.c',
      'openssl/crypto/x509/v3_purp.c',
      'openssl/crypto/x509/v3_san.c',
      'openssl/crypto/x509/v3_skid.c',
      'openssl/crypto/x509/v3_sxnet.c',
      'openssl/crypto/x509/v3_tlsf.c',
      'openssl/crypto/x509/v3_utf8.c',
      'openssl/crypto/x509/v3_utl.c',
      'openssl/crypto/x509/v3err.c',
      'openssl/crypto/x509/x509_att.c',
      'openssl/crypto/x509/x509_cmp.c',
      'openssl/crypto/x509/x509_d2.c',
      'openssl/crypto/x509/x509_def.c',
      'openssl/crypto/x509/x509_err.c',
      'openssl/crypto/x509/x509_ext.c',
      'openssl/crypto/x509/x509_lu.c',
      'openssl/crypto/x509/x509_meth.c',
      'openssl/crypto/x509/x509_obj.c',
      'openssl/crypto/x509/x509_r2x.c',
      'openssl/crypto/x509/x509_req.c',
      'openssl/crypto/x509/x509_set.c',
      'openssl/crypto/x509/x509_trust.c',
      'openssl/crypto/x509/x509_txt.c',
      'openssl/crypto/x509/x509_v3.c',
      'openssl/crypto/x509/x509_vfy.c',
      'openssl/crypto/x509/x509_vpm.c',
      'openssl/crypto/x509/x509cset.c',
      'openssl/crypto/x509/x509name.c',
      'openssl/crypto/x509/x509rset.c',
      'openssl/crypto/x509/x509spki.c',
      'openssl/crypto/x509/x509type.c',
      'openssl/crypto/x509/x_all.c',
      'openssl/crypto/x509/x_attrib.c',
      'openssl/crypto/x509/x_crl.c',
      'openssl/crypto/x509/x_exten.c',
      'openssl/crypto/x509/x_name.c',
      'openssl/crypto/x509/x_pubkey.c',
      'openssl/crypto/x509/x_req.c',
      'openssl/crypto/x509/x_x509.c',
      'openssl/crypto/x509/x_x509a.c',
      'openssl/engines/e_capi.c',
      'openssl/engines/e_padlock.c',
      'openssl/providers/baseprov.c',
      'openssl/providers/defltprov.c',
      'openssl/providers/nullprov.c',
      'openssl/providers/prov_running.c',
      'openssl/providers/common/der/der_rsa_sig.c',
      'openssl/providers/common/der/der_sm2_key.c',
      'openssl/providers/common/der/der_sm2_sig.c',
      'openssl/providers/common/bio_prov.c',
      'openssl/providers/common/capabilities.c',
      'openssl/providers/common/digest_to_nid.c',
      'openssl/providers/common/provider_seeding.c',
      'openssl/providers/common/provider_util.c',
      'openssl/providers/common/securitycheck.c',
      'openssl/providers/common/securitycheck_default.c',
      'openssl/providers/implementations/asymciphers/rsa_enc.c',
      'openssl/providers/implementations/asymciphers/sm2_enc.c',
      'openssl/providers/implementations/ciphers/cipher_aes.c',
      'openssl/providers/implementations/ciphers/cipher_aes_cbc_hmac_sha.c',
      'openssl/providers/implementations/ciphers/cipher_aes_cbc_hmac_sha1_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aes_cbc_hmac_sha256_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aes_ccm.c',
      'openssl/providers/implementations/ciphers/cipher_aes_ccm_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aes_gcm.c',
      'openssl/providers/implementations/ciphers/cipher_aes_gcm_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aes_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aes_ocb.c',
      'openssl/providers/implementations/ciphers/cipher_aes_ocb_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aes_siv.c',
      'openssl/providers/implementations/ciphers/cipher_aes_siv_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aes_wrp.c',
      'openssl/providers/implementations/ciphers/cipher_aes_xts.c',
      'openssl/providers/implementations/ciphers/cipher_aes_xts_fips.c',
      'openssl/providers/implementations/ciphers/cipher_aes_xts_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aria.c',
      'openssl/providers/implementations/ciphers/cipher_aria_ccm.c',
      'openssl/providers/implementations/ciphers/cipher_aria_ccm_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aria_gcm.c',
      'openssl/providers/implementations/ciphers/cipher_aria_gcm_hw.c',
      'openssl/providers/implementations/ciphers/cipher_aria_hw.c',
      'openssl/providers/implementations/ciphers/cipher_camellia.c',
      'openssl/providers/implementations/ciphers/cipher_camellia_hw.c',
      'openssl/providers/implementations/ciphers/cipher_chacha20.c',
      'openssl/providers/implementations/ciphers/cipher_chacha20_hw.c',
      'openssl/providers/implementations/ciphers/cipher_chacha20_poly1305.c',
      'openssl/providers/implementations/ciphers/cipher_chacha20_poly1305_hw.c',
      'openssl/providers/implementations/ciphers/cipher_cts.c',
      'openssl/providers/implementations/ciphers/cipher_null.c',
      'openssl/providers/implementations/ciphers/cipher_sm4.c',
      'openssl/providers/implementations/ciphers/cipher_sm4_hw.c',
      'openssl/providers/implementations/ciphers/cipher_tdes.c',
      'openssl/providers/implementations/ciphers/cipher_tdes_common.c',
      'openssl/providers/implementations/ciphers/cipher_tdes_default.c',
      'openssl/providers/implementations/ciphers/cipher_tdes_default_hw.c',
      'openssl/providers/implementations/ciphers/cipher_tdes_hw.c',
      'openssl/providers/implementations/ciphers/cipher_tdes_wrap.c',
      'openssl/providers/implementations/ciphers/cipher_tdes_wrap_hw.c',
      'openssl/providers/implementations/digests/blake2_prov.c',
      'openssl/providers/implementations/digests/blake2b_prov.c',
      'openssl/providers/implementations/digests/blake2s_prov.c',
      'openssl/providers/implementations/digests/md5_prov.c',
      'openssl/providers/implementations/digests/md5_sha1_prov.c',
      'openssl/providers/implementations/digests/null_prov.c',
      'openssl/providers/implementations/digests/ripemd_prov.c',
      'openssl/providers/implementations/digests/sha2_prov.c',
      'openssl/providers/implementations/digests/sha3_prov.c',
      'openssl/providers/implementations/digests/sm3_prov.c',
      'openssl/providers/implementations/encode_decode/decode_der2key.c',
      'openssl/providers/implementations/encode_decode/decode_epki2pki.c',
      'openssl/providers/implementations/encode_decode/decode_msblob2key.c',
      'openssl/providers/implementations/encode_decode/decode_pem2der.c',
      'openssl/providers/implementations/encode_decode/decode_pvk2key.c',
      'openssl/providers/implementations/encode_decode/decode_spki2typespki.c',
      'openssl/providers/implementations/encode_decode/encode_key2any.c',
      'openssl/providers/implementations/encode_decode/encode_key2blob.c',
      'openssl/providers/implementations/encode_decode/encode_key2ms.c',
      'openssl/providers/implementations/encode_decode/encode_key2text.c',
      'openssl/providers/implementations/encode_decode/endecoder_common.c',
      'openssl/providers/implementations/exchange/dh_exch.c',
      'openssl/providers/implementations/exchange/ecdh_exch.c',
      'openssl/providers/implementations/exchange/ecx_exch.c',
      'openssl/providers/implementations/exchange/kdf_exch.c',
      'openssl/providers/implementations/kdfs/hkdf.c',
      'openssl/providers/implementations/kdfs/kbkdf.c',
      'openssl/providers/implementations/kdfs/krb5kdf.c',
      'openssl/providers/implementations/kdfs/pbkdf2.c',
      'openssl/providers/implementations/kdfs/pbkdf2_fips.c',
      'openssl/providers/implementations/kdfs/pkcs12kdf.c',
      'openssl/providers/implementations/kdfs/scrypt.c',
      'openssl/providers/implementations/kdfs/sshkdf.c',
      'openssl/providers/implementations/kdfs/sskdf.c',
      'openssl/providers/implementations/kdfs/tls1_prf.c',
      'openssl/providers/implementations/kdfs/x942kdf.c',
      'openssl/providers/implementations/kem/rsa_kem.c',
      'openssl/providers/implementations/keymgmt/dh_kmgmt.c',
      'openssl/providers/implementations/keymgmt/dsa_kmgmt.c',
      'openssl/providers/implementations/keymgmt/ec_kmgmt.c',
      'openssl/providers/implementations/keymgmt/ecx_kmgmt.c',
      'openssl/providers/implementations/keymgmt/kdf_legacy_kmgmt.c',
      'openssl/providers/implementations/keymgmt/mac_legacy_kmgmt.c',
      'openssl/providers/implementations/keymgmt/rsa_kmgmt.c',
      'openssl/providers/implementations/macs/blake2b_mac.c',
      'openssl/providers/implementations/macs/blake2s_mac.c',
      'openssl/providers/implementations/macs/cmac_prov.c',
      'openssl/providers/implementations/macs/gmac_prov.c',
      'openssl/providers/implementations/macs/hmac_prov.c',
      'openssl/providers/implementations/macs/kmac_prov.c',
      'openssl/providers/implementations/macs/poly1305_prov.c',
      'openssl/providers/implementations/macs/siphash_prov.c',
      'openssl/providers/implementations/rands/crngt.c',
      'openssl/providers/implementations/rands/drbg.c',
      'openssl/providers/implementations/rands/drbg_ctr.c',
      'openssl/providers/implementations/rands/drbg_hash.c',
      'openssl/providers/implementations/rands/drbg_hmac.c',
      'openssl/providers/implementations/rands/seed_src.c',
      'openssl/providers/implementations/rands/test_rng.c',
      'openssl/providers/implementations/rands/seeding/rand_cpu_x86.c',
      'openssl/providers/implementations/rands/seeding/rand_tsc.c',
      'openssl/providers/implementations/rands/seeding/rand_unix.c',
      'openssl/providers/implementations/rands/seeding/rand_win.c',
      'openssl/providers/implementations/signature/dsa_sig.c',
      'openssl/providers/implementations/signature/ecdsa_sig.c',
      'openssl/providers/implementations/signature/eddsa_sig.c',
      'openssl/providers/implementations/signature/mac_legacy_sig.c',
      'openssl/providers/implementations/signature/rsa_sig.c',
      'openssl/providers/implementations/signature/sm2_sig.c',
      'openssl/providers/implementations/storemgmt/file_store.c',
      'openssl/providers/implementations/storemgmt/file_store_any2obj.c',
      'openssl/ssl/s3_cbc.c',
      'openssl/providers/common/der/der_dsa_key.c',
      'openssl/providers/common/der/der_dsa_sig.c',
      'openssl/providers/common/der/der_ec_key.c',
      'openssl/providers/common/der/der_ec_sig.c',
      'openssl/providers/common/der/der_ecx_key.c',
      'openssl/providers/common/der/der_rsa_key.c',
      'openssl/providers/common/provider_ctx.c',
      'openssl/providers/common/provider_err.c',
      'openssl/providers/implementations/ciphers/ciphercommon.c',
      'openssl/providers/implementations/ciphers/ciphercommon_block.c',
      'openssl/providers/implementations/ciphers/ciphercommon_ccm.c',
      'openssl/providers/implementations/ciphers/ciphercommon_ccm_hw.c',
      'openssl/providers/implementations/ciphers/ciphercommon_gcm.c',
      'openssl/providers/implementations/ciphers/ciphercommon_gcm_hw.c',
      'openssl/providers/implementations/ciphers/ciphercommon_hw.c',
      'openssl/providers/implementations/digests/digestcommon.c',
      'openssl/ssl/record/tls_pad.c',
      'openssl/providers/implementations/ciphers/cipher_blowfish.c',
      'openssl/providers/implementations/ciphers/cipher_blowfish_hw.c',
      'openssl/providers/implementations/ciphers/cipher_cast5.c',
      'openssl/providers/implementations/ciphers/cipher_cast5_hw.c',
      'openssl/providers/implementations/ciphers/cipher_des.c',
      'openssl/providers/implementations/ciphers/cipher_des_hw.c',
      'openssl/providers/implementations/ciphers/cipher_desx.c',
      'openssl/providers/implementations/ciphers/cipher_desx_hw.c',
      'openssl/providers/implementations/ciphers/cipher_idea.c',
      'openssl/providers/implementations/ciphers/cipher_idea_hw.c',
      'openssl/providers/implementations/ciphers/cipher_rc2.c',
      'openssl/providers/implementations/ciphers/cipher_rc2_hw.c',
      'openssl/providers/implementations/ciphers/cipher_rc4.c',
      'openssl/providers/implementations/ciphers/cipher_rc4_hmac_md5.c',
      'openssl/providers/implementations/ciphers/cipher_rc4_hmac_md5_hw.c',
      'openssl/providers/implementations/ciphers/cipher_rc4_hw.c',
      'openssl/providers/implementations/ciphers/cipher_seed.c',
      'openssl/providers/implementations/ciphers/cipher_seed_hw.c',
      'openssl/providers/implementations/ciphers/cipher_tdes_common.c',
      'openssl/providers/implementations/digests/md4_prov.c',
      'openssl/providers/implementations/digests/mdc2_prov.c',
      'openssl/providers/implementations/digests/ripemd_prov.c',
      'openssl/providers/implementations/digests/wp_prov.c',
      'openssl/providers/implementations/kdfs/pbkdf1.c',
      'openssl/providers/prov_running.c',
      'openssl/providers/legacyprov.c',
    ],
    'openssl_sources_linux-aarch64': [
      './config/archs/linux-aarch64/asm/crypto/aes/aesv8-armx.S',
      './config/archs/linux-aarch64/asm/crypto/aes/vpaes-armv8.S',
      './config/archs/linux-aarch64/asm/crypto/bn/armv8-mont.S',
      './config/archs/linux-aarch64/asm/crypto/chacha/chacha-armv8.S',
      './config/archs/linux-aarch64/asm/crypto/ec/ecp_nistz256-armv8.S',
      './config/archs/linux-aarch64/asm/crypto/arm64cpuid.S',
      './config/archs/linux-aarch64/asm/crypto/modes/aes-gcm-armv8_64.S',
      './config/archs/linux-aarch64/asm/crypto/modes/ghashv8-armx.S',
      './config/archs/linux-aarch64/asm/crypto/poly1305/poly1305-armv8.S',
      './config/archs/linux-aarch64/asm/crypto/sha/keccak1600-armv8.S',
      './config/archs/linux-aarch64/asm/crypto/sha/sha1-armv8.S',
      './config/archs/linux-aarch64/asm/crypto/sha/sha256-armv8.S',
      './config/archs/linux-aarch64/asm/crypto/sha/sha512-armv8.S',
      './config/archs/linux-aarch64/asm/providers/common/der/der_sm2_gen.c',
      './config/archs/linux-aarch64/asm/providers/common/der/der_digests_gen.c',
      './config/archs/linux-aarch64/asm/providers/common/der/der_dsa_gen.c',
      './config/archs/linux-aarch64/asm/providers/common/der/der_ec_gen.c',
      './config/archs/linux-aarch64/asm/providers/common/der/der_ecx_gen.c',
      './config/archs/linux-aarch64/asm/providers/common/der/der_rsa_gen.c',
      './config/archs/linux-aarch64/asm/providers/common/der/der_wrap_gen.c',
      './config/archs/linux-aarch64/asm/providers/legacy.ld',
      './config/archs/linux-aarch64/asm/providers/fips.ld',
    ],
    'openssl_defines_linux-aarch64': [
      'NDEBUG',
      'OPENSSL_USE_NODELETE',
      'OPENSSL_BUILDING_OPENSSL',
      'ECP_NISTZ256_ASM',
      'KECCAK1600_ASM',
      'OPENSSL_BN_ASM_MONT',
      'OPENSSL_CPUID_OBJ',
      'POLY1305_ASM',
      'SHA1_ASM',
      'SHA256_ASM',
      'SHA512_ASM',
      'VPAES_ASM',
      'OPENSSL_PIC',
    ],
    'openssl_cflags_linux-aarch64': [
      '-Wa,--noexecstack',
      '-Wall -O3',
      '-pthread',
      '-Wall -O3',
    ],
    'openssl_ex_libs_linux-aarch64': [
      '-ldl -pthread',
    ],
  },
  'include_dirs': [
    '.',
    './include',
    './crypto',
    './crypto/include/internal',
    './providers/common/include',
  ],
  'defines': ['<@(openssl_defines_linux-aarch64)'],
  'cflags' : ['<@(openssl_cflags_linux-aarch64)'],
  'libraries': ['<@(openssl_ex_libs_linux-aarch64)'],
  'sources': ['<@(openssl_sources)', '<@(openssl_sources_linux-aarch64)'],
  'direct_dependent_settings': {
    'include_dirs': ['./include', '.'],
    'defines': ['<@(openssl_defines_linux-aarch64)'],
  },
}
