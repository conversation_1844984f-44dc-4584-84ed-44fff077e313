// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@abstract
extern class JSFunctionOrBoundFunctionOrWrappedFunction extends JSObject {}

extern class JSBoundFunction extends
    JSFunctionOrBoundFunctionOrWrappedFunction {
  // The wrapped function object.
  bound_target_function: Callable;
  // The value that is always passed as the this value when calling the wrapped
  // function.
  bound_this: JSAny|SourceTextModule;
  // A list of values whose elements are used as the first arguments to any call
  // to the wrapped function.
  bound_arguments: FixedArray;
}

extern class JSWrappedFunction extends
    JSFunctionOrBoundFunctionOrWrappedFunction {
  // The wrapped function object.
  wrapped_target_function: Callable;
  // The creation context.
  context: NativeContext;
}

// This class does not use the generated verifier, so if you change anything
// here, please also update JSFunctionVerify in objects-debug.cc.
@highestInstanceTypeWithinParentClassRange
extern class JSFun<PERSON> extends JSFun<PERSON>OrBoundFunctionOrWrappedFunction {
  // When the sandbox is enabled, the Code object is referenced through an
  // indirect pointer. Otherwise, it is a regular tagged pointer.
  @if(V8_ENABLE_SANDBOX) code: IndirectPointer<Code>;
  @ifnot(V8_ENABLE_SANDBOX) code: Code;
  shared_function_info: SharedFunctionInfo;
  context: Context;
  feedback_cell: FeedbackCell;
  // Space for the following field may or may not be allocated.
  prototype_or_initial_map: JSReceiver|Map;
}

// Class constructors are special, because they are callable, but [[Call]] will
// raise an exception.
// See ES6 section 9.2.1 [[Call]] ( thisArgument, argumentsList ).
@doNotGenerateCast
@highestInstanceTypeWithinParentClassRange
extern class JSClassConstructor extends JSFunction
    generates 'TNode<JSFunction>';

type JSFunctionWithPrototypeSlot extends JSFunction;
