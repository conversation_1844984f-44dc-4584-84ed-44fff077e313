TAP version 13
# Subtest: escaped description \\ \# \\\#\\ \\n \\t \\f \\v \\b \\r
ok 1 - escaped description \\ \# \\\#\\ \\n \\t \\f \\v \\b \\r
  ---
  duration_ms: *
  ...
# Subtest: escaped skip message
ok 2 - escaped skip message # SKIP \#skip
  ---
  duration_ms: *
  ...
# Subtest: escaped todo message
ok 3 - escaped todo message # TODO \#todo
  ---
  duration_ms: *
  ...
# Subtest: escaped diagnostic
ok 4 - escaped diagnostic
  ---
  duration_ms: *
  ...
# \#diagnostic
1..4
# tests 4
# suites 0
# pass 2
# fail 0
# cancelled 0
# skipped 1
# todo 1
# duration_ms *
