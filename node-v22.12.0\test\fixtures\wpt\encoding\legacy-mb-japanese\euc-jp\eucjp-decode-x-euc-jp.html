<!DOCTYPE html>
<html lang="en-GB">
<head>
<meta charset="utf-8"/>
<title>x-euc-jp decoding</title>
<meta name="timeout" content="long">
<meta name="variant" content="?1-1000">
<meta name="variant" content="?1001-2000">
<meta name="variant" content="?2001-3000">
<meta name="variant" content="?3001-4000">
<meta name="variant" content="?4001-5000">
<meta name="variant" content="?5001-6000">
<meta name="variant" content="?6001-7000">
<meta name="variant" content="?7001-8000">
<meta name="variant" content="?8001-9000">
<meta name="variant" content="?9001-10000">
<meta name="variant" content="?10001-11000">
<meta name="variant" content="?11001-12000">
<meta name="variant" content="?12001-13000">
<meta name="variant" content="?13001-last">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/common/subset-tests.js"></script>
<link rel="author" title="Richard Ishida" href="mailto:<EMAIL>">
<link rel="help" href="https://encoding.spec.whatwg.org/#euc-jp">
<meta name="assert" content="The browser produces the same decoding behavior for a document labeled 'x-euc-jp' as for a document labeled 'euc-jp'.">
<style>
 iframe { display:none }
</style>
<script src="jis0208_index.js"></script>
<script src="jis0212_index.js"></script>
<script src="eucjp-decoder.js"></script>
<script src="../../resources/decode-common.js"></script>
</head>

<body onload="showNodes(eucjpDecoder);">

<iframe src="eucjp_chars-x-euc-jp.html" name="scriptWindow" id="scrwin"></iframe>

<div id="log"></div>

</body>
</html>

