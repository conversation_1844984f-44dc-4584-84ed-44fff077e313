TAP version 13
# Subtest: nested - no tests
    # Subtest: nested
        # Subtest: nested
        ok 1 - nested
          ---
          duration_ms: *
          ...
        1..1
    ok 1 - nested
      ---
      duration_ms: *
      type: 'suite'
      ...
    1..1
ok 1 - nested - no tests
  ---
  duration_ms: *
  type: 'suite'
  ...
1..1
# tests 1
# suites 2
# pass 1
# fail 0
# cancelled 0
# skipped 0
# todo 0
# duration_ms *
