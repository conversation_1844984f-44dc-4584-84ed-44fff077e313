// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

type TieringState extends uint16 constexpr 'TieringState';

bitfield struct FeedbackVectorFlags extends uint16 {
  tiering_state: TieringState: 3 bit;
  // Set for non-executed functions with --log-function-events in order to
  // log first-executions of code objects with minimal overhead.
  log_next_execution: bool: 1 bit;
  // Whether the maybe_optimized_code field contains a code object. 'maybe',
  // because they flag may lag behind the actual state of the world (it will be
  // updated in time).
  maybe_has_maglev_code: bool: 1 bit;
  maybe_has_turbofan_code: bool: 1 bit;
  // Just one bit, since only {kNone,kInProgress} are relevant for OSR.
  osr_tiering_state: TieringState: 1 bit;
  interrupt_budget_reset_by_ic_change: bool: 1 bit;
  all_your_bits_are_belong_to_jgruber: uint32: 8 bit;
}

bitfield struct OsrState extends uint8 {
  // The layout is chosen s.t. osr_urgency and
  // maybe_has_(maglev|turbofan)_osr_code can be loaded with a single load
  // (i.e. no masking required).
  osr_urgency: uint32: 3 bit;
  maybe_has_maglev_osr_code: bool: 1 bit;
  maybe_has_turbofan_osr_code: bool: 1 bit;
  // In order to have fast OSR checks in Ignition and Sparkplug, these bits
  // should remain 0. That way, the OSR check can be implemented as a single
  // comparison.
  dont_use_these_bits_unless_beneficial: uint32: 3 bit;
}

@cppObjectDefinition
extern class ClosureFeedbackCellArray extends HeapObject {
  const capacity: Smi;
  objects[capacity]: FeedbackCell;
}

@generateBodyDescriptor
extern class FeedbackVector extends HeapObject {
  const length: int32;
  invocation_count: int32;
  @if(TAGGED_SIZE_8_BYTES) optional_padding: uint32;
  invocation_count_before_stable: uint8;
  osr_state: OsrState;
  flags: FeedbackVectorFlags;
  shared_function_info: SharedFunctionInfo;
  closure_feedback_cell_array: ClosureFeedbackCellArray;
  parent_feedback_cell: FeedbackCell;
  // TODO(saelo): if we had support for weak indirect pointers, this field
  // could become a code pointer, which would save one indirection.
  maybe_optimized_code: Weak<CodeWrapper>;
  @cppRelaxedLoad raw_feedback_slots[length]: MaybeObject;
}

extern class FeedbackMetadata extends HeapObject;
