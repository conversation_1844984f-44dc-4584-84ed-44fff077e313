beginning
test.vm:1
throw new Error("boo!")
^

Error: boo!
    at test.vm:1:7
    at Script.runInThisContext (node:vm:*)
    at Object.runInThisContext (node:vm:*)
    at Object.<anonymous> (*fixtures*vm*vm_display_runtime_error.js:31:6)
test.vm:1
throw new Error("spooky!")
^

Error: spooky!
    at test.vm:1:7
    at Script.runInThisContext (node:vm:*)
    at Object.runInThisContext (node:vm:*)
    at Object.<anonymous> (*fixtures*vm*vm_display_runtime_error.js:36:4)

Node.js *
