gonna timeout
TAP version 13
not gonna timeout
before each test second 2
after each test first 0
gonna timeout
# Subtest: before each timeout
    # Subtest: first describe first test
    not ok 1 - first describe first test
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/timeout_in_before_each_should_not_affect_further_tests.js:(LINE):3'
      failureType: 'hookFailed'
      error: 'failed running beforeEach hook'
      code: 'ERR_TEST_FAILURE'
      stack: |-
        async Promise.all (index 0)
      ...
    # Subtest: first describe second test
    ok 2 - first describe second test
      ---
      duration_ms: *
      ...
    1..2
not ok 1 - before each timeout
  ---
  duration_ms: *
  type: 'suite'
  location: '/test/fixtures/test-runner/output/timeout_in_before_each_should_not_affect_further_tests.js:(LINE):1'
  failureType: 'subtestsFailed'
  error: '1 subtest failed'
  code: 'ERR_TEST_FAILURE'
  ...
after each test second 1
not gonna timeout
# Subtest: after each timeout
    # Subtest: second describe first test
    not ok 1 - second describe first test
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/timeout_in_before_each_should_not_affect_further_tests.js:(LINE):3'
      failureType: 'hookFailed'
      error: 'failed running afterEach hook'
      code: 'ERR_TEST_FAILURE'
      stack: |-
        async Promise.all (index 0)
      ...
    # Subtest: second describe second test
    ok 2 - second describe second test
      ---
      duration_ms: *
      ...
    1..2
not ok 2 - after each timeout
  ---
  duration_ms: *
  type: 'suite'
  location: '/test/fixtures/test-runner/output/timeout_in_before_each_should_not_affect_further_tests.js:(LINE):1'
  failureType: 'subtestsFailed'
  error: '1 subtest failed'
  code: 'ERR_TEST_FAILURE'
  ...
1..2
# tests 4
# suites 2
# pass 2
# fail 2
# cancelled 0
# skipped 0
# todo 0
# duration_ms *
