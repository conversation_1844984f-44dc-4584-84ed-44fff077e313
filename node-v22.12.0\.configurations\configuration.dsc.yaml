# yaml-language-server: $schema=https://aka.ms/configuration-dsc-schema/0.2
# Reference: https://github.com/nodejs/node/blob/main/BUILDING.md#windows
properties:
  resources:
    - resource: Microsoft.WinGet.DSC/WinGetPackage
      id: pythonPackage
      directives:
        description: Install Python 3.12
        module: Microsoft.WinGet.DSC
        allowPrerelease: true
      settings:
        id: Python.Python.3.12
        source: winget
    - resource: Microsoft.WinGet.DSC/WinGetPackage
      id: vsPackage
      directives:
        description: Install Visual Studio 2022 Community
        allowPrerelease: true
      settings:
        id: Microsoft.VisualStudio.2022.Community
        source: winget
        useLatest: true
    - resource: Microsoft.VisualStudio.DSC/VSComponents
      id: vsComponents
      dependsOn:
        - vsPackage
      directives:
        description: Install required VS workloads and components
        allowPrerelease: true
      settings:
        productId: Microsoft.VisualStudio.Product.Community
        channelId: VisualStudio.17.Release
        includeRecommended: true
        components:
          - Microsoft.VisualStudio.Workload.NativeDesktop
          - Microsoft.VisualStudio.Component.VC.Llvm.Clang
          - Microsoft.VisualStudio.Component.VC.Llvm.ClangToolset
    - resource: Microsoft.WinGet.DSC/WinGetPackage
      id: gitPackage
      directives:
        description: Install Git
        allowPrerelease: true
      settings:
        id: Git.Git
        source: winget
    - resource: Microsoft.WinGet.DSC/WinGetPackage
      id: nasmPackage
      directives:
        description: Install NetWide Assembler (NASM)
        allowPrerelease: true
      settings:
        id: Nasm.Nasm
        source: winget
  configurationVersion: 0.1.0
