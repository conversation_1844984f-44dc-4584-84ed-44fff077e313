/*
 * Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

/* Macros to build Self test data */
#define ITM(x) ((void *)&x), sizeof(x)
#define ITM_STR(x) ((void *)&x), (sizeof(x) - 1)

#define ST_KAT_PARAM_END() { "", 0, NULL, 0 }
#define ST_KAT_PARAM_BIGNUM(name, data)                                        \
    { name, OSSL_PARAM_UNSIGNED_INTEGER, ITM(data) }
#define ST_KAT_PARAM_OCTET(name, data)                                         \
    { name, OSSL_PARAM_OCTET_STRING, ITM(data) }
#define ST_KAT_PARAM_UTF8STRING(name, data)                                    \
    { name, OSSL_PARAM_UTF8_STRING, ITM_STR(data) }
#define ST_KAT_PARAM_UTF8CHAR(name, data)                                      \
    { name, OSSL_PARAM_UTF8_STRING, ITM(data) }
#define ST_KAT_PARAM_INT(name, i)                                              \
    { name, OSSL_PARAM_INTEGER, ITM(i) }

/* used to store raw parameters for keys and algorithms */
typedef struct st_kat_param_st {
    const char *name;  /* an OSSL_PARAM name */
    size_t type;       /* the type associated with the data */
    const void *data;  /* unsigned char [], or char [] depending on the type */
    size_t data_len;   /* the length of the data */
} ST_KAT_PARAM;

typedef struct st_kat_st {
    const char *desc;
    const char *algorithm;
    const unsigned char *pt;
    size_t pt_len;
    const unsigned char *expected;
    size_t expected_len;
} ST_KAT;

#define CIPHER_MODE_ENCRYPT 1
#define CIPHER_MODE_DECRYPT 2
#define CIPHER_MODE_ALL     (CIPHER_MODE_ENCRYPT | CIPHER_MODE_DECRYPT)

typedef ST_KAT ST_KAT_DIGEST;
typedef struct st_kat_cipher_st {
    ST_KAT base;
    int mode;
    const unsigned char *key;
    size_t key_len;
    const unsigned char *iv;
    size_t iv_len;
    const unsigned char *aad;
    size_t aad_len;
    const unsigned char *tag;
    size_t tag_len;
} ST_KAT_CIPHER;

typedef struct st_kat_kdf_st {
    const char *desc;
    const char *algorithm;
    const ST_KAT_PARAM *params;
    const unsigned char *expected;
    size_t expected_len;
} ST_KAT_KDF;

typedef struct st_kat_drbg_st {
    const char *desc;
    const char *algorithm;
    const char *param_name;
    char *param_value;
    const unsigned char *entropyin;
    size_t entropyinlen;
    const unsigned char *nonce;
    size_t noncelen;
    const unsigned char *persstr;
    size_t persstrlen;
    const unsigned char *entropyinpr1;
    size_t entropyinpr1len;
    const unsigned char *entropyinpr2;
    size_t entropyinpr2len;
    const unsigned char *entropyaddin1;
    size_t entropyaddin1len;
    const unsigned char *entropyaddin2;
    size_t entropyaddin2len;
    const unsigned char *expected;
    size_t expectedlen;
} ST_KAT_DRBG;

typedef struct st_kat_kas_st {
    const char *desc;
    const char *algorithm;

    const ST_KAT_PARAM *key_group;
    const ST_KAT_PARAM *key_host_data;
    const ST_KAT_PARAM *key_peer_data;

    const unsigned char *expected;
    size_t expected_len;
} ST_KAT_KAS;

typedef struct st_kat_sign_st {
    const char *desc;
    const char *algorithm;
    const char *mdalgorithm;
    const ST_KAT_PARAM *key;
    const unsigned char *sig_expected; /* Set to NULL if this value changes */
    size_t sig_expected_len;
} ST_KAT_SIGN;

typedef struct st_kat_asym_cipher_st {
    const char *desc;
    const char *algorithm;
    int encrypt;
    const ST_KAT_PARAM *key;
    const ST_KAT_PARAM *postinit;
    const unsigned char *in;
    size_t in_len;
    const unsigned char *expected;
    size_t expected_len;
} ST_KAT_ASYM_CIPHER;

/*- DIGEST TEST DATA */
static const unsigned char sha1_pt[] = "abc";
static const unsigned char sha1_digest[] = {
    0xA9, 0x99, 0x3E, 0x36, 0x47, 0x06, 0x81, 0x6A, 0xBA, 0x3E, 0x25, 0x71,
    0x78, 0x50, 0xC2, 0x6C, 0x9C, 0xD0, 0xD8, 0x9D
};

static const unsigned char sha512_pt[] = "abc";
static const unsigned char sha512_digest[] = {
    0xDD, 0xAF, 0x35, 0xA1, 0x93, 0x61, 0x7A, 0xBA, 0xCC, 0x41, 0x73, 0x49,
    0xAE, 0x20, 0x41, 0x31, 0x12, 0xE6, 0xFA, 0x4E, 0x89, 0xA9, 0x7E, 0xA2,
    0x0A, 0x9E, 0xEE, 0xE6, 0x4B, 0x55, 0xD3, 0x9A, 0x21, 0x92, 0x99, 0x2A,
    0x27, 0x4F, 0xC1, 0xA8, 0x36, 0xBA, 0x3C, 0x23, 0xA3, 0xFE, 0xEB, 0xBD,
    0x45, 0x4D, 0x44, 0x23, 0x64, 0x3C, 0xE8, 0x0E, 0x2A, 0x9A, 0xC9, 0x4F,
    0xA5, 0x4C, 0xA4, 0x9F
};
static const unsigned char sha3_256_pt[] = { 0xe7, 0x37, 0x21, 0x05 };
static const unsigned char sha3_256_digest[] = {
    0x3a, 0x42, 0xb6, 0x8a, 0xb0, 0x79, 0xf2, 0x8c, 0x4c, 0xa3, 0xc7, 0x52,
    0x29, 0x6f, 0x27, 0x90, 0x06, 0xc4, 0xfe, 0x78, 0xb1, 0xeb, 0x79, 0xd9,
    0x89, 0x77, 0x7f, 0x05, 0x1e, 0x40, 0x46, 0xae
};

static const ST_KAT_DIGEST st_kat_digest_tests[] =
{
    {
         OSSL_SELF_TEST_DESC_MD_SHA1,
         "SHA1",
         ITM_STR(sha1_pt),
         ITM(sha1_digest),
    },
    {
         OSSL_SELF_TEST_DESC_MD_SHA2,
         "SHA512",
         ITM_STR(sha512_pt),
         ITM(sha512_digest),
    },
    {
         OSSL_SELF_TEST_DESC_MD_SHA3,
         "SHA3-256",
         ITM(sha3_256_pt),
         ITM(sha3_256_digest),
    },
};


/*- CIPHER TEST DATA */

/* DES3 test data */
static const unsigned char des_ede3_cbc_pt[] = {
    0x6B, 0xC1, 0xBE, 0xE2, 0x2E, 0x40, 0x9F, 0x96,
    0xE9, 0x3D, 0x7E, 0x11, 0x73, 0x93, 0x17, 0x2A,
    0xAE, 0x2D, 0x8A, 0x57, 0x1E, 0x03, 0xAC, 0x9C,
    0x9E, 0xB7, 0x6F, 0xAC, 0x45, 0xAF, 0x8E, 0x51
};
static const unsigned char des_ede3_cbc_key[] = {
    0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF,
    0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF, 0x01,
    0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF, 0x01, 0x23
};
static const unsigned char des_ede3_cbc_iv[] = {
    0xF6, 0x9F, 0x24, 0x45, 0xDF, 0x4F, 0x9B, 0x17
};
static const unsigned char des_ede3_cbc_ct[] = {
    0x20, 0x79, 0xC3, 0xD5, 0x3A, 0xA7, 0x63, 0xE1,
    0x93, 0xB7, 0x9E, 0x25, 0x69, 0xAB, 0x52, 0x62,
    0x51, 0x65, 0x70, 0x48, 0x1F, 0x25, 0xB5, 0x0F,
    0x73, 0xC0, 0xBD, 0xA8, 0x5C, 0x8E, 0x0D, 0xA7
};

/* AES-256 GCM test data */
static const unsigned char aes_256_gcm_key[] = {
    0x92, 0xe1, 0x1d, 0xcd, 0xaa, 0x86, 0x6f, 0x5c,
    0xe7, 0x90, 0xfd, 0x24, 0x50, 0x1f, 0x92, 0x50,
    0x9a, 0xac, 0xf4, 0xcb, 0x8b, 0x13, 0x39, 0xd5,
    0x0c, 0x9c, 0x12, 0x40, 0x93, 0x5d, 0xd0, 0x8b
};
static const unsigned char aes_256_gcm_iv[] = {
    0xac, 0x93, 0xa1, 0xa6, 0x14, 0x52, 0x99, 0xbd,
    0xe9, 0x02, 0xf2, 0x1a
};
static const unsigned char aes_256_gcm_pt[] = {
    0x2d, 0x71, 0xbc, 0xfa, 0x91, 0x4e, 0x4a, 0xc0,
    0x45, 0xb2, 0xaa, 0x60, 0x95, 0x5f, 0xad, 0x24
};
static const unsigned char aes_256_gcm_aad[] = {
    0x1e, 0x08, 0x89, 0x01, 0x6f, 0x67, 0x60, 0x1c,
    0x8e, 0xbe, 0xa4, 0x94, 0x3b, 0xc2, 0x3a, 0xd6
};
static const unsigned char aes_256_gcm_ct[] = {
    0x89, 0x95, 0xae, 0x2e, 0x6d, 0xf3, 0xdb, 0xf9,
    0x6f, 0xac, 0x7b, 0x71, 0x37, 0xba, 0xe6, 0x7f
};
static const unsigned char aes_256_gcm_tag[] = {
    0xec, 0xa5, 0xaa, 0x77, 0xd5, 0x1d, 0x4a, 0x0a,
    0x14, 0xd9, 0xc5, 0x1e, 0x1d, 0xa4, 0x74, 0xab
};

/* AES-ECB test data */
static const unsigned char aes_128_ecb_key[] = {
    0x10, 0xa5, 0x88, 0x69, 0xd7, 0x4b, 0xe5, 0xa3,
    0x74, 0xcf, 0x86, 0x7c, 0xfb, 0x47, 0x38, 0x59
};
static const unsigned char aes_128_ecb_pt[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
static const unsigned char aes_128_ecb_ct[] = {
    0x6d, 0x25, 0x1e, 0x69, 0x44, 0xb0, 0x51, 0xe0,
    0x4e, 0xaa, 0x6f, 0xb4, 0xdb, 0xf7, 0x84, 0x65
};

static const ST_KAT_CIPHER st_kat_cipher_tests[] = {
#ifndef OPENSSL_NO_DES
    {
        {
            OSSL_SELF_TEST_DESC_CIPHER_TDES,
            "DES-EDE3-CBC",
            ITM(des_ede3_cbc_pt),
            ITM(des_ede3_cbc_ct)
        },
        CIPHER_MODE_ENCRYPT | CIPHER_MODE_DECRYPT,
        ITM(des_ede3_cbc_key),
        ITM(des_ede3_cbc_iv),
    },
#endif
    {
        {
            OSSL_SELF_TEST_DESC_CIPHER_AES_GCM,
            "AES-256-GCM",
            ITM(aes_256_gcm_pt),
            ITM(aes_256_gcm_ct)
        },
        CIPHER_MODE_ENCRYPT | CIPHER_MODE_DECRYPT,
        ITM(aes_256_gcm_key),
        ITM(aes_256_gcm_iv),
        ITM(aes_256_gcm_aad),
        ITM(aes_256_gcm_tag)
    },
    {
        {
            OSSL_SELF_TEST_DESC_CIPHER_AES_ECB,
            "AES-128-ECB",
            ITM(aes_128_ecb_pt),
            ITM(aes_128_ecb_ct)
        },
        CIPHER_MODE_DECRYPT,
        ITM(aes_128_ecb_key)
    }
};

static const char hkdf_digest[] = "SHA256";
static const unsigned char hkdf_secret[] = { 's', 'e', 'c', 'r', 'e', 't' };
static const unsigned char hkdf_salt[] = { 's', 'a', 'l', 't' };
static const unsigned char hkdf_info[] = { 'l', 'a', 'b', 'e', 'l' };

static const ST_KAT_PARAM hkdf_params[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, hkdf_digest),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_KEY, hkdf_secret),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_SALT, hkdf_salt),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_INFO, hkdf_info),
    ST_KAT_PARAM_END()
};
static const unsigned char hkdf_expected[] = {
    0x2a, 0xc4, 0x36, 0x9f, 0x52, 0x59, 0x96, 0xf8,
    0xde, 0x13
};

static const char sskdf_digest[] = "SHA224";
static const unsigned char sskdf_secret[] = {
    0x6d, 0xbd, 0xc2, 0x3f, 0x04, 0x54, 0x88, 0xe4,
    0x06, 0x27, 0x57, 0xb0, 0x6b, 0x9e, 0xba, 0xe1,
    0x83, 0xfc, 0x5a, 0x59, 0x46, 0xd8, 0x0d, 0xb9,
    0x3f, 0xec, 0x6f, 0x62, 0xec, 0x07, 0xe3, 0x72,
    0x7f, 0x01, 0x26, 0xae, 0xd1, 0x2c, 0xe4, 0xb2,
    0x62, 0xf4, 0x7d, 0x48, 0xd5, 0x42, 0x87, 0xf8,
    0x1d, 0x47, 0x4c, 0x7c, 0x3b, 0x18, 0x50, 0xe9
};
static const unsigned char sskdf_otherinfo[] = {
    0xa1, 0xb2, 0xc3, 0xd4, 0xe5, 0x43, 0x41, 0x56,
    0x53, 0x69, 0x64, 0x3c, 0x83, 0x2e, 0x98, 0x49,
    0xdc, 0xdb, 0xa7, 0x1e, 0x9a, 0x31, 0x39, 0xe6,
    0x06, 0xe0, 0x95, 0xde, 0x3c, 0x26, 0x4a, 0x66,
    0xe9, 0x8a, 0x16, 0x58, 0x54, 0xcd, 0x07, 0x98,
    0x9b, 0x1e, 0xe0, 0xec, 0x3f, 0x8d, 0xbe
};
static const unsigned char sskdf_expected[] = {
    0xa4, 0x62, 0xde, 0x16, 0xa8, 0x9d, 0xe8, 0x46,
    0x6e, 0xf5, 0x46, 0x0b, 0x47, 0xb8
};
static const ST_KAT_PARAM sskdf_params[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, sskdf_digest),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_KEY, sskdf_secret),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_INFO, sskdf_otherinfo),
    ST_KAT_PARAM_END()
};

static const char x942kdf_digest[] = "SHA1";
static const char x942kdf_cekalg[] = "AES-128-WRAP";
static const unsigned char x942kdf_secret[] = {
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
    0x10, 0x11, 0x12, 0x13
};
static const unsigned char x942kdf_expected[] = {
    0xd6, 0xd6, 0xb0, 0x94, 0xc1, 0x02, 0x7a, 0x7d,
    0xe6, 0xe3, 0x11, 0x72, 0x94, 0xa3, 0x53, 0x64
};
static const ST_KAT_PARAM x942kdf_params[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, x942kdf_digest),
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_CEK_ALG, x942kdf_cekalg),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_KEY, x942kdf_secret),
    ST_KAT_PARAM_END()
};

static const char x963kdf_digest[] = "SHA256";
static const unsigned char x963kdf_otherinfo[] = {
    0x75, 0xee, 0xf8, 0x1a, 0xa3, 0x04, 0x1e, 0x33,
    0xb8, 0x09, 0x71, 0x20, 0x3d, 0x2c, 0x0c, 0x52
};
static const unsigned char x963kdf_secret[] = {
    0x22, 0x51, 0x8b, 0x10, 0xe7, 0x0f, 0x2a, 0x3f,
    0x24, 0x38, 0x10, 0xae, 0x32, 0x54, 0x13, 0x9e,
    0xfb, 0xee, 0x04, 0xaa, 0x57, 0xc7, 0xaf, 0x7d
};
static const unsigned char x963kdf_expected[] = {
    0xc4, 0x98, 0xaf, 0x77, 0x16, 0x1c, 0xc5, 0x9f,
    0x29, 0x62, 0xb9, 0xa7, 0x13, 0xe2, 0xb2, 0x15,
    0x15, 0x2d, 0x13, 0x97, 0x66, 0xce, 0x34, 0xa7,
    0x76, 0xdf, 0x11, 0x86, 0x6a, 0x69, 0xbf, 0x2e
};
static const ST_KAT_PARAM x963kdf_params[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, x963kdf_digest),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_KEY, x963kdf_secret),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_INFO, x963kdf_otherinfo),
    ST_KAT_PARAM_END()
};

static const char pbkdf2_digest[] = "SHA256";
static const unsigned char pbkdf2_password[] = {
    0x70, 0x61, 0x73, 0x73, 0x00, 0x77, 0x6f, 0x72,
    0x64
};
static const unsigned char pbkdf2_salt[] = {
    0x73, 0x61, 0x00, 0x6c, 0x74
};
static const unsigned char pbkdf2_expected[] = {
    0x89, 0xb6, 0x9d, 0x05, 0x16, 0xf8, 0x29, 0x89,
    0x3c, 0x69, 0x62, 0x26, 0x65, 0x0a, 0x86, 0x87,
};
static int pbkdf2_iterations = 4096;
static int pbkdf2_pkcs5 = 1;
static const ST_KAT_PARAM pbkdf2_params[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, pbkdf2_digest),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_PASSWORD, pbkdf2_password),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_SALT, pbkdf2_salt),
    ST_KAT_PARAM_INT(OSSL_KDF_PARAM_ITER, pbkdf2_iterations),
    ST_KAT_PARAM_INT(OSSL_KDF_PARAM_PKCS5, pbkdf2_pkcs5),
    ST_KAT_PARAM_END()
};

static const char sshkdf_digest[] = "SHA1";
static const char sshkdf_type = EVP_KDF_SSHKDF_TYPE_INITIAL_IV_CLI_TO_SRV;
static const unsigned char sshkdf_key[] = {
    0x00, 0x00, 0x00, 0x80, 0x55, 0xba, 0xe9, 0x31,
    0xc0, 0x7f, 0xd8, 0x24, 0xbf, 0x10, 0xad, 0xd1,
    0x90, 0x2b, 0x6f, 0xbc, 0x7c, 0x66, 0x53, 0x47,
    0x38, 0x34, 0x98, 0xa6, 0x86, 0x92, 0x9f, 0xf5,
    0xa2, 0x5f, 0x8e, 0x40, 0xcb, 0x66, 0x45, 0xea,
    0x81, 0x4f, 0xb1, 0xa5, 0xe0, 0xa1, 0x1f, 0x85,
    0x2f, 0x86, 0x25, 0x56, 0x41, 0xe5, 0xed, 0x98,
    0x6e, 0x83, 0xa7, 0x8b, 0xc8, 0x26, 0x94, 0x80,
    0xea, 0xc0, 0xb0, 0xdf, 0xd7, 0x70, 0xca, 0xb9,
    0x2e, 0x7a, 0x28, 0xdd, 0x87, 0xff, 0x45, 0x24,
    0x66, 0xd6, 0xae, 0x86, 0x7c, 0xea, 0xd6, 0x3b,
    0x36, 0x6b, 0x1c, 0x28, 0x6e, 0x6c, 0x48, 0x11,
    0xa9, 0xf1, 0x4c, 0x27, 0xae, 0xa1, 0x4c, 0x51,
    0x71, 0xd4, 0x9b, 0x78, 0xc0, 0x6e, 0x37, 0x35,
    0xd3, 0x6e, 0x6a, 0x3b, 0xe3, 0x21, 0xdd, 0x5f,
    0xc8, 0x23, 0x08, 0xf3, 0x4e, 0xe1, 0xcb, 0x17,
    0xfb, 0xa9, 0x4a, 0x59,
};
static const unsigned char sshkdf_xcghash[] = {
    0xa4, 0xeb, 0xd4, 0x59, 0x34, 0xf5, 0x67, 0x92,
    0xb5, 0x11, 0x2d, 0xcd, 0x75, 0xa1, 0x07, 0x5f,
    0xdc, 0x88, 0x92, 0x45,
};
static const unsigned char sshkdf_session_id[] = {
    0xa4, 0xeb, 0xd4, 0x59, 0x34, 0xf5, 0x67, 0x92,
    0xb5, 0x11, 0x2d, 0xcd, 0x75, 0xa1, 0x07, 0x5f,
    0xdc, 0x88, 0x92, 0x45,
};
static const unsigned char sshkdf_expected[] = {
    0xe2, 0xf6, 0x27, 0xc0, 0xb4, 0x3f, 0x1a, 0xc1,
};
static const ST_KAT_PARAM sshkdf_params[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, sshkdf_digest),
    ST_KAT_PARAM_UTF8CHAR(OSSL_KDF_PARAM_SSHKDF_TYPE, sshkdf_type),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_KEY, sshkdf_key),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_SSHKDF_XCGHASH, sshkdf_xcghash),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_SSHKDF_SESSION_ID, sshkdf_session_id),
    ST_KAT_PARAM_END()
};

static const char tls12prf_digest[] = "SHA256";
static const unsigned char tls12prf_secret[] = {
    0x20, 0x2c, 0x88, 0xc0, 0x0f, 0x84, 0xa1, 0x7a,
    0x20, 0x02, 0x70, 0x79, 0x60, 0x47, 0x87, 0x46,
    0x11, 0x76, 0x45, 0x55, 0x39, 0xe7, 0x05, 0xbe,
    0x73, 0x08, 0x90, 0x60, 0x2c, 0x28, 0x9a, 0x50,
    0x01, 0xe3, 0x4e, 0xeb, 0x3a, 0x04, 0x3e, 0x5d,
    0x52, 0xa6, 0x5e, 0x66, 0x12, 0x51, 0x88, 0xbf,
};
static const unsigned char tls12prf_seed[] = {
    'k', 'e', 'y', ' ', 'e', 'x', 'p', 'a', 'n', 's', 'i', 'o', 'n',
    0xae, 0x6c, 0x80, 0x6f, 0x8a, 0xd4, 0xd8, 0x07,
    0x84, 0x54, 0x9d, 0xff, 0x28, 0xa4, 0xb5, 0x8f,
    0xd8, 0x37, 0x68, 0x1a, 0x51, 0xd9, 0x28, 0xc3,
    0xe3, 0x0e, 0xe5, 0xff, 0x14, 0xf3, 0x98, 0x68,
    0x62, 0xe1, 0xfd, 0x91, 0xf2, 0x3f, 0x55, 0x8a,
    0x60, 0x5f, 0x28, 0x47, 0x8c, 0x58, 0xcf, 0x72,
    0x63, 0x7b, 0x89, 0x78, 0x4d, 0x95, 0x9d, 0xf7,
    0xe9, 0x46, 0xd3, 0xf0, 0x7b, 0xd1, 0xb6, 0x16,
 };
static const unsigned char tls12prf_expected[] = {
    0xd0, 0x61, 0x39, 0x88, 0x9f, 0xff, 0xac, 0x1e,
    0x3a, 0x71, 0x86, 0x5f, 0x50, 0x4a, 0xa5, 0xd0,
    0xd2, 0xa2, 0xe8, 0x95, 0x06, 0xc6, 0xf2, 0x27,
    0x9b, 0x67, 0x0c, 0x3e, 0x1b, 0x74, 0xf5, 0x31,
    0x01, 0x6a, 0x25, 0x30, 0xc5, 0x1a, 0x3a, 0x0f,
    0x7e, 0x1d, 0x65, 0x90, 0xd0, 0xf0, 0x56, 0x6b,
    0x2f, 0x38, 0x7f, 0x8d, 0x11, 0xfd, 0x4f, 0x73,
    0x1c, 0xdd, 0x57, 0x2d, 0x2e, 0xae, 0x92, 0x7f,
    0x6f, 0x2f, 0x81, 0x41, 0x0b, 0x25, 0xe6, 0x96,
    0x0b, 0xe6, 0x89, 0x85, 0xad, 0xd6, 0xc3, 0x84,
    0x45, 0xad, 0x9f, 0x8c, 0x64, 0xbf, 0x80, 0x68,
    0xbf, 0x9a, 0x66, 0x79, 0x48, 0x5d, 0x96, 0x6f,
    0x1a, 0xd6, 0xf6, 0x8b, 0x43, 0x49, 0x5b, 0x10,
    0xa6, 0x83, 0x75, 0x5e, 0xa2, 0xb8, 0x58, 0xd7,
    0x0c, 0xca, 0xc7, 0xec, 0x8b, 0x05, 0x3c, 0x6b,
    0xd4, 0x1c, 0xa2, 0x99, 0xd4, 0xe5, 0x19, 0x28,
};
static const ST_KAT_PARAM tls12prf_params[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, tls12prf_digest),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_SECRET, tls12prf_secret),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_SEED, tls12prf_seed),
    ST_KAT_PARAM_END()
};

static const char kbkdf_digest[] = "SHA256";
static const char kbkdf_mac[] = "HMAC";
static const unsigned char kbkdf_salt[] = { 'p', 'r', 'f' };
static const unsigned char kbkdf_prfinput[] = { 't', 'e', 's', 't' };
static unsigned char kbkdf_key[] = {
    0x37, 0x05, 0xD9, 0x60, 0x80, 0xC1, 0x77, 0x28,
    0xA0, 0xE8, 0x00, 0xEA, 0xB6, 0xE0, 0xD2, 0x3C,
};
static unsigned char kbkdf_expected[] = {
    0x9D, 0x18, 0x86, 0x16, 0xF6, 0x38, 0x52, 0xFE,
    0x86, 0x91, 0x5B, 0xB8, 0x40, 0xB4, 0xA8, 0x86,
    0xFF, 0x3E, 0x6B, 0xB0, 0xF8, 0x19, 0xB4, 0x9B,
    0x89, 0x33, 0x93, 0xD3, 0x93, 0x85, 0x42, 0x95,
};
static const ST_KAT_PARAM kbkdf_params[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, kbkdf_digest),
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_MAC, kbkdf_mac),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_KEY, kbkdf_key),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_SALT, kbkdf_salt),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_INFO, kbkdf_prfinput),
    ST_KAT_PARAM_END()
};

static const char tls13_kdf_digest[] = "SHA256";
static int tls13_kdf_extract_mode = EVP_KDF_HKDF_MODE_EXTRACT_ONLY;
static int tls13_kdf_expand_mode = EVP_KDF_HKDF_MODE_EXPAND_ONLY;
static const unsigned char tls13_kdf_prefix[] = {
    0x74, 0x6C, 0x73, 0x31, 0x33, 0x20          /* "tls13 " */
};
static const unsigned char tls13_kdf_client_early_secret_label[] = {
    0x63, 0x20, 0x65, 0x20, 0x74, 0x72, 0x61, 0x66,
    0x66, 0x69, 0x63                            /* "c e traffic"*/
};
static const unsigned char tls13_kdf_psk[] = {
    0xF8, 0xAF, 0x6A, 0xEA, 0x2D, 0x39, 0x7B, 0xAF,
    0x29, 0x48, 0xA2, 0x5B, 0x28, 0x34, 0x20, 0x06,
    0x92, 0xCF, 0xF1, 0x7E, 0xEE, 0x91, 0x65, 0xE4,
    0xE2, 0x7B, 0xAB, 0xEE, 0x9E, 0xDE, 0xFD, 0x05
};
static const unsigned char tls13_kdf_client_hello_hash[] = {
    0x7c, 0x92, 0xf6, 0x8b, 0xd5, 0xbf, 0x36, 0x38,
    0xea, 0x33, 0x8a, 0x64, 0x94, 0x72, 0x2e, 0x1b,
    0x44, 0x12, 0x7e, 0x1b, 0x7e, 0x8a, 0xad, 0x53,
    0x5f, 0x23, 0x22, 0xa6, 0x44, 0xff, 0x22, 0xb3
};

static const unsigned char tls13_kdf_early_secret[] = {
    0x15, 0x3B, 0x63, 0x94, 0xA9, 0xC0, 0x3C, 0xF3,
    0xF5, 0xAC, 0xCC, 0x6E, 0x45, 0x5A, 0x76, 0x93,
    0x28, 0x11, 0x38, 0xA1, 0xBC, 0xFA, 0x38, 0x03,
    0xC2, 0x67, 0x35, 0xDD, 0x11, 0x94, 0xD2, 0x16
};
static const unsigned char tls13_kdf_client_early_traffic_secret[] = {
    0xC8, 0x05, 0x83, 0xA9, 0x0E, 0x99, 0x5C, 0x48,
    0x96, 0x00, 0x49, 0x2A, 0x5D, 0xA6, 0x42, 0xE6,
    0xB1, 0xF6, 0x79, 0xBA, 0x67, 0x48, 0x28, 0x79,
    0x2D, 0xF0, 0x87, 0xB9, 0x39, 0x63, 0x61, 0x71
};
static const ST_KAT_PARAM tls13_kdf_early_secret_params[] = {
    ST_KAT_PARAM_INT(OSSL_KDF_PARAM_MODE, tls13_kdf_extract_mode),
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, tls13_kdf_digest),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_KEY, tls13_kdf_psk),
    ST_KAT_PARAM_END()
};
static const ST_KAT_PARAM tls13_kdf_client_early_secret_params[] = {
    ST_KAT_PARAM_INT(OSSL_KDF_PARAM_MODE, tls13_kdf_expand_mode),
    ST_KAT_PARAM_UTF8STRING(OSSL_KDF_PARAM_DIGEST, tls13_kdf_digest),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_KEY, tls13_kdf_early_secret),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_DATA, tls13_kdf_client_hello_hash),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_PREFIX, tls13_kdf_prefix),
    ST_KAT_PARAM_OCTET(OSSL_KDF_PARAM_LABEL,
                       tls13_kdf_client_early_secret_label),
    ST_KAT_PARAM_END()
};

static const ST_KAT_KDF st_kat_kdf_tests[] =
{
    {
        OSSL_SELF_TEST_DESC_KDF_TLS13_EXTRACT,
        OSSL_KDF_NAME_TLS1_3_KDF,
        tls13_kdf_early_secret_params,
        ITM(tls13_kdf_early_secret)
    },
    {
        OSSL_SELF_TEST_DESC_KDF_TLS13_EXPAND,
        OSSL_KDF_NAME_TLS1_3_KDF,
        tls13_kdf_client_early_secret_params,
        ITM(tls13_kdf_client_early_traffic_secret)
    },
    {
        OSSL_SELF_TEST_DESC_KDF_TLS12_PRF,
        OSSL_KDF_NAME_TLS1_PRF,
        tls12prf_params,
        ITM(tls12prf_expected)
    },
    {
        OSSL_SELF_TEST_DESC_KDF_PBKDF2,
        OSSL_KDF_NAME_PBKDF2,
        pbkdf2_params,
        ITM(pbkdf2_expected)
    },
    {
        OSSL_SELF_TEST_DESC_KDF_SSHKDF,
        OSSL_KDF_NAME_SSHKDF,
        sshkdf_params,
        ITM(sshkdf_expected)
    },
    {
        OSSL_SELF_TEST_DESC_KDF_KBKDF,
        OSSL_KDF_NAME_KBKDF,
        kbkdf_params,
        ITM(kbkdf_expected)
    },
    {
        OSSL_SELF_TEST_DESC_KDF_HKDF,
        OSSL_KDF_NAME_HKDF,
        hkdf_params,
        ITM(hkdf_expected)
    },
    {
        OSSL_SELF_TEST_DESC_KDF_SSKDF,
        OSSL_KDF_NAME_SSKDF,
        sskdf_params,
        ITM(sskdf_expected)
    },
    {
        OSSL_SELF_TEST_DESC_KDF_X963KDF,
        OSSL_KDF_NAME_X963KDF,
        x963kdf_params,
        ITM(x963kdf_expected)
    },
    {
        OSSL_SELF_TEST_DESC_KDF_X942KDF,
        OSSL_KDF_NAME_X942KDF_ASN1,
        x942kdf_params,
        ITM(x942kdf_expected)
    },
};

/*-
* DRBG test vectors are a small subset of
* https://csrc.nist.rip/groups/STM/cavp/documents/drbg/drbgtestvectors.zip
* Using the folder drbgvectors_pr_true
* Generated for CAVS 14.3.
*/

/*
 * Hash_DRBG.rsp
 *
 * [SHA-256]
 * [PredictionResistance = True]
 * [EntropyInputLen = 256]
 * [NonceLen = 128]
 * [PersonalizationStringLen = 256]
 * [AdditionalInputLen = 256]
 * [ReturnedBitsLen = 1024]
 *
 * COUNT = 14
 */
static const unsigned char drbg_hash_sha256_pr_entropyin[] = {
    0x06, 0x6d, 0xc8, 0xce, 0x75, 0xb2, 0x89, 0x66, 0xa6, 0x85, 0x16, 0x3f,
    0xe2, 0xa4, 0xd4, 0x27, 0xfb, 0xdb, 0x61, 0x66, 0x50, 0x61, 0x6b, 0xa2,
    0x82, 0xfc, 0x33, 0x2b, 0x4e, 0x6f, 0x12, 0x20
};
static const unsigned char drbg_hash_sha256_pr_nonce[] = {
    0x55, 0x9f, 0x7c, 0x64, 0x89, 0x70, 0x83, 0xec, 0x2d, 0x73, 0x70, 0xd9,
    0xf0, 0xe5, 0x07, 0x1f
};
static const unsigned char drbg_hash_sha256_pr_persstr[] = {
    0x88, 0x6f, 0x54, 0x9a, 0xad, 0x1a, 0xc6, 0x3d, 0x18, 0xcb, 0xcc, 0x66,
    0x85, 0xda, 0xa2, 0xc2, 0xf7, 0x9e, 0xb0, 0x89, 0x4c, 0xb4, 0xae, 0xf1,
    0xac, 0x54, 0x4f, 0xce, 0x57, 0xf1, 0x5e, 0x11
};
static const unsigned char drbg_hash_sha256_pr_entropyinpr0[] = {
    0xff, 0x80, 0xb7, 0xd2, 0x6a, 0x05, 0xbc, 0x8a, 0x7a, 0xbe, 0x53, 0x28,
    0x6b, 0x0e, 0xeb, 0x73, 0x3b, 0x71, 0x5a, 0x20, 0x5b, 0xfa, 0x4f, 0xf6,
    0x37, 0x03, 0xde, 0xad, 0xb6, 0xea, 0x0e, 0xf4
};
static const unsigned char drbg_hash_sha256_pr_entropyinpr1[] = {
    0xc7, 0x38, 0x32, 0x53, 0x46, 0x81, 0xed, 0xe3, 0x7e, 0x03, 0x84, 0x6d,
    0x3c, 0x84, 0x17, 0x67, 0x29, 0x7d, 0x24, 0x6c, 0x68, 0x92, 0x41, 0xd2,
    0xe7, 0x75, 0xbe, 0x7e, 0xc9, 0x96, 0x29, 0x3d
};
static const unsigned char drbg_hash_sha256_pr_addin0[] = {
    0xb7, 0x21, 0x5f, 0x14, 0xac, 0x7b, 0xaf, 0xd0, 0xa9, 0x17, 0x72, 0xba,
    0x22, 0xf7, 0x19, 0xaf, 0xbd, 0x20, 0xb3, 0x11, 0x63, 0x6c, 0x2b, 0x1e,
    0x83, 0xe4, 0xa8, 0x23, 0x35, 0x3f, 0xc6, 0xea
};
static const unsigned char drbg_hash_sha256_pr_addin1[] = {
    0xce, 0xd3, 0x1f, 0x7e, 0x0d, 0xae, 0x5b, 0xb5, 0xc0, 0x43, 0xe2, 0x46,
    0xb2, 0x94, 0x73, 0xe2, 0xfd, 0x39, 0x51, 0x2e, 0xad, 0x45, 0x69, 0xee,
    0xe3, 0xe3, 0x80, 0x33, 0x14, 0xab, 0xa7, 0xa3
};
static const unsigned char drbg_hash_sha256_pr_expected[] = {
    0x60, 0xc2, 0x34, 0xcf, 0xaf, 0xb4, 0x68, 0x03, 0x3b, 0xf1, 0x95, 0xe5,
    0x78, 0xce, 0x26, 0x6e, 0x14, 0x65, 0x32, 0x6a, 0x96, 0xa9, 0xe0, 0x3f,
    0x8b, 0x89, 0x36, 0x70, 0xef, 0x62, 0x75, 0x4d, 0x5e, 0x80, 0xd5, 0x53,
    0xa1, 0xf8, 0x49, 0x50, 0x20, 0x8b, 0x93, 0x43, 0x07, 0x9f, 0x2e, 0xf8,
    0x56, 0xe9, 0xc5, 0x70, 0x61, 0x85, 0x97, 0xb5, 0xdc, 0x82, 0xa2, 0xda,
    0xea, 0xa3, 0xfd, 0x9b, 0x2f, 0xd2, 0xa0, 0xd7, 0x1b, 0xc6, 0x29, 0x35,
    0xcc, 0xb8, 0x3d, 0xa0, 0x67, 0x98, 0x05, 0xa0, 0xe3, 0x1e, 0xfe, 0xe4,
    0xf0, 0xe5, 0x13, 0xb0, 0x83, 0x17, 0xfa, 0xca, 0x93, 0x5e, 0x38, 0x29,
    0x48, 0xd2, 0x72, 0xdb, 0x76, 0x3e, 0x6d, 0xf3, 0x25, 0x10, 0xff, 0x1b,
    0x99, 0xff, 0xf8, 0xc6, 0x0e, 0xb0, 0xdd, 0x29, 0x2e, 0xbc, 0xbb, 0xc8,
    0x0a, 0x01, 0x6e, 0xd3, 0xb0, 0x0e, 0x4e, 0xab
};

/*
 * CTR_DRBG.rsp
 *
 * [AES-128 use df]
 * [PredictionResistance = True]
 * [EntropyInputLen = 128]
 * [NonceLen = 64]
 * [PersonalizationStringLen = 128]
 * [AdditionalInputLen = 128]
 * [ReturnedBitsLen = 512]
 *
 * COUNT = 0
 */
static const unsigned char drbg_ctr_aes128_pr_df_entropyin[] = {
    0x92, 0x89, 0x8f, 0x31, 0xfa, 0x1c, 0xff, 0x6d, 0x18, 0x2f, 0x26, 0x06,
    0x43, 0xdf, 0xf8, 0x18
};
static const unsigned char drbg_ctr_aes128_pr_df_nonce[] = {
    0xc2, 0xa4, 0xd9, 0x72, 0xc3, 0xb9, 0xb6, 0x97
};
static const unsigned char drbg_ctr_aes128_pr_df_persstr[] = {
    0xea, 0x65, 0xee, 0x60, 0x26, 0x4e, 0x7e, 0xb6, 0x0e, 0x82, 0x68, 0xc4,
    0x37, 0x3c, 0x5c, 0x0b
};
static const unsigned char drbg_ctr_aes128_pr_df_entropyinpr0[] = {
    0x20, 0x72, 0x8a, 0x06, 0xf8, 0x6f, 0x8d, 0xd4, 0x41, 0xe2, 0x72, 0xb7,
    0xc4, 0x2c, 0xe8, 0x10
};
static const unsigned char drbg_ctr_aes128_pr_df_entropyinpr1[] = {
    0x3d, 0xb0, 0xf0, 0x94, 0xf3, 0x05, 0x50, 0x33, 0x17, 0x86, 0x3e, 0x22,
    0x08, 0xf7, 0xa5, 0x01
};
static const unsigned char drbg_ctr_aes128_pr_df_addin0[] = {
    0x1a, 0x40, 0xfa, 0xe3, 0xcc, 0x6c, 0x7c, 0xa0, 0xf8, 0xda, 0xba, 0x59,
    0x23, 0x6d, 0xad, 0x1d
};
static const unsigned char drbg_ctr_aes128_pr_df_addin1[] = {
    0x9f, 0x72, 0x76, 0x6c, 0xc7, 0x46, 0xe5, 0xed, 0x2e, 0x53, 0x20, 0x12,
    0xbc, 0x59, 0x31, 0x8c
};
static const unsigned char drbg_ctr_aes128_pr_df_expected[] = {
    0x5a, 0x35, 0x39, 0x87, 0x0f, 0x4d, 0x22, 0xa4, 0x09, 0x24, 0xee, 0x71,
    0xc9, 0x6f, 0xac, 0x72, 0x0a, 0xd6, 0xf0, 0x88, 0x82, 0xd0, 0x83, 0x28,
    0x73, 0xec, 0x3f, 0x93, 0xd8, 0xab, 0x45, 0x23, 0xf0, 0x7e, 0xac, 0x45,
    0x14, 0x5e, 0x93, 0x9f, 0xb1, 0xd6, 0x76, 0x43, 0x3d, 0xb6, 0xe8, 0x08,
    0x88, 0xf6, 0xda, 0x89, 0x08, 0x77, 0x42, 0xfe, 0x1a, 0xf4, 0x3f, 0xc4,
    0x23, 0xc5, 0x1f, 0x68
};

/*
 * HMAC_DRBG.rsp
 *
 * [SHA-1]
 * [PredictionResistance = True]
 * [EntropyInputLen = 128]
 * [NonceLen = 64]
 * [PersonalizationStringLen = 128]
 * [AdditionalInputLen = 128]
 * [ReturnedBitsLen = 640]
 *
 * COUNT = 0
 */
static const unsigned char drbg_hmac_sha1_pr_entropyin[] = {
    0x68, 0x0f, 0xac, 0xe9, 0x0d, 0x7b, 0xca, 0x21, 0xd4, 0xa0, 0xed, 0xb7,
    0x79, 0x9e, 0xe5, 0xd8
};
static const unsigned char drbg_hmac_sha1_pr_nonce[] = {
    0xb7, 0xbe, 0x9e, 0xed, 0xdd, 0x0e, 0x3b, 0x4b
};
static const unsigned char drbg_hmac_sha1_pr_persstr[] = {
    0xf5, 0x8c, 0x40, 0xae, 0x70, 0xf7, 0xa5, 0x56, 0x48, 0xa9, 0x31, 0xa0,
    0xa9, 0x31, 0x3d, 0xd7
};
static const unsigned char drbg_hmac_sha1_pr_entropyinpr0[] = {
    0x7c, 0xaf, 0xe2, 0x31, 0x63, 0x0a, 0xa9, 0x5a, 0x74, 0x2c, 0x4e, 0x5f,
    0x5f, 0x22, 0xc6, 0xa4
};
static const unsigned char drbg_hmac_sha1_pr_entropyinpr1[] = {
    0x1c, 0x0d, 0x77, 0x92, 0x89, 0x88, 0x27, 0x94, 0x8a, 0x58, 0x9f, 0x82,
    0x2d, 0x1a, 0xf7, 0xa6
};
static const unsigned char drbg_hmac_sha1_pr_addin0[] = {
    0xdc, 0x36, 0x63, 0xf0, 0x62, 0x78, 0x9c, 0xd1, 0x5c, 0xbb, 0x20, 0xc3,
    0xc1, 0x8c, 0xd9, 0xd7
};
static const unsigned char drbg_hmac_sha1_pr_addin1[] = {
    0xfe, 0x85, 0xb0, 0xab, 0x14, 0xc6, 0x96, 0xe6, 0x9c, 0x24, 0xe7, 0xb5,
    0xa1, 0x37, 0x12, 0x0c
};
static const unsigned char drbg_hmac_sha1_pr_expected[] = {
    0x68, 0x00, 0x4b, 0x3a, 0x28, 0xf7, 0xf0, 0x1c, 0xf9, 0xe9, 0xb5, 0x71,
    0x20, 0x79, 0xef, 0x80, 0x87, 0x1b, 0x08, 0xb9, 0xa9, 0x1b, 0xcd, 0x2b,
    0x9f, 0x09, 0x4d, 0xa4, 0x84, 0x80, 0xb3, 0x4c, 0xaf, 0xd5, 0x59, 0x6b,
    0x0c, 0x0a, 0x48, 0xe1, 0x48, 0xda, 0xbc, 0x6f, 0x77, 0xb8, 0xff, 0xaf,
    0x18, 0x70, 0x28, 0xe1, 0x04, 0x13, 0x7a, 0x4f, 0xeb, 0x1c, 0x72, 0xb0,
    0xc4, 0x4f, 0xe8, 0xb1, 0xaf, 0xab, 0xa5, 0xbc, 0xfd, 0x86, 0x67, 0xf2,
    0xf5, 0x5b, 0x46, 0x06, 0x63, 0x2e, 0x3c, 0xbc
};

static const ST_KAT_DRBG st_kat_drbg_tests[] =
{
    {
        OSSL_SELF_TEST_DESC_DRBG_HASH,
        "HASH-DRBG", "digest", "SHA256",
        ITM(drbg_hash_sha256_pr_entropyin),
        ITM(drbg_hash_sha256_pr_nonce),
        ITM(drbg_hash_sha256_pr_persstr),
        ITM(drbg_hash_sha256_pr_entropyinpr0),
        ITM(drbg_hash_sha256_pr_entropyinpr1),
        ITM(drbg_hash_sha256_pr_addin0),
        ITM(drbg_hash_sha256_pr_addin1),
        ITM(drbg_hash_sha256_pr_expected)
    },
    {
        OSSL_SELF_TEST_DESC_DRBG_CTR,
        "CTR-DRBG", "cipher", "AES-128-CTR",
        ITM(drbg_ctr_aes128_pr_df_entropyin),
        ITM(drbg_ctr_aes128_pr_df_nonce),
        ITM(drbg_ctr_aes128_pr_df_persstr),
        ITM(drbg_ctr_aes128_pr_df_entropyinpr0),
        ITM(drbg_ctr_aes128_pr_df_entropyinpr1),
        ITM(drbg_ctr_aes128_pr_df_addin0),
        ITM(drbg_ctr_aes128_pr_df_addin1),
        ITM(drbg_ctr_aes128_pr_df_expected)
    },
    {
        OSSL_SELF_TEST_DESC_DRBG_HMAC,
        "HMAC-DRBG", "digest", "SHA1",
        ITM(drbg_hmac_sha1_pr_entropyin),
        ITM(drbg_hmac_sha1_pr_nonce),
        ITM(drbg_hmac_sha1_pr_persstr),
        ITM(drbg_hmac_sha1_pr_entropyinpr0),
        ITM(drbg_hmac_sha1_pr_entropyinpr1),
        ITM(drbg_hmac_sha1_pr_addin0),
        ITM(drbg_hmac_sha1_pr_addin1),
        ITM(drbg_hmac_sha1_pr_expected)
    }
};

/* KEY EXCHANGE TEST DATA */

#ifndef OPENSSL_NO_DH
/* DH KAT */
static const unsigned char dh_p[] = {
    0xdc, 0xca, 0x15, 0x11, 0xb2, 0x31, 0x32, 0x25,
    0xf5, 0x21, 0x16, 0xe1, 0x54, 0x27, 0x89, 0xe0,
    0x01, 0xf0, 0x42, 0x5b, 0xcc, 0xc7, 0xf3, 0x66,
    0xf7, 0x40, 0x64, 0x07, 0xf1, 0xc9, 0xfa, 0x8b,
    0xe6, 0x10, 0xf1, 0x77, 0x8b, 0xb1, 0x70, 0xbe,
    0x39, 0xdb, 0xb7, 0x6f, 0x85, 0xbf, 0x24, 0xce,
    0x68, 0x80, 0xad, 0xb7, 0x62, 0x9f, 0x7c, 0x6d,
    0x01, 0x5e, 0x61, 0xd4, 0x3f, 0xa3, 0xee, 0x4d,
    0xe1, 0x85, 0xf2, 0xcf, 0xd0, 0x41, 0xff, 0xde,
    0x9d, 0x41, 0x84, 0x07, 0xe1, 0x51, 0x38, 0xbb,
    0x02, 0x1d, 0xae, 0xb3, 0x5f, 0x76, 0x2d, 0x17,
    0x82, 0xac, 0xc6, 0x58, 0xd3, 0x2b, 0xd4, 0xb0,
    0x23, 0x2c, 0x92, 0x7d, 0xd3, 0x8f, 0xa0, 0x97,
    0xb3, 0xd1, 0x85, 0x9f, 0xa8, 0xac, 0xaf, 0xb9,
    0x8f, 0x06, 0x66, 0x08, 0xfc, 0x64, 0x4e, 0xc7,
    0xdd, 0xb6, 0xf0, 0x85, 0x99, 0xf9, 0x2a, 0xc1,
    0xb5, 0x98, 0x25, 0xda, 0x84, 0x32, 0x07, 0x7d,
    0xef, 0x69, 0x56, 0x46, 0x06, 0x3c, 0x20, 0x82,
    0x3c, 0x95, 0x07, 0xab, 0x6f, 0x01, 0x76, 0xd4,
    0x73, 0x0d, 0x99, 0x0d, 0xbb, 0xe6, 0x36, 0x1c,
    0xd8, 0xb2, 0xb9, 0x4d, 0x3d, 0x2f, 0x32, 0x9b,
    0x82, 0x09, 0x9b, 0xd6, 0x61, 0xf4, 0x29, 0x50,
    0xf4, 0x03, 0xdf, 0x3e, 0xde, 0x62, 0xa3, 0x31,
    0x88, 0xb0, 0x27, 0x98, 0xba, 0x82, 0x3f, 0x44,
    0xb9, 0x46, 0xfe, 0x9d, 0xf6, 0x77, 0xa0, 0xc5,
    0xa1, 0x23, 0x8e, 0xaa, 0x97, 0xb7, 0x0f, 0x80,
    0xda, 0x8c, 0xac, 0x88, 0xe0, 0x92, 0xb1, 0x12,
    0x70, 0x60, 0xff, 0xbf, 0x45, 0x57, 0x99, 0x94,
    0x01, 0x1d, 0xc2, 0xfa, 0xa5, 0xe7, 0xf6, 0xc7,
    0x62, 0x45, 0xe1, 0xcc, 0x31, 0x22, 0x31, 0xc1,
    0x7d, 0x1c, 0xa6, 0xb1, 0x90, 0x07, 0xef, 0x0d,
    0xb9, 0x9f, 0x9c, 0xb6, 0x0e, 0x1d, 0x5f, 0x69
};
static const unsigned char dh_q[] = {
    0x89, 0x8b, 0x22, 0x67, 0x17, 0xef, 0x03, 0x9e,
    0x60, 0x3e, 0x82, 0xe5, 0xc7, 0xaf, 0xe4, 0x83,
    0x74, 0xac, 0x5f, 0x62, 0x5c, 0x54, 0xf1, 0xea,
    0x11, 0xac, 0xb5, 0x7d
};
static const unsigned char dh_g[] = {
    0x5e, 0xf7, 0xb8, 0x8f, 0x2d, 0xf6, 0x01, 0x39,
    0x35, 0x1d, 0xfb, 0xfe, 0x12, 0x66, 0x80, 0x5f,
    0xdf, 0x35, 0x6c, 0xdf, 0xd1, 0x3a, 0x4d, 0xa0,
    0x05, 0x0c, 0x7e, 0xde, 0x24, 0x6d, 0xf5, 0x9f,
    0x6a, 0xbf, 0x96, 0xad, 0xe5, 0xf2, 0xb2, 0x8f,
    0xfe, 0x88, 0xd6, 0xbc, 0xe7, 0xf7, 0x89, 0x4a,
    0x3d, 0x53, 0x5f, 0xc8, 0x21, 0x26, 0xdd, 0xd4,
    0x24, 0x87, 0x2e, 0x16, 0xb8, 0x38, 0xdf, 0x8c,
    0x51, 0xe9, 0x01, 0x6f, 0x88, 0x9c, 0x7c, 0x20,
    0x3e, 0x98, 0xa8, 0xb6, 0x31, 0xf9, 0xc7, 0x25,
    0x63, 0xd3, 0x8a, 0x49, 0x58, 0x9a, 0x07, 0x53,
    0xd3, 0x58, 0xe7, 0x83, 0x31, 0x8c, 0xef, 0xd9,
    0x67, 0x7c, 0x7b, 0x2d, 0xbb, 0x77, 0xd6, 0xdc,
    0xe2, 0xa1, 0x96, 0x37, 0x95, 0xca, 0x64, 0xb9,
    0x2d, 0x1c, 0x9a, 0xac, 0x6d, 0x0e, 0x8d, 0x43,
    0x1d, 0xe5, 0xe5, 0x00, 0x60, 0xdf, 0xf7, 0x86,
    0x89, 0xc9, 0xec, 0xa1, 0xc1, 0x24, 0x8c, 0x16,
    0xed, 0x09, 0xc7, 0xad, 0x41, 0x2a, 0x17, 0x40,
    0x6d, 0x2b, 0x52, 0x5a, 0xa1, 0xca, 0xbb, 0x23,
    0x7b, 0x97, 0x34, 0xec, 0x7b, 0x8c, 0xe3, 0xfa,
    0xe0, 0x2f, 0x29, 0xc5, 0xef, 0xed, 0x30, 0xd6,
    0x91, 0x87, 0xda, 0x10, 0x9c, 0x2c, 0x9f, 0xe2,
    0xaa, 0xdb, 0xb0, 0xc2, 0x2a, 0xf5, 0x4c, 0x61,
    0x66, 0x55, 0x00, 0x0c, 0x43, 0x1c, 0x6b, 0x4a,
    0x37, 0x97, 0x63, 0xb0, 0xa9, 0x16, 0x58, 0xef,
    0xc8, 0x4e, 0x8b, 0x06, 0x35, 0x8c, 0x8b, 0x4f,
    0x21, 0x37, 0x10, 0xfd, 0x10, 0x17, 0x2c, 0xf3,
    0x9b, 0x83, 0x0c, 0x2d, 0xd8, 0x4a, 0x0c, 0x8a,
    0xb8, 0x25, 0x16, 0xec, 0xab, 0x99, 0x5f, 0xa4,
    0x21, 0x5e, 0x02, 0x3e, 0x4e, 0xcf, 0x80, 0x74,
    0xc3, 0x9d, 0x6c, 0x88, 0xb7, 0x0d, 0x1e, 0xe4,
    0xe9, 0x6f, 0xdc, 0x20, 0xea, 0x11, 0x5c, 0x32
};
static const unsigned char dh_priv[] = {
    0x14, 0x33, 0xe0, 0xb5, 0xa9, 0x17, 0xb6, 0x0a,
    0x30, 0x23, 0xf2, 0xf8, 0xaa, 0x2c, 0x2d, 0x70,
    0xd2, 0x96, 0x8a, 0xba, 0x9a, 0xea, 0xc8, 0x15,
    0x40, 0xb8, 0xfc, 0xe6
};
static const unsigned char dh_pub[] = {
    0x95, 0xdd, 0x33, 0x8d, 0x29, 0xe5, 0x71, 0x04,
    0x92, 0xb9, 0x18, 0x31, 0x7b, 0x72, 0xa3, 0x69,
    0x36, 0xe1, 0x95, 0x1a, 0x2e, 0xe5, 0xa5, 0x59,
    0x16, 0x99, 0xc0, 0x48, 0x6d, 0x0d, 0x4f, 0x9b,
    0xdd, 0x6d, 0x5a, 0x3f, 0x6b, 0x98, 0x89, 0x0c,
    0x62, 0xb3, 0x76, 0x52, 0xd3, 0x6e, 0x71, 0x21,
    0x11, 0xe6, 0x8a, 0x73, 0x55, 0x37, 0x25, 0x06,
    0x99, 0xef, 0xe3, 0x30, 0x53, 0x73, 0x91, 0xfb,
    0xc2, 0xc5, 0x48, 0xbc, 0x5a, 0xc3, 0xe5, 0xb2,
    0x33, 0x86, 0xc3, 0xee, 0xf5, 0xeb, 0x43, 0xc0,
    0x99, 0xd7, 0x0a, 0x52, 0x02, 0x68, 0x7e, 0x83,
    0x96, 0x42, 0x48, 0xfc, 0xa9, 0x1f, 0x40, 0x90,
    0x8e, 0x8f, 0xb3, 0x31, 0x93, 0x15, 0xf6, 0xd2,
    0x60, 0x6d, 0x7f, 0x7c, 0xd5, 0x2c, 0xc6, 0xe7,
    0xc5, 0x84, 0x3a, 0xfb, 0x22, 0x51, 0x9c, 0xf0,
    0xf0, 0xf9, 0xd3, 0xa0, 0xa4, 0xe8, 0xc8, 0x88,
    0x99, 0xef, 0xed, 0xe7, 0x36, 0x43, 0x51, 0xfb,
    0x6a, 0x36, 0x3e, 0xe7, 0x17, 0xe5, 0x44, 0x5a,
    0xda, 0xb4, 0xc9, 0x31, 0xa6, 0x48, 0x39, 0x97,
    0xb8, 0x7d, 0xad, 0x83, 0x67, 0x7e, 0x4d, 0x1d,
    0x3a, 0x77, 0x75, 0xe0, 0xf6, 0xd0, 0x0f, 0xdf,
    0x73, 0xc7, 0xad, 0x80, 0x1e, 0x66, 0x5a, 0x0e,
    0x5a, 0x79, 0x6d, 0x0a, 0x03, 0x80, 0xa1, 0x9f,
    0xa1, 0x82, 0xef, 0xc8, 0xa0, 0x4f, 0x5e, 0x4d,
    0xb9, 0x0d, 0x1a, 0x86, 0x37, 0xf9, 0x5d, 0xb1,
    0x64, 0x36, 0xbd, 0xc8, 0xf3, 0xfc, 0x09, 0x6c,
    0x4f, 0xf7, 0xf2, 0x34, 0xbe, 0x8f, 0xef, 0x47,
    0x9a, 0xc4, 0xb0, 0xdc, 0x4b, 0x77, 0x26, 0x3e,
    0x07, 0xd9, 0x95, 0x9d, 0xe0, 0xf1, 0xbf, 0x3f,
    0x0a, 0xe3, 0xd9, 0xd5, 0x0e, 0x4b, 0x89, 0xc9,
    0x9e, 0x3e, 0xa1, 0x21, 0x73, 0x43, 0xdd, 0x8c,
    0x65, 0x81, 0xac, 0xc4, 0x95, 0x9c, 0x91, 0xd3
};
static const unsigned char dh_peer_pub[] = {
    0x1f, 0xc1, 0xda, 0x34, 0x1d, 0x1a, 0x84, 0x6a,
    0x96, 0xb7, 0xbe, 0x24, 0x34, 0x0f, 0x87, 0x7d,
    0xd0, 0x10, 0xaa, 0x03, 0x56, 0xd5, 0xad, 0x58,
    0xaa, 0xe9, 0xc7, 0xb0, 0x8f, 0x74, 0x9a, 0x32,
    0x23, 0x51, 0x10, 0xb5, 0xd8, 0x8e, 0xb5, 0xdb,
    0xfa, 0x97, 0x8d, 0x27, 0xec, 0xc5, 0x30, 0xf0,
    0x2d, 0x31, 0x14, 0x00, 0x5b, 0x64, 0xb1, 0xc0,
    0xe0, 0x24, 0xcb, 0x8a, 0xe2, 0x16, 0x98, 0xbc,
    0xa9, 0xe6, 0x0d, 0x42, 0x80, 0x86, 0x22, 0xf1,
    0x81, 0xc5, 0x6e, 0x1d, 0xe7, 0xa9, 0x6e, 0x6e,
    0xfe, 0xe9, 0xd6, 0x65, 0x67, 0xe9, 0x1b, 0x97,
    0x70, 0x42, 0xc7, 0xe3, 0xd0, 0x44, 0x8f, 0x05,
    0xfb, 0x77, 0xf5, 0x22, 0xb9, 0xbf, 0xc8, 0xd3,
    0x3c, 0xc3, 0xc3, 0x1e, 0xd3, 0xb3, 0x1f, 0x0f,
    0xec, 0xb6, 0xdb, 0x4f, 0x6e, 0xa3, 0x11, 0xe7,
    0x7a, 0xfd, 0xbc, 0xd4, 0x7a, 0xee, 0x1b, 0xb1,
    0x50, 0xf2, 0x16, 0x87, 0x35, 0x78, 0xfb, 0x96,
    0x46, 0x8e, 0x8f, 0x9f, 0x3d, 0xe8, 0xef, 0xbf,
    0xce, 0x75, 0x62, 0x4b, 0x1d, 0xf0, 0x53, 0x22,
    0xa3, 0x4f, 0x14, 0x63, 0xe8, 0x39, 0xe8, 0x98,
    0x4c, 0x4a, 0xd0, 0xa9, 0x6e, 0x1a, 0xc8, 0x42,
    0xe5, 0x31, 0x8c, 0xc2, 0x3c, 0x06, 0x2a, 0x8c,
    0xa1, 0x71, 0xb8, 0xd5, 0x75, 0x98, 0x0d, 0xde,
    0x7f, 0xc5, 0x6f, 0x15, 0x36, 0x52, 0x38, 0x20,
    0xd4, 0x31, 0x92, 0xbf, 0xd5, 0x1e, 0x8e, 0x22,
    0x89, 0x78, 0xac, 0xa5, 0xb9, 0x44, 0x72, 0xf3,
    0x39, 0xca, 0xeb, 0x99, 0x31, 0xb4, 0x2b, 0xe3,
    0x01, 0x26, 0x8b, 0xc9, 0x97, 0x89, 0xc9, 0xb2,
    0x55, 0x71, 0xc3, 0xc0, 0xe4, 0xcb, 0x3f, 0x00,
    0x7f, 0x1a, 0x51, 0x1c, 0xbb, 0x53, 0xc8, 0x51,
    0x9c, 0xdd, 0x13, 0x02, 0xab, 0xca, 0x6c, 0x0f,
    0x34, 0xf9, 0x67, 0x39, 0xf1, 0x7f, 0xf4, 0x8b
};

static const unsigned char dh_secret_expected[] = {
    0x08, 0xff, 0x33, 0xbb, 0x2e, 0xcf, 0xf4, 0x9a,
    0x7d, 0x4a, 0x79, 0x12, 0xae, 0xb1, 0xbb, 0x6a,
    0xb5, 0x11, 0x64, 0x1b, 0x4a, 0x76, 0x77, 0x0c,
    0x8c, 0xc1, 0xbc, 0xc2, 0x33, 0x34, 0x3d, 0xfe,
    0x70, 0x0d, 0x11, 0x81, 0x3d, 0x2c, 0x9e, 0xd2,
    0x3b, 0x21, 0x1c, 0xa9, 0xe8, 0x78, 0x69, 0x21,
    0xed, 0xca, 0x28, 0x3c, 0x68, 0xb1, 0x61, 0x53,
    0xfa, 0x01, 0xe9, 0x1a, 0xb8, 0x2c, 0x90, 0xdd,
    0xab, 0x4a, 0x95, 0x81, 0x67, 0x70, 0xa9, 0x87,
    0x10, 0xe1, 0x4c, 0x92, 0xab, 0x83, 0xb6, 0xe4,
    0x6e, 0x1e, 0x42, 0x6e, 0xe8, 0x52, 0x43, 0x0d,
    0x61, 0x87, 0xda, 0xa3, 0x72, 0x0a, 0x6b, 0xcd,
    0x73, 0x23, 0x5c, 0x6b, 0x0f, 0x94, 0x1f, 0x33,
    0x64, 0xf5, 0x04, 0x20, 0x55, 0x1a, 0x4b, 0xfe,
    0xaf, 0xe2, 0xbc, 0x43, 0x85, 0x05, 0xa5, 0x9a,
    0x4a, 0x40, 0xda, 0xca, 0x7a, 0x89, 0x5a, 0x73,
    0xdb, 0x57, 0x5c, 0x74, 0xc1, 0x3a, 0x23, 0xad,
    0x88, 0x32, 0x95, 0x7d, 0x58, 0x2d, 0x38, 0xf0,
    0xa6, 0x16, 0x5f, 0xb0, 0xd7, 0xe9, 0xb8, 0x79,
    0x9e, 0x42, 0xfd, 0x32, 0x20, 0xe3, 0x32, 0xe9,
    0x81, 0x85, 0xa0, 0xc9, 0x42, 0x97, 0x57, 0xb2,
    0xd0, 0xd0, 0x2c, 0x17, 0xdb, 0xaa, 0x1f, 0xf6,
    0xed, 0x93, 0xd7, 0xe7, 0x3e, 0x24, 0x1e, 0xae,
    0xd9, 0x0c, 0xaf, 0x39, 0x4d, 0x2b, 0xc6, 0x57,
    0x0f, 0x18, 0xc8, 0x1f, 0x2b, 0xe5, 0xd0, 0x1a,
    0x2c, 0xa9, 0x9f, 0xf1, 0x42, 0xb5, 0xd9, 0x63,
    0xf9, 0xf5, 0x00, 0x32, 0x5e, 0x75, 0x56, 0xf9,
    0x58, 0x49, 0xb3, 0xff, 0xc7, 0x47, 0x94, 0x86,
    0xbe, 0x1d, 0x45, 0x96, 0xa3, 0x10, 0x6b, 0xd5,
    0xcb, 0x4f, 0x61, 0xc5, 0x7e, 0xc5, 0xf1, 0x00,
    0xfb, 0x7a, 0x0c, 0x82, 0xa1, 0x0b, 0x82, 0x52,
    0x6a, 0x97, 0xd1, 0xd9, 0x7d, 0x98, 0xea, 0xf6
};

static const ST_KAT_PARAM dh_group[] = {
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_FFC_P, dh_p),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_FFC_Q, dh_q),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_FFC_G, dh_g),
    ST_KAT_PARAM_END()
};

/* The host's private key */
static const ST_KAT_PARAM dh_host_key[] = {
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_PUB_KEY, dh_pub),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_PRIV_KEY, dh_priv),
    ST_KAT_PARAM_END()
};

/* The peer's public key */
static const ST_KAT_PARAM dh_peer_key[] = {
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_PUB_KEY, dh_peer_pub),
    ST_KAT_PARAM_END()
};
#endif /* OPENSSL_NO_DH */


#ifndef OPENSSL_NO_EC
static const char ecdh_curve_name[] = "prime256v1";
static const unsigned char ecdh_privd[] = {
    0x33, 0xd0, 0x43, 0x83, 0xa9, 0x89, 0x56, 0x03,
    0xd2, 0xd7, 0xfe, 0x6b, 0x01, 0x6f, 0xe4, 0x59,
    0xcc, 0x0d, 0x9a, 0x24, 0x6c, 0x86, 0x1b, 0x2e,
    0xdc, 0x4b, 0x4d, 0x35, 0x43, 0xe1, 0x1b, 0xad
};
static const unsigned char ecdh_pub[] = {
    0x04,
    0x1b, 0x93, 0x67, 0x55, 0x1c, 0x55, 0x9f, 0x63,
    0xd1, 0x22, 0xa4, 0xd8, 0xd1, 0x0a, 0x60, 0x6d,
    0x02, 0xa5, 0x77, 0x57, 0xc8, 0xa3, 0x47, 0x73,
    0x3a, 0x6a, 0x08, 0x28, 0x39, 0xbd, 0xc9, 0xd2,
    0x80, 0xec, 0xe9, 0xa7, 0x08, 0x29, 0x71, 0x2f,
    0xc9, 0x56, 0x82, 0xee, 0x9a, 0x85, 0x0f, 0x6d,
    0x7f, 0x59, 0x5f, 0x8c, 0xd1, 0x96, 0x0b, 0xdf,
    0x29, 0x3e, 0x49, 0x07, 0x88, 0x3f, 0x9a, 0x29
};
static const unsigned char ecdh_peer_pub[] = {
    0x04,
    0x1f, 0x72, 0xbd, 0x2a, 0x3e, 0xeb, 0x6c, 0x76,
    0xe5, 0x5d, 0x69, 0x75, 0x24, 0xbf, 0x2f, 0x5b,
    0x96, 0xb2, 0x91, 0x62, 0x06, 0x35, 0xcc, 0xb2,
    0x4b, 0x31, 0x1b, 0x0c, 0x6f, 0x06, 0x9f, 0x86,
    0xcf, 0xc8, 0xac, 0xd5, 0x4f, 0x4d, 0x77, 0xf3,
    0x70, 0x4a, 0x8f, 0x04, 0x9a, 0xb1, 0x03, 0xc7,
    0xeb, 0xd5, 0x94, 0x78, 0x61, 0xab, 0x78, 0x0c,
    0x4a, 0x2d, 0x6b, 0xf3, 0x2f, 0x2e, 0x4a, 0xbc
};

static const ST_KAT_PARAM ecdh_group[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_PKEY_PARAM_GROUP_NAME, ecdh_curve_name),
    ST_KAT_PARAM_END()
};
static const ST_KAT_PARAM ecdh_host_key[] = {
    ST_KAT_PARAM_OCTET(OSSL_PKEY_PARAM_PUB_KEY, ecdh_pub),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_PRIV_KEY, ecdh_privd),
    ST_KAT_PARAM_END()
};
static const ST_KAT_PARAM ecdh_peer_key[] = {
    ST_KAT_PARAM_OCTET(OSSL_PKEY_PARAM_PUB_KEY, ecdh_peer_pub),
    ST_KAT_PARAM_END()
};
static const unsigned char ecdh_secret_expected[] = {
    0x45, 0x2a, 0x2f, 0x0d, 0x24, 0xe6, 0x8d, 0xd0,
    0xda, 0x59, 0x7b, 0x0c, 0xec, 0x9b, 0x4c, 0x38,
    0x41, 0xdd, 0xce, 0xb3, 0xcc, 0xf1, 0x90, 0x8e,
    0x30, 0xdb, 0x5b, 0x5f, 0x97, 0xea, 0xe0, 0xc2
};
#endif /* OPENSSL_NO_EC */

#if !defined(OPENSSL_NO_DH) || !defined(OPENSSL_NO_EC)
static const ST_KAT_KAS st_kat_kas_tests[] =
{
# ifndef OPENSSL_NO_DH
    {
        OSSL_SELF_TEST_DESC_KA_DH,
        "DH",
        dh_group,
        dh_host_key,
        dh_peer_key,
        ITM(dh_secret_expected)
    },
# endif /* OPENSSL_NO_DH */
# ifndef OPENSSL_NO_EC
    {
        OSSL_SELF_TEST_DESC_KA_ECDH,
        "EC",
        ecdh_group,
        ecdh_host_key,
        ecdh_peer_key,
        ITM(ecdh_secret_expected)
    },
# endif /* OPENSSL_NO_EC */
};
#endif /* !defined(OPENSSL_NO_DH) || !defined(OPENSSL_NO_EC) */

/* RSA key data */
static const unsigned char rsa_n[] = {
    0xDB, 0x10, 0x1A, 0xC2, 0xA3, 0xF1, 0xDC, 0xFF,
    0x13, 0x6B, 0xED, 0x44, 0xDF, 0xF0, 0x02, 0x6D,
    0x13, 0xC7, 0x88, 0xDA, 0x70, 0x6B, 0x54, 0xF1,
    0xE8, 0x27, 0xDC, 0xC3, 0x0F, 0x99, 0x6A, 0xFA,
    0xC6, 0x67, 0xFF, 0x1D, 0x1E, 0x3C, 0x1D, 0xC1,
    0xB5, 0x5F, 0x6C, 0xC0, 0xB2, 0x07, 0x3A, 0x6D,
    0x41, 0xE4, 0x25, 0x99, 0xAC, 0xFC, 0xD2, 0x0F,
    0x02, 0xD3, 0xD1, 0x54, 0x06, 0x1A, 0x51, 0x77,
    0xBD, 0xB6, 0xBF, 0xEA, 0xA7, 0x5C, 0x06, 0xA9,
    0x5D, 0x69, 0x84, 0x45, 0xD7, 0xF5, 0x05, 0xBA,
    0x47, 0xF0, 0x1B, 0xD7, 0x2B, 0x24, 0xEC, 0xCB,
    0x9B, 0x1B, 0x10, 0x8D, 0x81, 0xA0, 0xBE, 0xB1,
    0x8C, 0x33, 0xE4, 0x36, 0xB8, 0x43, 0xEB, 0x19,
    0x2A, 0x81, 0x8D, 0xDE, 0x81, 0x0A, 0x99, 0x48,
    0xB6, 0xF6, 0xBC, 0xCD, 0x49, 0x34, 0x3A, 0x8F,
    0x26, 0x94, 0xE3, 0x28, 0x82, 0x1A, 0x7C, 0x8F,
    0x59, 0x9F, 0x45, 0xE8, 0x5D, 0x1A, 0x45, 0x76,
    0x04, 0x56, 0x05, 0xA1, 0xD0, 0x1B, 0x8C, 0x77,
    0x6D, 0xAF, 0x53, 0xFA, 0x71, 0xE2, 0x67, 0xE0,
    0x9A, 0xFE, 0x03, 0xA9, 0x85, 0xD2, 0xC9, 0xAA,
    0xBA, 0x2A, 0xBC, 0xF4, 0xA0, 0x08, 0xF5, 0x13,
    0x98, 0x13, 0x5D, 0xF0, 0xD9, 0x33, 0x34, 0x2A,
    0x61, 0xC3, 0x89, 0x55, 0xF0, 0xAE, 0x1A, 0x9C,
    0x22, 0xEE, 0x19, 0x05, 0x8D, 0x32, 0xFE, 0xEC,
    0x9C, 0x84, 0xBA, 0xB7, 0xF9, 0x6C, 0x3A, 0x4F,
    0x07, 0xFC, 0x45, 0xEB, 0x12, 0xE5, 0x7B, 0xFD,
    0x55, 0xE6, 0x29, 0x69, 0xD1, 0xC2, 0xE8, 0xB9,
    0x78, 0x59, 0xF6, 0x79, 0x10, 0xC6, 0x4E, 0xEB,
    0x6A, 0x5E, 0xB9, 0x9A, 0xC7, 0xC4, 0x5B, 0x63,
    0xDA, 0xA3, 0x3F, 0x5E, 0x92, 0x7A, 0x81, 0x5E,
    0xD6, 0xB0, 0xE2, 0x62, 0x8F, 0x74, 0x26, 0xC2,
    0x0C, 0xD3, 0x9A, 0x17, 0x47, 0xE6, 0x8E, 0xAB
};
static const unsigned char rsa_e[] = { 0x01, 0x00, 0x01 };
static const unsigned char rsa_d[] = {
    0x52, 0x41, 0xF4, 0xDA, 0x7B, 0xB7, 0x59, 0x55,
    0xCA, 0xD4, 0x2F, 0x0F, 0x3A, 0xCB, 0xA4, 0x0D,
    0x93, 0x6C, 0xCC, 0x9D, 0xC1, 0xB2, 0xFB, 0xFD,
    0xAE, 0x40, 0x31, 0xAC, 0x69, 0x52, 0x21, 0x92,
    0xB3, 0x27, 0xDF, 0xEA, 0xEE, 0x2C, 0x82, 0xBB,
    0xF7, 0x40, 0x32, 0xD5, 0x14, 0xC4, 0x94, 0x12,
    0xEC, 0xB8, 0x1F, 0xCA, 0x59, 0xE3, 0xC1, 0x78,
    0xF3, 0x85, 0xD8, 0x47, 0xA5, 0xD7, 0x02, 0x1A,
    0x65, 0x79, 0x97, 0x0D, 0x24, 0xF4, 0xF0, 0x67,
    0x6E, 0x75, 0x2D, 0xBF, 0x10, 0x3D, 0xA8, 0x7D,
    0xEF, 0x7F, 0x60, 0xE4, 0xE6, 0x05, 0x82, 0x89,
    0x5D, 0xDF, 0xC6, 0xD2, 0x6C, 0x07, 0x91, 0x33,
    0x98, 0x42, 0xF0, 0x02, 0x00, 0x25, 0x38, 0xC5,
    0x85, 0x69, 0x8A, 0x7D, 0x2F, 0x95, 0x6C, 0x43,
    0x9A, 0xB8, 0x81, 0xE2, 0xD0, 0x07, 0x35, 0xAA,
    0x05, 0x41, 0xC9, 0x1E, 0xAF, 0xE4, 0x04, 0x3B,
    0x19, 0xB8, 0x73, 0xA2, 0xAC, 0x4B, 0x1E, 0x66,
    0x48, 0xD8, 0x72, 0x1F, 0xAC, 0xF6, 0xCB, 0xBC,
    0x90, 0x09, 0xCA, 0xEC, 0x0C, 0xDC, 0xF9, 0x2C,
    0xD7, 0xEB, 0xAE, 0xA3, 0xA4, 0x47, 0xD7, 0x33,
    0x2F, 0x8A, 0xCA, 0xBC, 0x5E, 0xF0, 0x77, 0xE4,
    0x97, 0x98, 0x97, 0xC7, 0x10, 0x91, 0x7D, 0x2A,
    0xA6, 0xFF, 0x46, 0x83, 0x97, 0xDE, 0xE9, 0xE2,
    0x17, 0x03, 0x06, 0x14, 0xE2, 0xD7, 0xB1, 0x1D,
    0x77, 0xAF, 0x51, 0x27, 0x5B, 0x5E, 0x69, 0xB8,
    0x81, 0xE6, 0x11, 0xC5, 0x43, 0x23, 0x81, 0x04,
    0x62, 0xFF, 0xE9, 0x46, 0xB8, 0xD8, 0x44, 0xDB,
    0xA5, 0xCC, 0x31, 0x54, 0x34, 0xCE, 0x3E, 0x82,
    0xD6, 0xBF, 0x7A, 0x0B, 0x64, 0x21, 0x6D, 0x88,
    0x7E, 0x5B, 0x45, 0x12, 0x1E, 0x63, 0x8D, 0x49,
    0xA7, 0x1D, 0xD9, 0x1E, 0x06, 0xCD, 0xE8, 0xBA,
    0x2C, 0x8C, 0x69, 0x32, 0xEA, 0xBE, 0x60, 0x71
};
static const unsigned char rsa_p[] = {
    0xFA, 0xAC, 0xE1, 0x37, 0x5E, 0x32, 0x11, 0x34,
    0xC6, 0x72, 0x58, 0x2D, 0x91, 0x06, 0x3E, 0x77,
    0xE7, 0x11, 0x21, 0xCD, 0x4A, 0xF8, 0xA4, 0x3F,
    0x0F, 0xEF, 0x31, 0xE3, 0xF3, 0x55, 0xA0, 0xB9,
    0xAC, 0xB6, 0xCB, 0xBB, 0x41, 0xD0, 0x32, 0x81,
    0x9A, 0x8F, 0x7A, 0x99, 0x30, 0x77, 0x6C, 0x68,
    0x27, 0xE2, 0x96, 0xB5, 0x72, 0xC9, 0xC3, 0xD4,
    0x42, 0xAA, 0xAA, 0xCA, 0x95, 0x8F, 0xFF, 0xC9,
    0x9B, 0x52, 0x34, 0x30, 0x1D, 0xCF, 0xFE, 0xCF,
    0x3C, 0x56, 0x68, 0x6E, 0xEF, 0xE7, 0x6C, 0xD7,
    0xFB, 0x99, 0xF5, 0x4A, 0xA5, 0x21, 0x1F, 0x2B,
    0xEA, 0x93, 0xE8, 0x98, 0x26, 0xC4, 0x6E, 0x42,
    0x21, 0x5E, 0xA0, 0xA1, 0x2A, 0x58, 0x35, 0xBB,
    0x10, 0xE7, 0xBA, 0x27, 0x0A, 0x3B, 0xB3, 0xAF,
    0xE2, 0x75, 0x36, 0x04, 0xAC, 0x56, 0xA0, 0xAB,
    0x52, 0xDE, 0xCE, 0xDD, 0x2C, 0x28, 0x77, 0x03
};
static const unsigned char rsa_q[] = {
    0xDF, 0xB7, 0x52, 0xB6, 0xD7, 0xC0, 0xE2, 0x96,
    0xE7, 0xC9, 0xFE, 0x5D, 0x71, 0x5A, 0xC4, 0x40,
    0x96, 0x2F, 0xE5, 0x87, 0xEA, 0xF3, 0xA5, 0x77,
    0x11, 0x67, 0x3C, 0x8D, 0x56, 0x08, 0xA7, 0xB5,
    0x67, 0xFA, 0x37, 0xA8, 0xB8, 0xCF, 0x61, 0xE8,
    0x63, 0xD8, 0x38, 0x06, 0x21, 0x2B, 0x92, 0x09,
    0xA6, 0x39, 0x3A, 0xEA, 0xA8, 0xB4, 0x45, 0x4B,
    0x36, 0x10, 0x4C, 0xE4, 0x00, 0x66, 0x71, 0x65,
    0xF8, 0x0B, 0x94, 0x59, 0x4F, 0x8C, 0xFD, 0xD5,
    0x34, 0xA2, 0xE7, 0x62, 0x84, 0x0A, 0xA7, 0xBB,
    0xDB, 0xD9, 0x8A, 0xCD, 0x05, 0xE1, 0xCC, 0x57,
    0x7B, 0xF1, 0xF1, 0x1F, 0x11, 0x9D, 0xBA, 0x3E,
    0x45, 0x18, 0x99, 0x1B, 0x41, 0x64, 0x43, 0xEE,
    0x97, 0x5D, 0x77, 0x13, 0x5B, 0x74, 0x69, 0x73,
    0x87, 0x95, 0x05, 0x07, 0xBE, 0x45, 0x07, 0x17,
    0x7E, 0x4A, 0x69, 0x22, 0xF3, 0xDB, 0x05, 0x39
};
static const unsigned char rsa_dp[] = {
    0x5E, 0xD8, 0xDC, 0xDA, 0x53, 0x44, 0xC4, 0x67,
    0xE0, 0x92, 0x51, 0x34, 0xE4, 0x83, 0xA5, 0x4D,
    0x3E, 0xDB, 0xA7, 0x9B, 0x82, 0xBB, 0x73, 0x81,
    0xFC, 0xE8, 0x77, 0x4B, 0x15, 0xBE, 0x17, 0x73,
    0x49, 0x9B, 0x5C, 0x98, 0xBC, 0xBD, 0x26, 0xEF,
    0x0C, 0xE9, 0x2E, 0xED, 0x19, 0x7E, 0x86, 0x41,
    0x1E, 0x9E, 0x48, 0x81, 0xDD, 0x2D, 0xE4, 0x6F,
    0xC2, 0xCD, 0xCA, 0x93, 0x9E, 0x65, 0x7E, 0xD5,
    0xEC, 0x73, 0xFD, 0x15, 0x1B, 0xA2, 0xA0, 0x7A,
    0x0F, 0x0D, 0x6E, 0xB4, 0x53, 0x07, 0x90, 0x92,
    0x64, 0x3B, 0x8B, 0xA9, 0x33, 0xB3, 0xC5, 0x94,
    0x9B, 0x4C, 0x5D, 0x9C, 0x7C, 0x46, 0xA4, 0xA5,
    0x56, 0xF4, 0xF3, 0xF8, 0x27, 0x0A, 0x7B, 0x42,
    0x0D, 0x92, 0x70, 0x47, 0xE7, 0x42, 0x51, 0xA9,
    0xC2, 0x18, 0xB1, 0x58, 0xB1, 0x50, 0x91, 0xB8,
    0x61, 0x41, 0xB6, 0xA9, 0xCE, 0xD4, 0x7C, 0xBB
};
static const unsigned char rsa_dq[] = {
    0x54, 0x09, 0x1F, 0x0F, 0x03, 0xD8, 0xB6, 0xC5,
    0x0C, 0xE8, 0xB9, 0x9E, 0x0C, 0x38, 0x96, 0x43,
    0xD4, 0xA6, 0xC5, 0x47, 0xDB, 0x20, 0x0E, 0xE5,
    0xBD, 0x29, 0xD4, 0x7B, 0x1A, 0xF8, 0x41, 0x57,
    0x49, 0x69, 0x9A, 0x82, 0xCC, 0x79, 0x4A, 0x43,
    0xEB, 0x4D, 0x8B, 0x2D, 0xF2, 0x43, 0xD5, 0xA5,
    0xBE, 0x44, 0xFD, 0x36, 0xAC, 0x8C, 0x9B, 0x02,
    0xF7, 0x9A, 0x03, 0xE8, 0x19, 0xA6, 0x61, 0xAE,
    0x76, 0x10, 0x93, 0x77, 0x41, 0x04, 0xAB, 0x4C,
    0xED, 0x6A, 0xCC, 0x14, 0x1B, 0x99, 0x8D, 0x0C,
    0x6A, 0x37, 0x3B, 0x86, 0x6C, 0x51, 0x37, 0x5B,
    0x1D, 0x79, 0xF2, 0xA3, 0x43, 0x10, 0xC6, 0xA7,
    0x21, 0x79, 0x6D, 0xF9, 0xE9, 0x04, 0x6A, 0xE8,
    0x32, 0xFF, 0xAE, 0xFD, 0x1C, 0x7B, 0x8C, 0x29,
    0x13, 0xA3, 0x0C, 0xB2, 0xAD, 0xEC, 0x6C, 0x0F,
    0x8D, 0x27, 0x12, 0x7B, 0x48, 0xB2, 0xDB, 0x31
};
static const unsigned char rsa_qInv[] = {
    0x8D, 0x1B, 0x05, 0xCA, 0x24, 0x1F, 0x0C, 0x53,
    0x19, 0x52, 0x74, 0x63, 0x21, 0xFA, 0x78, 0x46,
    0x79, 0xAF, 0x5C, 0xDE, 0x30, 0xA4, 0x6C, 0x20,
    0x38, 0xE6, 0x97, 0x39, 0xB8, 0x7A, 0x70, 0x0D,
    0x8B, 0x6C, 0x6D, 0x13, 0x74, 0xD5, 0x1C, 0xDE,
    0xA9, 0xF4, 0x60, 0x37, 0xFE, 0x68, 0x77, 0x5E,
    0x0B, 0x4E, 0x5E, 0x03, 0x31, 0x30, 0xDF, 0xD6,
    0xAE, 0x85, 0xD0, 0x81, 0xBB, 0x61, 0xC7, 0xB1,
    0x04, 0x5A, 0xC4, 0x6D, 0x56, 0x1C, 0xD9, 0x64,
    0xE7, 0x85, 0x7F, 0x88, 0x91, 0xC9, 0x60, 0x28,
    0x05, 0xE2, 0xC6, 0x24, 0x8F, 0xDD, 0x61, 0x64,
    0xD8, 0x09, 0xDE, 0x7E, 0xD3, 0x4A, 0x61, 0x1A,
    0xD3, 0x73, 0x58, 0x4B, 0xD8, 0xA0, 0x54, 0x25,
    0x48, 0x83, 0x6F, 0x82, 0x6C, 0xAF, 0x36, 0x51,
    0x2A, 0x5D, 0x14, 0x2F, 0x41, 0x25, 0x00, 0xDD,
    0xF8, 0xF3, 0x95, 0xFE, 0x31, 0x25, 0x50, 0x12
};

static const ST_KAT_PARAM rsa_crt_key[] = {
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_N, rsa_n),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_E, rsa_e),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_D, rsa_d),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_FACTOR1, rsa_p),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_FACTOR2, rsa_q),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_EXPONENT1, rsa_dp),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_EXPONENT2, rsa_dq),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_COEFFICIENT1, rsa_qInv),
    ST_KAT_PARAM_END()
};

static const ST_KAT_PARAM rsa_pub_key[] = {
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_N, rsa_n),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_E, rsa_e),
    ST_KAT_PARAM_END()
};

static const ST_KAT_PARAM rsa_priv_key[] = {
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_N, rsa_n),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_E, rsa_e),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_RSA_D, rsa_d),
    ST_KAT_PARAM_END()
};

/*-
 * Using OSSL_PKEY_RSA_PAD_MODE_NONE directly in the expansion of the
 * ST_KAT_PARAM_UTF8STRING macro below causes a failure on ancient
 * HP/UX PA-RISC compilers.
 */
static const char pad_mode_none[] = OSSL_PKEY_RSA_PAD_MODE_NONE;

static const ST_KAT_PARAM rsa_enc_params[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_ASYM_CIPHER_PARAM_PAD_MODE, pad_mode_none),
    ST_KAT_PARAM_END()
};

static const unsigned char rsa_expected_sig[256] = {
    0xad, 0xbe, 0x2a, 0xaf, 0x16, 0x85, 0xc5, 0x00,
    0x91, 0x3e, 0xd0, 0x49, 0xfb, 0x3a, 0x81, 0xb9,
    0x6c, 0x28, 0xbc, 0xbf, 0xea, 0x96, 0x5f, 0xe4,
    0x9f, 0x99, 0xf7, 0x18, 0x8c, 0xec, 0x60, 0x28,
    0xeb, 0x29, 0x02, 0x49, 0xfc, 0xda, 0xd7, 0x78,
    0x68, 0xf8, 0xe1, 0xe9, 0x4d, 0x20, 0x6d, 0x32,
    0xa6, 0xde, 0xfc, 0xe4, 0xda, 0xcc, 0x6c, 0x75,
    0x36, 0x6b, 0xff, 0x5a, 0xac, 0x01, 0xa8, 0xc2,
    0xa9, 0xe6, 0x8b, 0x18, 0x3e, 0xec, 0xea, 0x4c,
    0x4a, 0x9e, 0x00, 0x09, 0xd1, 0x8a, 0x69, 0x1b,
    0x8b, 0xd9, 0xad, 0x37, 0xe5, 0x7c, 0xff, 0x7d,
    0x59, 0x56, 0x3e, 0xa0, 0xc6, 0x32, 0xd8, 0x35,
    0x2f, 0xff, 0xfb, 0x05, 0x02, 0xcd, 0xd7, 0x19,
    0xb9, 0x00, 0x86, 0x2a, 0xcf, 0xaa, 0x78, 0x16,
    0x4b, 0xf1, 0xa7, 0x59, 0xef, 0x7d, 0xe8, 0x74,
    0x23, 0x5c, 0xb2, 0xd4, 0x8a, 0x99, 0xa5, 0xbc,
    0xfa, 0x63, 0xd8, 0xf7, 0xbd, 0xc6, 0x00, 0x13,
    0x06, 0x02, 0x9a, 0xd4, 0xa7, 0xb4, 0x3d, 0x61,
    0xab, 0xf1, 0xc2, 0x95, 0x59, 0x9b, 0x3d, 0x67,
    0x1f, 0xde, 0x57, 0xb6, 0xb6, 0x9f, 0xb0, 0x87,
    0xd6, 0x51, 0xd5, 0x3e, 0x00, 0xe2, 0xc9, 0xa0,
    0x03, 0x66, 0xbc, 0x01, 0xb3, 0x8e, 0xfa, 0xf1,
    0x15, 0xeb, 0x26, 0xf1, 0x5d, 0x81, 0x90, 0xb4,
    0x1c, 0x00, 0x7c, 0x83, 0x4a, 0xa5, 0xde, 0x64,
    0xae, 0xea, 0x6c, 0x43, 0xc3, 0x20, 0x77, 0x77,
    0x42, 0x12, 0x24, 0xf5, 0xe3, 0x70, 0xdd, 0x59,
    0x48, 0x9c, 0xef, 0xd4, 0x8a, 0x3c, 0x29, 0x6a,
    0x0c, 0x9c, 0xf2, 0x13, 0xa4, 0x1c, 0x2f, 0x49,
    0xcd, 0xb4, 0xaa, 0x28, 0x40, 0x34, 0xc6, 0x75,
    0xba, 0x30, 0xe6, 0xd8, 0x5b, 0x2f, 0x08, 0xd0,
    0x29, 0xa5, 0x39, 0xfb, 0x6e, 0x3b, 0x0f, 0x52,
    0x2c, 0x68, 0xf0, 0x37, 0xa9, 0xd2, 0x56, 0xd6
};

static const unsigned char rsa_asym_plaintext_encrypt[256] = {
   0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
   0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
};
static const unsigned char rsa_asym_expected_encrypt[256] = {
    0x54, 0xac, 0x23, 0x96, 0x1d, 0x82, 0x5d, 0x8b,
    0x8f, 0x36, 0x33, 0xd0, 0xf4, 0x02, 0xa2, 0x61,
    0xb1, 0x13, 0xd4, 0x4a, 0x46, 0x06, 0x37, 0x3c,
    0xbf, 0x40, 0x05, 0x3c, 0xc6, 0x3b, 0x64, 0xdc,
    0x22, 0x22, 0xaf, 0x36, 0x79, 0x62, 0x45, 0xf0,
    0x97, 0x82, 0x22, 0x44, 0x86, 0x4a, 0x7c, 0xfa,
    0xac, 0x03, 0x21, 0x84, 0x3f, 0x31, 0xad, 0x2a,
    0xa4, 0x6e, 0x7a, 0xc5, 0x93, 0xf3, 0x0f, 0xfc,
    0xf1, 0x62, 0xce, 0x82, 0x12, 0x45, 0xc9, 0x35,
    0xb0, 0x7a, 0xcd, 0x99, 0x8c, 0x91, 0x6b, 0x5a,
    0xd3, 0x46, 0xdb, 0xf9, 0x9e, 0x52, 0x49, 0xbd,
    0x1e, 0xe8, 0xda, 0xac, 0x61, 0x47, 0xc2, 0xda,
    0xfc, 0x1e, 0xfb, 0x74, 0xd7, 0xd6, 0xc1, 0x18,
    0x86, 0x3e, 0x20, 0x9c, 0x7a, 0xe1, 0x04, 0xb7,
    0x38, 0x43, 0xb1, 0x4e, 0xa0, 0xd8, 0xc1, 0x39,
    0x4d, 0xe1, 0xd3, 0xb0, 0xb3, 0xf1, 0x82, 0x87,
    0x1f, 0x74, 0xb5, 0x69, 0xfd, 0x33, 0xd6, 0x21,
    0x7c, 0x61, 0x60, 0x28, 0xca, 0x70, 0xdb, 0xa0,
    0xbb, 0xc8, 0x73, 0xa9, 0x82, 0xf8, 0x6b, 0xd8,
    0xf0, 0xc9, 0x7b, 0x20, 0xdf, 0x9d, 0xfb, 0x8c,
    0xd4, 0xa2, 0x89, 0xe1, 0x9b, 0x04, 0xad, 0xaa,
    0x11, 0x6c, 0x8f, 0xce, 0x83, 0x29, 0x56, 0x69,
    0xbb, 0x00, 0x3b, 0xef, 0xca, 0x2d, 0xcd, 0x52,
    0xc8, 0xf1, 0xb3, 0x9b, 0xb4, 0x4f, 0x6d, 0x9c,
    0x3d, 0x69, 0xcc, 0x6d, 0x1f, 0x38, 0x4d, 0xe6,
    0xbb, 0x0c, 0x87, 0xdc, 0x5f, 0xa9, 0x24, 0x93,
    0x03, 0x46, 0xa2, 0x33, 0x6c, 0xf4, 0xd8, 0x5d,
    0x68, 0xf3, 0xd3, 0xe0, 0xf2, 0x30, 0xdb, 0xf5,
    0x4f, 0x0f, 0xad, 0xc7, 0xd0, 0xaa, 0x47, 0xd9,
    0x9f, 0x85, 0x1b, 0x2e, 0x6c, 0x3c, 0x57, 0x04,
    0x29, 0xf4, 0xf5, 0x66, 0x7d, 0x93, 0x4a, 0xaa,
    0x05, 0x52, 0x55, 0xc1, 0xc6, 0x06, 0x90, 0xab,
};

#ifndef OPENSSL_NO_EC
/* ECDSA key data */
static const char ecd_prime_curve_name[] = "secp224r1";
static const unsigned char ecd_prime_priv[] = {
    0x98, 0x1f, 0xb5, 0xf1, 0xfc, 0x87, 0x1d, 0x7d,
    0xde, 0x1e, 0x01, 0x64, 0x09, 0x9b, 0xe7, 0x1b,
    0x9f, 0xad, 0x63, 0xdd, 0x33, 0x01, 0xd1, 0x50,
    0x80, 0x93, 0x50, 0x30
};
static const unsigned char ecd_prime_pub[] = {
    0x04, 0x95, 0x47, 0x99, 0x44, 0x29, 0x8f, 0x51,
    0x39, 0xe2, 0x53, 0xec, 0x79, 0xb0, 0x4d, 0xde,
    0x87, 0x1a, 0x76, 0x54, 0xd5, 0x96, 0xb8, 0x7a,
    0x6d, 0xf4, 0x1c, 0x2c, 0x87, 0x91, 0x5f, 0xd5,
    0x31, 0xdd, 0x24, 0xe5, 0x78, 0xd9, 0x08, 0x24,
    0x8a, 0x49, 0x99, 0xec, 0x55, 0xf2, 0x82, 0xb3,
    0xc4, 0xb7, 0x33, 0x68, 0xe4, 0x24, 0xa9, 0x12,
    0x82
};
static const ST_KAT_PARAM ecdsa_prime_key[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_PKEY_PARAM_GROUP_NAME, ecd_prime_curve_name),
    ST_KAT_PARAM_OCTET(OSSL_PKEY_PARAM_PUB_KEY, ecd_prime_pub),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_PRIV_KEY, ecd_prime_priv),
    ST_KAT_PARAM_END()
};

# ifndef OPENSSL_NO_EC2M
static const char ecd_bin_curve_name[] = "sect233r1";
static const unsigned char ecd_bin_priv[] = {
    0x00, 0x6d, 0xd6, 0x39, 0x9d, 0x2a, 0xa2, 0xc8,
    0x8c, 0xfc, 0x7b, 0x80, 0x66, 0xaa, 0xe1, 0xaa,
    0xba, 0xee, 0xcb, 0xfd, 0xc9, 0xe5, 0x36, 0x38,
    0x2e, 0xf7, 0x37, 0x6d, 0xd3, 0x20
};
static const unsigned char ecd_bin_pub[] = {
    0x04, 0x00, 0x06, 0xe2, 0x56, 0xf7, 0x37, 0xf9,
    0xea, 0xb6, 0xd1, 0x0f, 0x59, 0xfa, 0x23, 0xc3,
    0x93, 0xa8, 0xb2, 0x26, 0xe2, 0x5c, 0x08, 0xbe,
    0x63, 0x49, 0x26, 0xdc, 0xc7, 0x1e, 0x6f, 0x01,
    0x32, 0x3b, 0xe6, 0x54, 0x8d, 0xc1, 0x13, 0x3e,
    0x54, 0xb2, 0x66, 0x89, 0xb2, 0x82, 0x0a, 0x72,
    0x02, 0xa8, 0xe9, 0x6f, 0x54, 0xfd, 0x3a, 0x6b,
    0x99, 0xb6, 0x8f, 0x80, 0x46
};
static const ST_KAT_PARAM ecdsa_bin_key[] = {
    ST_KAT_PARAM_UTF8STRING(OSSL_PKEY_PARAM_GROUP_NAME, ecd_bin_curve_name),
    ST_KAT_PARAM_OCTET(OSSL_PKEY_PARAM_PUB_KEY, ecd_bin_pub),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_PRIV_KEY, ecd_bin_priv),
    ST_KAT_PARAM_END()
};
# endif /* OPENSSL_NO_EC2M */
#endif /* OPENSSL_NO_EC */

#ifndef OPENSSL_NO_DSA
/* dsa 2048 */
static const unsigned char dsa_p[] = {
    0xa2, 0x9b, 0x88, 0x72, 0xce, 0x8b, 0x84, 0x23,
    0xb7, 0xd5, 0xd2, 0x1d, 0x4b, 0x02, 0xf5, 0x7e,
    0x03, 0xe9, 0xe6, 0xb8, 0xa2, 0x58, 0xdc, 0x16,
    0x61, 0x1b, 0xa0, 0x98, 0xab, 0x54, 0x34, 0x15,
    0xe4, 0x15, 0xf1, 0x56, 0x99, 0x7a, 0x3e, 0xe2,
    0x36, 0x65, 0x8f, 0xa0, 0x93, 0x26, 0x0d, 0xe3,
    0xad, 0x42, 0x2e, 0x05, 0xe0, 0x46, 0xf9, 0xec,
    0x29, 0x16, 0x1a, 0x37, 0x5f, 0x0e, 0xb4, 0xef,
    0xfc, 0xef, 0x58, 0x28, 0x5c, 0x5d, 0x39, 0xed,
    0x42, 0x5d, 0x7a, 0x62, 0xca, 0x12, 0x89, 0x6c,
    0x4a, 0x92, 0xcb, 0x19, 0x46, 0xf2, 0x95, 0x2a,
    0x48, 0x13, 0x3f, 0x07, 0xda, 0x36, 0x4d, 0x1b,
    0xdf, 0x6b, 0x0f, 0x71, 0x39, 0x98, 0x3e, 0x69,
    0x3c, 0x80, 0x05, 0x9b, 0x0e, 0xac, 0xd1, 0x47,
    0x9b, 0xa9, 0xf2, 0x85, 0x77, 0x54, 0xed, 0xe7,
    0x5f, 0x11, 0x2b, 0x07, 0xeb, 0xbf, 0x35, 0x34,
    0x8b, 0xbf, 0x3e, 0x01, 0xe0, 0x2f, 0x2d, 0x47,
    0x3d, 0xe3, 0x94, 0x53, 0xf9, 0x9d, 0xd2, 0x36,
    0x75, 0x41, 0xca, 0xca, 0x3b, 0xa0, 0x11, 0x66,
    0x34, 0x3d, 0x7b, 0x5b, 0x58, 0xa3, 0x7b, 0xd1,
    0xb7, 0x52, 0x1d, 0xb2, 0xf1, 0x3b, 0x86, 0x70,
    0x71, 0x32, 0xfe, 0x09, 0xf4, 0xcd, 0x09, 0xdc,
    0x16, 0x18, 0xfa, 0x34, 0x01, 0xeb, 0xf9, 0xcc,
    0x7b, 0x19, 0xfa, 0x94, 0xaa, 0x47, 0x20, 0x88,
    0x13, 0x3d, 0x6c, 0xb2, 0xd3, 0x5c, 0x11, 0x79,
    0xc8, 0xc8, 0xff, 0x36, 0x87, 0x58, 0xd5, 0x07,
    0xd9, 0xf9, 0xa1, 0x7d, 0x46, 0xc1, 0x10, 0xfe,
    0x31, 0x44, 0xce, 0x9b, 0x02, 0x2b, 0x42, 0xe4,
    0x19, 0xeb, 0x4f, 0x53, 0x88, 0x61, 0x3b, 0xfc,
    0x3e, 0x26, 0x24, 0x1a, 0x43, 0x2e, 0x87, 0x06,
    0xbc, 0x58, 0xef, 0x76, 0x11, 0x72, 0x78, 0xde,
    0xab, 0x6c, 0xf6, 0x92, 0x61, 0x82, 0x91, 0xb7
};
static const unsigned char dsa_q[] = {
    0xa3, 0xbf, 0xd9, 0xab, 0x78, 0x84, 0x79, 0x4e,
    0x38, 0x34, 0x50, 0xd5, 0x89, 0x1d, 0xc1, 0x8b,
    0x65, 0x15, 0x7b, 0xdc, 0xfc, 0xda, 0xc5, 0x15,
    0x18, 0x90, 0x28, 0x67
};
static const unsigned char dsa_g[] = {
    0x68, 0x19, 0x27, 0x88, 0x69, 0xc7, 0xfd, 0x3d,
    0x2d, 0x7b, 0x77, 0xf7, 0x7e, 0x81, 0x50, 0xd9,
    0xad, 0x43, 0x3b, 0xea, 0x3b, 0xa8, 0x5e, 0xfc,
    0x80, 0x41, 0x5a, 0xa3, 0x54, 0x5f, 0x78, 0xf7,
    0x22, 0x96, 0xf0, 0x6c, 0xb1, 0x9c, 0xed, 0xa0,
    0x6c, 0x94, 0xb0, 0x55, 0x1c, 0xfe, 0x6e, 0x6f,
    0x86, 0x3e, 0x31, 0xd1, 0xde, 0x6e, 0xed, 0x7d,
    0xab, 0x8b, 0x0c, 0x9d, 0xf2, 0x31, 0xe0, 0x84,
    0x34, 0xd1, 0x18, 0x4f, 0x91, 0xd0, 0x33, 0x69,
    0x6b, 0xb3, 0x82, 0xf8, 0x45, 0x5e, 0x98, 0x88,
    0xf5, 0xd3, 0x1d, 0x47, 0x84, 0xec, 0x40, 0x12,
    0x02, 0x46, 0xf4, 0xbe, 0xa6, 0x17, 0x94, 0xbb,
    0xa5, 0x86, 0x6f, 0x09, 0x74, 0x64, 0x63, 0xbd,
    0xf8, 0xe9, 0xe1, 0x08, 0xcd, 0x95, 0x29, 0xc3,
    0xd0, 0xf6, 0xdf, 0x80, 0x31, 0x6e, 0x2e, 0x70,
    0xaa, 0xeb, 0x1b, 0x26, 0xcd, 0xb8, 0xad, 0x97,
    0xbc, 0x3d, 0x28, 0x7e, 0x0b, 0x8d, 0x61, 0x6c,
    0x42, 0xe6, 0x5b, 0x87, 0xdb, 0x20, 0xde, 0xb7,
    0x00, 0x5b, 0xc4, 0x16, 0x74, 0x7a, 0x64, 0x70,
    0x14, 0x7a, 0x68, 0xa7, 0x82, 0x03, 0x88, 0xeb,
    0xf4, 0x4d, 0x52, 0xe0, 0x62, 0x8a, 0xf9, 0xcf,
    0x1b, 0x71, 0x66, 0xd0, 0x34, 0x65, 0xf3, 0x5a,
    0xcc, 0x31, 0xb6, 0x11, 0x0c, 0x43, 0xda, 0xbc,
    0x7c, 0x5d, 0x59, 0x1e, 0x67, 0x1e, 0xaf, 0x7c,
    0x25, 0x2c, 0x1c, 0x14, 0x53, 0x36, 0xa1, 0xa4,
    0xdd, 0xf1, 0x32, 0x44, 0xd5, 0x5e, 0x83, 0x56,
    0x80, 0xca, 0xb2, 0x53, 0x3b, 0x82, 0xdf, 0x2e,
    0xfe, 0x55, 0xec, 0x18, 0xc1, 0xe6, 0xcd, 0x00,
    0x7b, 0xb0, 0x89, 0x75, 0x8b, 0xb1, 0x7c, 0x2c,
    0xbe, 0x14, 0x44, 0x1b, 0xd0, 0x93, 0xae, 0x66,
    0xe5, 0x97, 0x6d, 0x53, 0x73, 0x3f, 0x4f, 0xa3,
    0x26, 0x97, 0x01, 0xd3, 0x1d, 0x23, 0xd4, 0x67
};
static const unsigned char dsa_pub[] = {
    0xa0, 0x12, 0xb3, 0xb1, 0x70, 0xb3, 0x07, 0x22,
    0x79, 0x57, 0xb7, 0xca, 0x20, 0x61, 0xa8, 0x16,
    0xac, 0x7a, 0x2b, 0x3d, 0x9a, 0xe9, 0x95, 0xa5,
    0x11, 0x9c, 0x38, 0x5b, 0x60, 0x3b, 0xf6, 0xf6,
    0xc5, 0xde, 0x4d, 0xc5, 0xec, 0xb5, 0xdf, 0xa4,
    0xa4, 0x1c, 0x68, 0x66, 0x2e, 0xb2, 0x5b, 0x63,
    0x8b, 0x7e, 0x26, 0x20, 0xba, 0x89, 0x8d, 0x07,
    0xda, 0x6c, 0x49, 0x91, 0xe7, 0x6c, 0xc0, 0xec,
    0xd1, 0xad, 0x34, 0x21, 0x07, 0x70, 0x67, 0xe4,
    0x7c, 0x18, 0xf5, 0x8a, 0x92, 0xa7, 0x2a, 0xd4,
    0x31, 0x99, 0xec, 0xb7, 0xbd, 0x84, 0xe7, 0xd3,
    0xaf, 0xb9, 0x01, 0x9f, 0x0e, 0x9d, 0xd0, 0xfb,
    0xaa, 0x48, 0x73, 0x00, 0xb1, 0x30, 0x81, 0xe3,
    0x3c, 0x90, 0x28, 0x76, 0x43, 0x6f, 0x7b, 0x03,
    0xc3, 0x45, 0x52, 0x84, 0x81, 0xd3, 0x62, 0x81,
    0x5e, 0x24, 0xfe, 0x59, 0xda, 0xc5, 0xac, 0x34,
    0x66, 0x0d, 0x4c, 0x8a, 0x76, 0xcb, 0x99, 0xa7,
    0xc7, 0xde, 0x93, 0xeb, 0x95, 0x6c, 0xd6, 0xbc,
    0x88, 0xe5, 0x8d, 0x90, 0x10, 0x34, 0x94, 0x4a,
    0x09, 0x4b, 0x01, 0x80, 0x3a, 0x43, 0xc6, 0x72,
    0xb9, 0x68, 0x8c, 0x0e, 0x01, 0xd8, 0xf4, 0xfc,
    0x91, 0xc6, 0x2a, 0x3f, 0x88, 0x02, 0x1f, 0x7b,
    0xd6, 0xa6, 0x51, 0xb1, 0xa8, 0x8f, 0x43, 0xaa,
    0x4e, 0xf2, 0x76, 0x53, 0xd1, 0x2b, 0xf8, 0xb7,
    0x09, 0x9f, 0xdf, 0x6b, 0x46, 0x10, 0x82, 0xf8,
    0xe9, 0x39, 0x10, 0x7b, 0xfd, 0x2f, 0x72, 0x10,
    0x08, 0x7d, 0x32, 0x6c, 0x37, 0x52, 0x00, 0xf1,
    0xf5, 0x1e, 0x7e, 0x74, 0xa3, 0x41, 0x31, 0x90,
    0x1b, 0xcd, 0x08, 0x63, 0x52, 0x1f, 0xf8, 0xd6,
    0x76, 0xc4, 0x85, 0x81, 0x86, 0x87, 0x36, 0xc5,
    0xe5, 0x1b, 0x16, 0xa4, 0xe3, 0x92, 0x15, 0xea,
    0x0b, 0x17, 0xc4, 0x73, 0x59, 0x74, 0xc5, 0x16
};
static const unsigned char dsa_priv[] = {
    0x6c, 0xca, 0xee, 0xf6, 0xd7, 0x3b, 0x4e, 0x80,
    0xf1, 0x1c, 0x17, 0xb8, 0xe9, 0x62, 0x7c, 0x03,
    0x66, 0x35, 0xba, 0xc3, 0x94, 0x23, 0x50, 0x5e,
    0x40, 0x7e, 0x5c, 0xb7
};

static const ST_KAT_PARAM dsa_key[] = {
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_FFC_P, dsa_p),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_FFC_Q, dsa_q),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_FFC_G, dsa_g),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_PUB_KEY, dsa_pub),
    ST_KAT_PARAM_BIGNUM(OSSL_PKEY_PARAM_PRIV_KEY, dsa_priv),
    ST_KAT_PARAM_END()
};
#endif /* OPENSSL_NO_DSA */

static const ST_KAT_SIGN st_kat_sign_tests[] = {
    {
        OSSL_SELF_TEST_DESC_SIGN_RSA,
        "RSA",
        "SHA-256",
        rsa_crt_key,
        ITM(rsa_expected_sig)
    },
#ifndef OPENSSL_NO_EC
    {
        OSSL_SELF_TEST_DESC_SIGN_ECDSA,
        "EC",
        "SHA-256",
        ecdsa_prime_key,
        /*
         * The ECDSA signature changes each time due to it using a random k.
         * So there is no expected KAT for this case.
         */
    },
# ifndef OPENSSL_NO_EC2M
    {
        OSSL_SELF_TEST_DESC_SIGN_ECDSA,
        "EC",
        "SHA-256",
        ecdsa_bin_key,
        /*
         * The ECDSA signature changes each time due to it using a random k.
         * So there is no expected KAT for this case.
         */
    },
# endif
#endif /* OPENSSL_NO_EC */
#ifndef OPENSSL_NO_DSA
    {
        OSSL_SELF_TEST_DESC_SIGN_DSA,
        "DSA",
        "SHA-256",
        dsa_key,
        /*
         * The DSA signature changes each time due to it using a random k.
         * So there is no expected KAT for this case.
         */
    },
#endif /* OPENSSL_NO_DSA */
};

static const ST_KAT_ASYM_CIPHER st_kat_asym_cipher_tests[] = {
    {
        OSSL_SELF_TEST_DESC_ASYM_RSA_ENC,
        "RSA",
        1,
        rsa_pub_key,
        rsa_enc_params,
        ITM(rsa_asym_plaintext_encrypt),
        ITM(rsa_asym_expected_encrypt),
    },
    {
        OSSL_SELF_TEST_DESC_ASYM_RSA_DEC,
        "RSA",
        0,
        rsa_priv_key,
        rsa_enc_params,
        ITM(rsa_asym_expected_encrypt),
        ITM(rsa_asym_plaintext_encrypt),
    },
    {
        OSSL_SELF_TEST_DESC_ASYM_RSA_DEC,
        "RSA",
        0,
        rsa_crt_key,
        rsa_enc_params,
        ITM(rsa_asym_expected_encrypt),
        ITM(rsa_asym_plaintext_encrypt),
    },
};
