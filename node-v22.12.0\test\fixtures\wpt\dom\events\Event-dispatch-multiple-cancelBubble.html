<!DOCTYPE html>
<html>
<head>
<title>Multiple dispatchEvent() and cancelBubble</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
</head>
<body>
<div id=log></div>

<div id="parent" style="display: none">
    <input id="target" type="hidden" value=""/>
</div>

<script>
test(function() {
    var event_type = "foo";
    var target = document.getElementById("target");
    var parent = document.getElementById("parent");
    var actual_result;
    var test_event = function(evt) {
        actual_result.push(evt.currentTarget);

        if (parent == evt.currentTarget) {
            evt.cancelBubble = true;
        }
    };

    var evt = document.createEvent("Event");
    evt.initEvent(event_type, true, true);

    target.addEventListener(event_type, test_event, false);
    parent.addEventListener(event_type, test_event, false);
    document.addEventListener(event_type, test_event, false);
    window.addEventListener(event_type, test_event, false);

    actual_result = [];
    target.dispatchEvent(evt);
    assert_array_equals(actual_result, [target, parent]);

    actual_result = [];
    parent.dispatchEvent(evt);
    assert_array_equals(actual_result, [parent]);

    actual_result = [];
    document.dispatchEvent(evt);
    assert_array_equals(actual_result, [document, window]);
});
</script>
</body>
</html>
