<!DOCTYPE html>
<meta charset="utf-8">
<title>Event's stopImmediatePropagation</title>
<link rel="help" href="https://dom.spec.whatwg.org/#dom-event-stopimmediatepropagation">
<link rel="author" href="mailto:<EMAIL>" title="Domenic Denicola">

<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>

<div id="target"></div>

<script>
"use strict";

setup({ single_test: true });

const target = document.querySelector("#target");

let timesCalled = 0;
target.addEventListener("test", e => {
  ++timesCalled;
  e.stopImmediatePropagation();
  assert_equals(e.cancelBubble, true, "The stop propagation flag must have been set");
});
target.addEventListener("test", () => {
  ++timesCalled;
});

const e = new Event("test");
target.dispatchEvent(e);
assert_equals(timesCalled, 1, "The second listener must not have been called");

done();
</script>
