/*
 * WARNING: do not edit!
 * Generated by <PERSON><PERSON><PERSON> from providers/common/der/der_sm2_gen.c.in
 *
 * Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#include "prov/der_sm2.h"

/* Well known OIDs precompiled */

/*
 * sm2-with-SM3 OBJECT IDENTIFIER ::= { sm-scheme 501 }
 */
const unsigned char ossl_der_oid_sm2_with_SM3[DER_OID_SZ_sm2_with_SM3] = {
    DER_OID_V_sm2_with_SM3
};

/*
 * curveSM2 OBJECT IDENTIFIER ::= { sm-scheme 301 }
 */
const unsigned char ossl_der_oid_curveSM2[DER_OID_SZ_curveSM2] = {
    DER_OID_V_curveSM2
};

