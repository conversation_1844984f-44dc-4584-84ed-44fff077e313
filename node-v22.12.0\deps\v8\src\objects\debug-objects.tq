// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

extern class BreakPoint extends Struct {
  id: Smi;
  condition: String;
}

extern class BreakPointInfo extends Struct {
  // The position in the source for the break position.
  source_position: Smi;
  // List of related JavaScript break points.
  break_points: FixedArray|BreakPoint|Undefined;
}

bitfield struct DebugInfoFlags extends uint31 {
  has_break_info: bool: 1 bit;
  prepared_for_debug_execution: bool: 1 bit;
  has_coverage_info: bool: 1 bit;
  break_at_entry: bool: 1 bit;
  can_break_at_entry: bool: 1 bit;
  debug_execution_mode: bool: 1 bit;
}

bitfield struct DebuggerHints extends uint31 {
  side_effect_state: int32: 2 bit;
  debug_is_blackboxed: bool: 1 bit;
  computed_debug_is_blackboxed: bool: 1 bit;
  debugging_id: int32: 20 bit;
}

extern class DebugInfo extends Struct {
  shared: SharedFunctionInfo;
  // Bit field containing various information collected for debugging.
  debugger_hints: SmiTagged<DebuggerHints>;
  // Fixed array holding status information for each active break point.
  break_points: FixedArray;
  // A bitfield that lists uses of the current instance.
  @cppRelaxedLoad @cppRelaxedStore flags: SmiTagged<DebugInfoFlags>;
  coverage_info: CoverageInfo|Undefined;
  // The original uninstrumented bytecode array for functions with break
  // points - the instrumented bytecode is held in the shared function info.
  // Can contain Smi::zero() if cleared.
  @ifnot(V8_ENABLE_SANDBOX)
      @cppAcquireLoad @cppReleaseStore original_bytecode_array: Smi
      |BytecodeArray;
  @if(V8_ENABLE_SANDBOX) original_bytecode_array:
      IndirectPointer<BytecodeArray>;
  // The debug instrumented bytecode array for functions with break points
  // - also pointed to by the shared function info.
  // Can contain Smi::zero() if cleared.
  @ifnot(V8_ENABLE_SANDBOX)
      @cppAcquireLoad @cppReleaseStore debug_bytecode_array: Smi|BytecodeArray;
  @if(V8_ENABLE_SANDBOX) debug_bytecode_array:
      IndirectPointer<BytecodeArray>;
}

@export
struct CoverageInfoSlot {
  start_source_position: int32;
  end_source_position: int32;
  block_count: int32;
  padding: int32;  // Padding to make the index count 4.
}

// CoverageInfo's visitor is included in DATA_ONLY_VISITOR_ID_LIST, so it must
// not contain any HeapObject fields.
extern class CoverageInfo extends HeapObject {
  const slot_count: int32;
  slots[slot_count]: CoverageInfoSlot;
}

bitfield struct StackFrameInfoFlags extends uint31 {
  is_constructor: bool: 1 bit;
  bytecode_offset_or_source_position: int32: 30 bit;
}

extern class StackFrameInfo extends Struct {
  // In case this field holds a SharedFunctionInfo, the
  // |bytecode_offset_or_source_position| part of the
  // |flags| bit field below contains the bytecode offset
  // within that SharedFunctionInfo. Otherwise if this
  // is a Script, the |bytecode_offset_or_source_position|
  // holds the source position within the Script.
  shared_or_script: SharedFunctionInfo|Script;
  function_name: String;
  flags: SmiTagged<StackFrameInfoFlags>;
}

// This struct is used by V8 as error_data_symbol on JSError
// instances when the inspector asks V8 to keep (detailed)
// stack traces in addition to the (simple) stack traces that
// are collected by V8 for error.stack.
//
// This can have one of the following forms:
//
// (1) A pair of FixedArray<CallSiteInfo> and positive limit
//     if the stack information is not formatted yet and the
//     inspector did not yet request any information about the
//     error's stack trace. The positive limit specifies the cap
//     for the number of call sites exposed to error.stack.
// (2) A pair of FixedArray<CallSiteInfo> and negative limit
//     is similar to the above, except that the limit should be
//     applied to the inspector StackFrameInfo list once computed
//     rather than the number of call sites exposed to error.stack.
// (3) A FixedArray<CallSiteInfo> and FixedArray<StackFrameInfo>
//     pair indicates that the inspector already asked for the
//     detailed stack information, but the error.stack property
//     was not yet formatted. If any limit (negative or positive)
//     was stored in the second field before, it was applied to the
//     appropriate FixedArray now.
// (4) A valid JavaScript object and FixedArray<StackFrameInfo>
//     once error.stack was accessed.
//
// Memorizing the limits is important to ensure that the fact that
// the inspector is active doesn't influence the script execution
// (i.e. the observable limit of call sites in error.stack is the
// same independent of whether the inspector is active or not).
extern class ErrorStackData extends Struct {
  // This holds either the FixedArray of CallSiteInfo instances or
  // the formatted stack value (usually a string) that's returned
  // from the error.stack property.
  call_site_infos_or_formatted_stack: FixedArray|JSAny;
  // This holds either the FixedArray of StackFrameInfo instances
  // for the inspector stack trace or a stack trace limit, which
  // if positive specifies how many of the CallSiteInfo instances
  // in the first field are to be revealed via error.stack or if
  // negative specifies the (negated) limit for the inspector
  // stack traces.
  limit_or_stack_frame_infos: Smi|FixedArray;
}

extern class PromiseOnStack extends Struct {
  prev: PromiseOnStack|Zero;
  promise: Weak<JSObject>;
}
