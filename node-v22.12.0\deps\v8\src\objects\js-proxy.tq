// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

extern class JSProxy extends JSReceiver {
  target: JSReceiver|Null;
  handler: <PERSON><PERSON>ecei<PERSON>|Null;
}

extern shape JSProxyRevocableResult extends JSObject {
  proxy: JSA<PERSON>;
  revoke: <PERSON><PERSON><PERSON>;
}

macro NewJSProxyRevocableResult(
    implicit context: Context)(proxy: JSPro<PERSON>,
    revoke: JSFunction): JSProxyRevocableResult {
  return new JSProxyRevocableResult{
    map: GetProxyRevocableResultMap(),
    properties_or_hash: kEmptyFixedArray,
    elements: kEmptyFixedArray,
    proxy,
    revoke
  };
}
