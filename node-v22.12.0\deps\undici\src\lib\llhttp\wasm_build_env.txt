
> undici@6.21.0 prebuild:wasm
> node build/wasm.js --prebuild

> docker build --platform=linux/x86_64 -t llhttp_wasm_builder -f /home/<USER>/work/node/node/deps/undici/src/build/Dockerfile /home/<USER>/work/node/node/deps/undici/src



> undici@6.21.0 build:wasm
> node build/wasm.js --docker

> docker run --rm -t --platform=linux/x86_64 --user 1001:127 --mount type=bind,source=/home/<USER>/work/node/node/deps/undici/src/lib/llhttp,target=/home/<USER>/undici/lib/llhttp llhttp_wasm_builder node build/wasm.js


alpine-baselayout-3.4.3-r2
alpine-baselayout-data-3.4.3-r2
alpine-keys-2.4-r1
apk-tools-2.14.0-r5
binutils-2.41-r0
busybox-1.36.1-r15
busybox-binsh-1.36.1-r15
ca-certificates-bundle-20230506-r0
clang17-17.0.5-r0
clang17-headers-17.0.5-r0
clang17-libs-17.0.5-r0
fortify-headers-1.1-r3
gcc-13.2.1_git20231014-r0
gmp-6.3.0-r0
isl26-0.26-r1
jansson-2.14-r4
libatomic-13.2.1_git20231014-r0
libc-utils-0.7.2-r5
libcrypto3-3.1.4-r5
libffi-3.4.4-r3
libgcc-13.2.1_git20231014-r0
libgomp-13.2.1_git20231014-r0
libssl3-3.1.4-r5
libstdc++-13.2.1_git20231014-r0
libstdc++-dev-13.2.1_git20231014-r0
libxml2-2.11.8-r0
lld-17.0.5-r0
lld-libs-17.0.5-r0
llvm17-libs-17.0.5-r0
llvm17-linker-tools-17.0.5-r0
mpc1-1.3.1-r1
mpfr4-4.2.1-r0
musl-1.2.4_git20230717-r4
musl-dev-1.2.4_git20230717-r4
musl-utils-1.2.4_git20230717-r4
scanelf-1.3.7-r2
scudo-malloc-17.0.5-r0
ssl_client-1.36.1-r15
wasi-compiler-rt-17.0.5-r1
wasi-libc-0.20231012-r0
wasi-libcxx-17.0.5-r0
wasi-sdk-20-r3
xz-libs-5.4.5-r0
zlib-1.3.1-r0
zstd-libs-1.5.5-r8

