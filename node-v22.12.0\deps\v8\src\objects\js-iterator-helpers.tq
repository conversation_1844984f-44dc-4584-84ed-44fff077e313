// Copyright 2023 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@abstract
extern class <PERSON><PERSON><PERSON>torHelper extends JSObject {
  underlying: iterator::IteratorRecord;
}

extern class <PERSON>SIteratorMapHelper extends J<PERSON>teratorHelper {
  mapper: Callable;
  counter: Number;
}

extern class JSIteratorFilterHelper extends JSIteratorHelper {
  predicate: Callable;
  counter: Number;
}

extern class <PERSON><PERSON><PERSON>tor<PERSON>ake<PERSON><PERSON>per extends J<PERSON><PERSON>torHel<PERSON> {
  remaining: Number;
}

extern class <PERSON><PERSON><PERSON>torDropHelper extends <PERSON><PERSON><PERSON>torHelper {
  remaining: Number;
}

extern class J<PERSON><PERSON>tor<PERSON><PERSON><PERSON>apHelper extends J<PERSON><PERSON>torHelper {
  mapper: Callable;
  counter: Number;
  innerIterator: iterator::IteratorRecord;
  innerAlive: Boolean;
}
