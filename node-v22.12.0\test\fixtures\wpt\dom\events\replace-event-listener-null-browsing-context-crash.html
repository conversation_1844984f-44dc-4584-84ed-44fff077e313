<!DOCTYPE html>
<title>Event listeners: replace listener after moving between documents</title>
<link rel="author" title="<PERSON> Chapin" href="mailto:<EMAIL>">
<link rel="help" href="https://w3c.github.io/touch-events/#dom-globaleventhandlers-ontouchcancel">
<link rel="help" href="https://bugs.chromium.org/p/chromium/issues/detail?id=1083793">
<meta name="assert" content="Overwriting an attribute event listener after adopting the owning node to a different document should not crash"/>
<progress id="p">
<iframe id="i"></iframe>
<script>
var p = document.getElementById("p");
i.contentDocument.adoptNode(p);
p.setAttribute("ontouchcancel", "");
document.body.appendChild(p);
p.setAttribute("ontouchcancel", "");
</script>

