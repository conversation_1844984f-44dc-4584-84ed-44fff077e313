<!DOCTYPE html>
<meta charset=utf-8>
<title>addEventListener with a platform object</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script>
</script>
<my-custom-click id=click>Click me!</my-custom-click>
<script>
"use strict";
setup({ single_test: true });

class MyCustomClick extends HTMLElement {
  connectedCallback() {
    this.addEventListener("click", this);
  }

  handleEvent(event) {
    if (event.target === this) {
      this.dataset.yay = "It worked!";
    }
  }
}
window.customElements.define("my-custom-click", MyCustomClick);

const customElement = document.getElementById("click");
customElement.click();

assert_equals(customElement.dataset.yay, "It worked!");

done();
</script>
