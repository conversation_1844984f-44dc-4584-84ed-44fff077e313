// Copyright 2016 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Generate this file using the update-object-macros-undef.py script.

// PRESUBMIT_INTENTIONALLY_MISSING_INCLUDE_GUARD

#undef OBJECT_CONSTRUCTORS
#undef OBJECT_CONSTRUCTORS_IMPL
#undef NEVER_READ_ONLY_SPACE
#undef NEVER_READ_ONLY_SPACE_IMPL
#undef DECL_PRIMITIVE_GETTER
#undef DECL_PRIMITIVE_SETTER
#undef DECL_PRIMITIVE_ACCESSORS
#undef DECL_BOOLEAN_ACCESSORS
#undef DECL_INT_ACCESSORS
#undef DECL_INT32_ACCESSORS
#undef DECL_SANDBOXED_POINTER_ACCESSORS
#undef DECL_UINT16_ACCESSORS
#undef DECL_INT16_ACCESSORS
#undef DECL_UINT8_ACCESSORS
#undef DECL_RELAXED_PRIMITIVE_ACCESSORS
#undef DECL_RELAXED_INT32_ACCESSORS
#undef DECL_RELAXED_UINT16_ACCESSORS
#undef DECL_GETTER
#undef DEF_GETTER
#undef DEF_RELAXED_GETTER
#undef DEF_ACQUIRE_GETTER
#undef TQ_FIELD_TYPE
#undef DECL_FIELD_OFFSET_TQ
#undef DECL_SETTER
#undef DECL_ACCESSORS
#undef DECL_ACCESSORS_LOAD_TAG
#undef DECL_ACCESSORS_STORE_TAG
#undef DECL_RELAXED_GETTER
#undef DECL_RELAXED_SETTER
#undef DECL_RELAXED_ACCESSORS
#undef DECL_ACQUIRE_GETTER
#undef DECL_RELEASE_SETTER
#undef DECL_RELEASE_ACQUIRE_ACCESSORS
#undef DECL_RELEASE_ACQUIRE_WEAK_ACCESSORS
#undef DECL_CAST
#undef CAST_ACCESSOR
#undef DEF_PRIMITIVE_ACCESSORS
#undef INT_ACCESSORS
#undef INT32_ACCESSORS
#undef UINT16_ACCESSORS
#undef UINT8_ACCESSORS
#undef RELAXED_INT32_ACCESSORS
#undef RELAXED_UINT16_ACCESSORS
#undef ACCESSORS_CHECKED2
#undef ACCESSORS_CHECKED
#undef ACCESSORS
#undef ACCESSORS_NOCAGE
#undef RENAME_TORQUE_ACCESSORS
#undef RENAME_PRIMITIVE_TORQUE_ACCESSORS
#undef ACCESSORS_RELAXED_CHECKED2
#undef ACCESSORS_RELAXED_CHECKED
#undef ACCESSORS_RELAXED
#undef RELAXED_ACCESSORS_CHECKED2
#undef RELAXED_ACCESSORS_CHECKED
#undef RELAXED_ACCESSORS
#undef RELEASE_ACQUIRE_ACCESSORS_CHECKED2
#undef RELEASE_ACQUIRE_ACCESSORS_CHECKED
#undef RELEASE_ACQUIRE_ACCESSORS
#undef WEAK_ACCESSORS_CHECKED2
#undef WEAK_ACCESSORS_CHECKED
#undef WEAK_ACCESSORS
#undef RELEASE_ACQUIRE_WEAK_ACCESSORS_CHECKED2
#undef RELEASE_ACQUIRE_WEAK_ACCESSORS_CHECKED
#undef RELEASE_ACQUIRE_WEAK_ACCESSORS
#undef SMI_ACCESSORS_CHECKED
#undef SMI_ACCESSORS
#undef DECL_RELEASE_ACQUIRE_INT_ACCESSORS
#undef RELEASE_ACQUIRE_SMI_ACCESSORS
#undef DECL_RELAXED_SMI_ACCESSORS
#undef RELAXED_SMI_ACCESSORS
#undef BOOL_GETTER
#undef BOOL_ACCESSORS
#undef DECL_RELAXED_BOOL_ACCESSORS
#undef RELAXED_BOOL_ACCESSORS
#undef DECL_EXTERNAL_POINTER_ACCESSORS_MAYBE_READ_ONLY_HOST
#undef EXTERNAL_POINTER_ACCESSORS_MAYBE_READ_ONLY_HOST
#undef DECL_EXTERNAL_POINTER_ACCESSORS
#undef EXTERNAL_POINTER_ACCESSORS
#undef BIT_FIELD_ACCESSORS2
#undef BIT_FIELD_ACCESSORS
#undef RELAXED_INT16_ACCESSORS
#undef FIELD_ADDR
#undef SEQ_CST_READ_FIELD
#undef ACQUIRE_READ_FIELD
#undef RELAXED_READ_FIELD
#undef RELAXED_READ_WEAK_FIELD
#undef WRITE_FIELD
#undef SEQ_CST_WRITE_FIELD
#undef RELEASE_WRITE_FIELD
#undef RELAXED_WRITE_FIELD
#undef RELAXED_WRITE_WEAK_FIELD
#undef WRITE_BARRIER
#undef WEAK_WRITE_BARRIER
#undef EPHEMERON_KEY_WRITE_BARRIER
#undef CONDITIONAL_WRITE_BARRIER
#undef CONDITIONAL_WEAK_WRITE_BARRIER
#undef CONDITIONAL_EPHEMERON_KEY_WRITE_BARRIER
#undef ACQUIRE_READ_INT8_FIELD
#undef ACQUIRE_READ_INT32_FIELD
#undef RELAXED_WRITE_INT8_FIELD
#undef RELAXED_READ_INT8_FIELD
#undef RELAXED_READ_UINT16_FIELD
#undef RELAXED_WRITE_UINT16_FIELD
#undef RELAXED_READ_INT16_FIELD
#undef RELAXED_WRITE_INT16_FIELD
#undef RELAXED_READ_UINT32_FIELD
#undef ACQUIRE_READ_UINT32_FIELD
#undef RELAXED_WRITE_UINT32_FIELD
#undef RELEASE_WRITE_INT8_FIELD
#undef RELEASE_WRITE_UINT32_FIELD
#undef RELAXED_READ_INT32_FIELD
#undef RELEASE_WRITE_INT32_FIELD
#undef RELAXED_WRITE_INT32_FIELD
#undef RELAXED_READ_INT_FIELD
#undef RELAXED_WRITE_INT_FIELD
#undef RELAXED_READ_UINT_FIELD
#undef RELAXED_WRITE_UINT_FIELD
#undef RELAXED_READ_BYTE_FIELD
#undef ACQUIRE_READ_BYTE_FIELD
#undef RELAXED_WRITE_BYTE_FIELD
#undef RELEASE_WRITE_BYTE_FIELD
#undef DECL_PRINTER
#undef DECL_VERIFIER
#undef EXPORT_DECL_VERIFIER
#undef DEFINE_DEOPT_ELEMENT_ACCESSORS
#undef DEFINE_DEOPT_ENTRY_ACCESSORS
#undef TQ_OBJECT_CONSTRUCTORS
#undef TQ_OBJECT_CONSTRUCTORS_IMPL
#undef TQ_CPP_OBJECT_DEFINITION_ASSERTS
#undef DEF_HEAP_OBJECT_PREDICATE
