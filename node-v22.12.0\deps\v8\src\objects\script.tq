// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

type CompilationType extends int32 constexpr 'Script::CompilationType';
type CompilationState extends int32 constexpr 'Script::CompilationState';

bitfield struct ScriptFlags extends uint31 {
  compilation_type: CompilationType: 1 bit;
  compilation_state: CompilationState: 1 bit;
  is_repl_mode: bool: 1 bit;
  origin_options: int32: 4 bit;
  // Whether an instrumentation breakpoint is set for this script (wasm only).
  break_on_entry: bool: 1 bit;
  produce_compile_hints: bool: 1 bit;
  deserialized: bool: 1 bit;
}

extern class Script extends Struct {
  // [source]: the script source.
  source: String|Undefined;

  // [name]: the script name.
  name: Object;

  // [line_offset]: script line offset in resource from where it was extracted.
  line_offset: Smi;

  // [column_offset]: script column offset in resource from where it was
  // extracted.
  column_offset: Smi;

  // [context_data]: context data for the context this script was compiled in.
  context_data: Smi|Undefined|Symbol;

  script_type: Smi;

  // [line_ends]: FixedArray of line ends positions.
  line_ends: FixedArray|Smi;

  // [id]: the script id.
  id: Smi;

  // For scripts originating from eval: the SharedFunctionInfo contains the SFI
  // for the script. For scripts wrapped as functions: the FixedArray contains
  // the arguments.
  eval_from_shared_or_wrapped_arguments: SharedFunctionInfo|FixedArray|
      Undefined;
  eval_from_position: Smi|Foreign;  // Smi or Managed<wasm::NativeModule>
  shared_function_infos: WeakFixedArray|WeakArrayList;

  // [compiled_lazy_function_positions]: ArrayList containing SMIs marking
  // the start positions of lazy functions which got compiled.
  compiled_lazy_function_positions: ArrayList|Undefined;

  // [flags]: Holds an exciting bitfield.
  flags: SmiTagged<ScriptFlags>;

  // [source_url]: sourceURL from magic comment
  source_url: String|Undefined;

  // [source_mapping_url]: sourceMappingURL magic comment
  source_mapping_url: Object;

  // [host_defined_options]: Options defined by the embedder.
  host_defined_options: FixedArray;

  // TODO(cbruni, v8:12302): remove once module callback API is updated.
  // Used to make sure we are backwards compatible with node to gaurantee
  // the same lifetime for ScriptOrModule as the Script they originated.
  @if(V8_SCRIPTORMODULE_LEGACY_LIFETIME) script_or_modules: ArrayList;

  // [source_hash]: Calculated once per file if the source text is available,
  // represents the SHA-256 of the content
  source_hash: String|Undefined;
}
