# We make separate GOAL variables for each algorithm, to make it easy to
# switch each to the Legacy provider when needed.

$DSA_GOAL=../../libdefault.a ../../libfips.a
$EC_GOAL=../../libdefault.a ../../libfips.a
$MAC_GOAL=../../libdefault.a ../../libfips.a
$RSA_GOAL=../../libdefault.a ../../libfips.a
$SM2_GOAL=../../libdefault.a

IF[{- !$disabled{dsa} -}]
  SOURCE[$DSA_GOAL]=dsa_sig.c
ENDIF

IF[{- !$disabled{ec} -}]
  SOURCE[$EC_GOAL]=eddsa_sig.c ecdsa_sig.c
ENDIF

IF[{- !$disabled{sm2} -}]
  SOURCE[$SM2_GOAL]=sm2_sig.c
ENDIF

SOURCE[$RSA_GOAL]=rsa_sig.c

DEPEND[rsa_sig.o]=../../common/include/prov/der_rsa.h
DEPEND[dsa_sig.o]=../../common/include/prov/der_dsa.h
DEPEND[ecdsa_sig.o]=../../common/include/prov/der_ec.h
DEPEND[eddsa_sig.o]=../../common/include/prov/der_ecx.h
DEPEND[sm2_sig.o]=../../common/include/prov/der_sm2.h

SOURCE[$MAC_GOAL]=mac_legacy_sig.c
