.text
.globl	sha256_block_data_order
.type	sha256_block_data_order,@function
.align	16
sha256_block_data_order:
.L_sha256_block_data_order_begin:
	%ifdef __CET__

.byte	243,15,30,251
	%endif

	pushl	%ebp
	pushl	%ebx
	pushl	%esi
	pushl	%edi
	movl	20(%esp),%esi
	movl	24(%esp),%edi
	movl	28(%esp),%eax
	movl	%esp,%ebx
	call	.L000pic_point
.L000pic_point:
	popl	%ebp
	leal	.L001K256-.L000pic_point(%ebp),%ebp
	subl	$16,%esp
	andl	$-64,%esp
	shll	$6,%eax
	addl	%edi,%eax
	movl	%esi,(%esp)
	movl	%edi,4(%esp)
	movl	%eax,8(%esp)
	movl	%ebx,12(%esp)
	leal	OPENSSL_ia32cap_P-.L001K256(%ebp),%edx
	movl	(%edx),%ecx
	movl	4(%edx),%ebx
	testl	$1048576,%ecx
	jnz	.L002loop
	movl	8(%edx),%edx
	testl	$16777216,%ecx
	jz	.L003no_xmm
	andl	$1073741824,%ecx
	andl	$268435968,%ebx
	testl	$536870912,%edx
	jnz	.L004shaext
	orl	%ebx,%ecx
	andl	$1342177280,%ecx
	cmpl	$1342177280,%ecx
	je	.L005AVX
	testl	$512,%ebx
	jnz	.L006SSSE3
.L003no_xmm:
	subl	%edi,%eax
	cmpl	$256,%eax
	jae	.L007unrolled
	jmp	.L002loop
.align	16
.L002loop:
	movl	(%edi),%eax
	movl	4(%edi),%ebx
	movl	8(%edi),%ecx
	bswap	%eax
	movl	12(%edi),%edx
	bswap	%ebx
	pushl	%eax
	bswap	%ecx
	pushl	%ebx
	bswap	%edx
	pushl	%ecx
	pushl	%edx
	movl	16(%edi),%eax
	movl	20(%edi),%ebx
	movl	24(%edi),%ecx
	bswap	%eax
	movl	28(%edi),%edx
	bswap	%ebx
	pushl	%eax
	bswap	%ecx
	pushl	%ebx
	bswap	%edx
	pushl	%ecx
	pushl	%edx
	movl	32(%edi),%eax
	movl	36(%edi),%ebx
	movl	40(%edi),%ecx
	bswap	%eax
	movl	44(%edi),%edx
	bswap	%ebx
	pushl	%eax
	bswap	%ecx
	pushl	%ebx
	bswap	%edx
	pushl	%ecx
	pushl	%edx
	movl	48(%edi),%eax
	movl	52(%edi),%ebx
	movl	56(%edi),%ecx
	bswap	%eax
	movl	60(%edi),%edx
	bswap	%ebx
	pushl	%eax
	bswap	%ecx
	pushl	%ebx
	bswap	%edx
	pushl	%ecx
	pushl	%edx
	addl	$64,%edi
	leal	-36(%esp),%esp
	movl	%edi,104(%esp)
	movl	(%esi),%eax
	movl	4(%esi),%ebx
	movl	8(%esi),%ecx
	movl	12(%esi),%edi
	movl	%ebx,8(%esp)
	xorl	%ecx,%ebx
	movl	%ecx,12(%esp)
	movl	%edi,16(%esp)
	movl	%ebx,(%esp)
	movl	16(%esi),%edx
	movl	20(%esi),%ebx
	movl	24(%esi),%ecx
	movl	28(%esi),%edi
	movl	%ebx,24(%esp)
	movl	%ecx,28(%esp)
	movl	%edi,32(%esp)
.align	16
.L00800_15:
	movl	%edx,%ecx
	movl	24(%esp),%esi
	rorl	$14,%ecx
	movl	28(%esp),%edi
	xorl	%edx,%ecx
	xorl	%edi,%esi
	movl	96(%esp),%ebx
	rorl	$5,%ecx
	andl	%edx,%esi
	movl	%edx,20(%esp)
	xorl	%ecx,%edx
	addl	32(%esp),%ebx
	xorl	%edi,%esi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%esi,%ebx
	rorl	$9,%ecx
	addl	%edx,%ebx
	movl	8(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,4(%esp)
	leal	-4(%esp),%esp
	rorl	$11,%ecx
	movl	(%ebp),%esi
	xorl	%eax,%ecx
	movl	20(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%esi,%ebx
	movl	%eax,(%esp)
	addl	%ebx,%edx
	andl	4(%esp),%eax
	addl	%ecx,%ebx
	xorl	%edi,%eax
	addl	$4,%ebp
	addl	%ebx,%eax
	cmpl	$3248222580,%esi
	jne	.L00800_15
	movl	156(%esp),%ecx
	jmp	.L00916_63
.align	16
.L00916_63:
	movl	%ecx,%ebx
	movl	104(%esp),%esi
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	160(%esp),%ebx
	shrl	$10,%edi
	addl	124(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	24(%esp),%esi
	rorl	$14,%ecx
	addl	%edi,%ebx
	movl	28(%esp),%edi
	xorl	%edx,%ecx
	xorl	%edi,%esi
	movl	%ebx,96(%esp)
	rorl	$5,%ecx
	andl	%edx,%esi
	movl	%edx,20(%esp)
	xorl	%ecx,%edx
	addl	32(%esp),%ebx
	xorl	%edi,%esi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%esi,%ebx
	rorl	$9,%ecx
	addl	%edx,%ebx
	movl	8(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,4(%esp)
	leal	-4(%esp),%esp
	rorl	$11,%ecx
	movl	(%ebp),%esi
	xorl	%eax,%ecx
	movl	20(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%esi,%ebx
	movl	%eax,(%esp)
	addl	%ebx,%edx
	andl	4(%esp),%eax
	addl	%ecx,%ebx
	xorl	%edi,%eax
	movl	156(%esp),%ecx
	addl	$4,%ebp
	addl	%ebx,%eax
	cmpl	$3329325298,%esi
	jne	.L00916_63
	movl	356(%esp),%esi
	movl	8(%esp),%ebx
	movl	16(%esp),%ecx
	addl	(%esi),%eax
	addl	4(%esi),%ebx
	addl	8(%esi),%edi
	addl	12(%esi),%ecx
	movl	%eax,(%esi)
	movl	%ebx,4(%esi)
	movl	%edi,8(%esi)
	movl	%ecx,12(%esi)
	movl	24(%esp),%eax
	movl	28(%esp),%ebx
	movl	32(%esp),%ecx
	movl	360(%esp),%edi
	addl	16(%esi),%edx
	addl	20(%esi),%eax
	addl	24(%esi),%ebx
	addl	28(%esi),%ecx
	movl	%edx,16(%esi)
	movl	%eax,20(%esi)
	movl	%ebx,24(%esi)
	movl	%ecx,28(%esi)
	leal	356(%esp),%esp
	subl	$256,%ebp
	cmpl	8(%esp),%edi
	jb	.L002loop
	movl	12(%esp),%esp
	popl	%edi
	popl	%esi
	popl	%ebx
	popl	%ebp
	ret
.align	64
.L001K256:
.long	1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298
.long	66051,67438087,134810123,202182159
.byte	83,72,65,50,53,54,32,98,108,111,99,107,32,116,114,97
.byte	110,115,102,111,114,109,32,102,111,114,32,120,56,54,44,32
.byte	67,82,89,80,84,79,71,65,77,83,32,98,121,32,60,97
.byte	112,112,114,111,64,111,112,101,110,115,115,108,46,111,114,103
.byte	62,0
.align	16
.L007unrolled:
	leal	-96(%esp),%esp
	movl	(%esi),%eax
	movl	4(%esi),%ebp
	movl	8(%esi),%ecx
	movl	12(%esi),%ebx
	movl	%ebp,4(%esp)
	xorl	%ecx,%ebp
	movl	%ecx,8(%esp)
	movl	%ebx,12(%esp)
	movl	16(%esi),%edx
	movl	20(%esi),%ebx
	movl	24(%esi),%ecx
	movl	28(%esi),%esi
	movl	%ebx,20(%esp)
	movl	%ecx,24(%esp)
	movl	%esi,28(%esp)
	jmp	.L010grand_loop
.align	16
.L010grand_loop:
	movl	(%edi),%ebx
	movl	4(%edi),%ecx
	bswap	%ebx
	movl	8(%edi),%esi
	bswap	%ecx
	movl	%ebx,32(%esp)
	bswap	%esi
	movl	%ecx,36(%esp)
	movl	%esi,40(%esp)
	movl	12(%edi),%ebx
	movl	16(%edi),%ecx
	bswap	%ebx
	movl	20(%edi),%esi
	bswap	%ecx
	movl	%ebx,44(%esp)
	bswap	%esi
	movl	%ecx,48(%esp)
	movl	%esi,52(%esp)
	movl	24(%edi),%ebx
	movl	28(%edi),%ecx
	bswap	%ebx
	movl	32(%edi),%esi
	bswap	%ecx
	movl	%ebx,56(%esp)
	bswap	%esi
	movl	%ecx,60(%esp)
	movl	%esi,64(%esp)
	movl	36(%edi),%ebx
	movl	40(%edi),%ecx
	bswap	%ebx
	movl	44(%edi),%esi
	bswap	%ecx
	movl	%ebx,68(%esp)
	bswap	%esi
	movl	%ecx,72(%esp)
	movl	%esi,76(%esp)
	movl	48(%edi),%ebx
	movl	52(%edi),%ecx
	bswap	%ebx
	movl	56(%edi),%esi
	bswap	%ecx
	movl	%ebx,80(%esp)
	bswap	%esi
	movl	%ecx,84(%esp)
	movl	%esi,88(%esp)
	movl	60(%edi),%ebx
	addl	$64,%edi
	bswap	%ebx
	movl	%edi,100(%esp)
	movl	%ebx,92(%esp)
	movl	%edx,%ecx
	movl	20(%esp),%esi
	rorl	$14,%edx
	movl	24(%esp),%edi
	xorl	%ecx,%edx
	movl	32(%esp),%ebx
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	addl	28(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	4(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	1116352408(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	12(%esp),%edx
	addl	%ecx,%ebp
	movl	%edx,%esi
	movl	16(%esp),%ecx
	rorl	$14,%edx
	movl	20(%esp),%edi
	xorl	%esi,%edx
	movl	36(%esp),%ebx
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,12(%esp)
	xorl	%esi,%edx
	addl	24(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,28(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	1899447441(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	rorl	$2,%esi
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%esi,%eax
	movl	%edx,%ecx
	movl	12(%esp),%esi
	rorl	$14,%edx
	movl	16(%esp),%edi
	xorl	%ecx,%edx
	movl	40(%esp),%ebx
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	addl	20(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	28(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,24(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	3049323471(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	4(%esp),%edx
	addl	%ecx,%ebp
	movl	%edx,%esi
	movl	8(%esp),%ecx
	rorl	$14,%edx
	movl	12(%esp),%edi
	xorl	%esi,%edx
	movl	44(%esp),%ebx
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,4(%esp)
	xorl	%esi,%edx
	addl	16(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	24(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,20(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	3921009573(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	rorl	$2,%esi
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%esi,%eax
	movl	%edx,%ecx
	movl	4(%esp),%esi
	rorl	$14,%edx
	movl	8(%esp),%edi
	xorl	%ecx,%edx
	movl	48(%esp),%ebx
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	addl	12(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	20(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,16(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	961987163(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	28(%esp),%edx
	addl	%ecx,%ebp
	movl	%edx,%esi
	movl	(%esp),%ecx
	rorl	$14,%edx
	movl	4(%esp),%edi
	xorl	%esi,%edx
	movl	52(%esp),%ebx
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,28(%esp)
	xorl	%esi,%edx
	addl	8(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	16(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,12(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	1508970993(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	rorl	$2,%esi
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%esi,%eax
	movl	%edx,%ecx
	movl	28(%esp),%esi
	rorl	$14,%edx
	movl	(%esp),%edi
	xorl	%ecx,%edx
	movl	56(%esp),%ebx
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	addl	4(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	12(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,8(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	2453635748(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	20(%esp),%edx
	addl	%ecx,%ebp
	movl	%edx,%esi
	movl	24(%esp),%ecx
	rorl	$14,%edx
	movl	28(%esp),%edi
	xorl	%esi,%edx
	movl	60(%esp),%ebx
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,20(%esp)
	xorl	%esi,%edx
	addl	(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	8(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,4(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	2870763221(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	rorl	$2,%esi
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%esi,%eax
	movl	%edx,%ecx
	movl	20(%esp),%esi
	rorl	$14,%edx
	movl	24(%esp),%edi
	xorl	%ecx,%edx
	movl	64(%esp),%ebx
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	addl	28(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	4(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	3624381080(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	12(%esp),%edx
	addl	%ecx,%ebp
	movl	%edx,%esi
	movl	16(%esp),%ecx
	rorl	$14,%edx
	movl	20(%esp),%edi
	xorl	%esi,%edx
	movl	68(%esp),%ebx
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,12(%esp)
	xorl	%esi,%edx
	addl	24(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,28(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	310598401(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	rorl	$2,%esi
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%esi,%eax
	movl	%edx,%ecx
	movl	12(%esp),%esi
	rorl	$14,%edx
	movl	16(%esp),%edi
	xorl	%ecx,%edx
	movl	72(%esp),%ebx
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	addl	20(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	28(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,24(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	607225278(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	4(%esp),%edx
	addl	%ecx,%ebp
	movl	%edx,%esi
	movl	8(%esp),%ecx
	rorl	$14,%edx
	movl	12(%esp),%edi
	xorl	%esi,%edx
	movl	76(%esp),%ebx
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,4(%esp)
	xorl	%esi,%edx
	addl	16(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	24(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,20(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	1426881987(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	rorl	$2,%esi
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%esi,%eax
	movl	%edx,%ecx
	movl	4(%esp),%esi
	rorl	$14,%edx
	movl	8(%esp),%edi
	xorl	%ecx,%edx
	movl	80(%esp),%ebx
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	addl	12(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	20(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,16(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	1925078388(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	28(%esp),%edx
	addl	%ecx,%ebp
	movl	%edx,%esi
	movl	(%esp),%ecx
	rorl	$14,%edx
	movl	4(%esp),%edi
	xorl	%esi,%edx
	movl	84(%esp),%ebx
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,28(%esp)
	xorl	%esi,%edx
	addl	8(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	16(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,12(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	2162078206(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	rorl	$2,%esi
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%esi,%eax
	movl	%edx,%ecx
	movl	28(%esp),%esi
	rorl	$14,%edx
	movl	(%esp),%edi
	xorl	%ecx,%edx
	movl	88(%esp),%ebx
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	addl	4(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	12(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,8(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	2614888103(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	20(%esp),%edx
	addl	%ecx,%ebp
	movl	%edx,%esi
	movl	24(%esp),%ecx
	rorl	$14,%edx
	movl	28(%esp),%edi
	xorl	%esi,%edx
	movl	92(%esp),%ebx
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,20(%esp)
	xorl	%esi,%edx
	addl	(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	8(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,4(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	3248222580(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	36(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%esi,%eax
	movl	88(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	32(%esp),%ebx
	shrl	$10,%edi
	addl	68(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	20(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	24(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,32(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	addl	28(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	4(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	3835390401(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	40(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	12(%esp),%edx
	addl	%ecx,%ebp
	movl	92(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	36(%esp),%ebx
	shrl	$10,%edi
	addl	72(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	16(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	20(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,36(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,12(%esp)
	xorl	%esi,%edx
	addl	24(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,28(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	4022224774(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	44(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%esi,%eax
	movl	32(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	40(%esp),%ebx
	shrl	$10,%edi
	addl	76(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	12(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	16(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,40(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	addl	20(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	28(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,24(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	264347078(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	48(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	4(%esp),%edx
	addl	%ecx,%ebp
	movl	36(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	44(%esp),%ebx
	shrl	$10,%edi
	addl	80(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	8(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	12(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,44(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,4(%esp)
	xorl	%esi,%edx
	addl	16(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	24(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,20(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	604807628(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	52(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%esi,%eax
	movl	40(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	48(%esp),%ebx
	shrl	$10,%edi
	addl	84(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	4(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	8(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,48(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	addl	12(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	20(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,16(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	770255983(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	56(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	28(%esp),%edx
	addl	%ecx,%ebp
	movl	44(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	52(%esp),%ebx
	shrl	$10,%edi
	addl	88(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	4(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,52(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,28(%esp)
	xorl	%esi,%edx
	addl	8(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	16(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,12(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	1249150122(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	60(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%esi,%eax
	movl	48(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	56(%esp),%ebx
	shrl	$10,%edi
	addl	92(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	28(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,56(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	addl	4(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	12(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,8(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	1555081692(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	64(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	20(%esp),%edx
	addl	%ecx,%ebp
	movl	52(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	60(%esp),%ebx
	shrl	$10,%edi
	addl	32(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	24(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	28(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,60(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,20(%esp)
	xorl	%esi,%edx
	addl	(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	8(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,4(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	1996064986(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	68(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%esi,%eax
	movl	56(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	64(%esp),%ebx
	shrl	$10,%edi
	addl	36(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	20(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	24(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,64(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	addl	28(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	4(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	2554220882(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	72(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	12(%esp),%edx
	addl	%ecx,%ebp
	movl	60(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	68(%esp),%ebx
	shrl	$10,%edi
	addl	40(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	16(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	20(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,68(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,12(%esp)
	xorl	%esi,%edx
	addl	24(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,28(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	2821834349(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	76(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%esi,%eax
	movl	64(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	72(%esp),%ebx
	shrl	$10,%edi
	addl	44(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	12(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	16(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,72(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	addl	20(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	28(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,24(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	2952996808(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	80(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	4(%esp),%edx
	addl	%ecx,%ebp
	movl	68(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	76(%esp),%ebx
	shrl	$10,%edi
	addl	48(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	8(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	12(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,76(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,4(%esp)
	xorl	%esi,%edx
	addl	16(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	24(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,20(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	3210313671(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	84(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%esi,%eax
	movl	72(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	80(%esp),%ebx
	shrl	$10,%edi
	addl	52(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	4(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	8(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,80(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	addl	12(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	20(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,16(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	3336571891(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	88(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	28(%esp),%edx
	addl	%ecx,%ebp
	movl	76(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	84(%esp),%ebx
	shrl	$10,%edi
	addl	56(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	4(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,84(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,28(%esp)
	xorl	%esi,%edx
	addl	8(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	16(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,12(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	3584528711(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	92(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%esi,%eax
	movl	80(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	88(%esp),%ebx
	shrl	$10,%edi
	addl	60(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	28(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,88(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	addl	4(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	12(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,8(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	113926993(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	32(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	20(%esp),%edx
	addl	%ecx,%ebp
	movl	84(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	92(%esp),%ebx
	shrl	$10,%edi
	addl	64(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	24(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	28(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,92(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,20(%esp)
	xorl	%esi,%edx
	addl	(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	8(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,4(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	338241895(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	36(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%esi,%eax
	movl	88(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	32(%esp),%ebx
	shrl	$10,%edi
	addl	68(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	20(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	24(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,32(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	addl	28(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	4(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	666307205(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	40(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	12(%esp),%edx
	addl	%ecx,%ebp
	movl	92(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	36(%esp),%ebx
	shrl	$10,%edi
	addl	72(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	16(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	20(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,36(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,12(%esp)
	xorl	%esi,%edx
	addl	24(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,28(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	773529912(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	44(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%esi,%eax
	movl	32(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	40(%esp),%ebx
	shrl	$10,%edi
	addl	76(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	12(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	16(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,40(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	addl	20(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	28(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,24(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	1294757372(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	48(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	4(%esp),%edx
	addl	%ecx,%ebp
	movl	36(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	44(%esp),%ebx
	shrl	$10,%edi
	addl	80(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	8(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	12(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,44(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,4(%esp)
	xorl	%esi,%edx
	addl	16(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	24(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,20(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	1396182291(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	52(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%esi,%eax
	movl	40(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	48(%esp),%ebx
	shrl	$10,%edi
	addl	84(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	4(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	8(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,48(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	addl	12(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	20(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,16(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	1695183700(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	56(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	28(%esp),%edx
	addl	%ecx,%ebp
	movl	44(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	52(%esp),%ebx
	shrl	$10,%edi
	addl	88(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	4(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,52(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,28(%esp)
	xorl	%esi,%edx
	addl	8(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	16(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,12(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	1986661051(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	60(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%esi,%eax
	movl	48(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	56(%esp),%ebx
	shrl	$10,%edi
	addl	92(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	28(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,56(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	addl	4(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	12(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,8(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	2177026350(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	64(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	20(%esp),%edx
	addl	%ecx,%ebp
	movl	52(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	60(%esp),%ebx
	shrl	$10,%edi
	addl	32(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	24(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	28(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,60(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,20(%esp)
	xorl	%esi,%edx
	addl	(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	8(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,4(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	2456956037(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	68(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%esi,%eax
	movl	56(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	64(%esp),%ebx
	shrl	$10,%edi
	addl	36(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	20(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	24(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,64(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	addl	28(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	4(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	2730485921(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	72(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	12(%esp),%edx
	addl	%ecx,%ebp
	movl	60(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	68(%esp),%ebx
	shrl	$10,%edi
	addl	40(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	16(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	20(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,68(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,12(%esp)
	xorl	%esi,%edx
	addl	24(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,28(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	2820302411(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	76(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%esi,%eax
	movl	64(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	72(%esp),%ebx
	shrl	$10,%edi
	addl	44(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	12(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	16(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,72(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	addl	20(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	28(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,24(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	3259730800(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	80(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	4(%esp),%edx
	addl	%ecx,%ebp
	movl	68(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	76(%esp),%ebx
	shrl	$10,%edi
	addl	48(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	8(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	12(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,76(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,4(%esp)
	xorl	%esi,%edx
	addl	16(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	24(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,20(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	3345764771(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	84(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%esi,%eax
	movl	72(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	80(%esp),%ebx
	shrl	$10,%edi
	addl	52(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	4(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	8(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,80(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	addl	12(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	20(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,16(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	3516065817(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	88(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	28(%esp),%edx
	addl	%ecx,%ebp
	movl	76(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	84(%esp),%ebx
	shrl	$10,%edi
	addl	56(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	4(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,84(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,28(%esp)
	xorl	%esi,%edx
	addl	8(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	16(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,12(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	3600352804(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	92(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%esi,%eax
	movl	80(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	88(%esp),%ebx
	shrl	$10,%edi
	addl	60(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	28(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,88(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	addl	4(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	12(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,8(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	4094571909(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	32(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	20(%esp),%edx
	addl	%ecx,%ebp
	movl	84(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	92(%esp),%ebx
	shrl	$10,%edi
	addl	64(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	24(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	28(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,92(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,20(%esp)
	xorl	%esi,%edx
	addl	(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	8(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,4(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	275423344(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	36(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%esi,%eax
	movl	88(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	32(%esp),%ebx
	shrl	$10,%edi
	addl	68(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	20(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	24(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,32(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	addl	28(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	4(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	430227734(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	40(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	12(%esp),%edx
	addl	%ecx,%ebp
	movl	92(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	36(%esp),%ebx
	shrl	$10,%edi
	addl	72(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	16(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	20(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,36(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,12(%esp)
	xorl	%esi,%edx
	addl	24(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,28(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	506948616(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	44(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%esi,%eax
	movl	32(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	40(%esp),%ebx
	shrl	$10,%edi
	addl	76(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	12(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	16(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,40(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	addl	20(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	28(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,24(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	659060556(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	48(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	4(%esp),%edx
	addl	%ecx,%ebp
	movl	36(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	44(%esp),%ebx
	shrl	$10,%edi
	addl	80(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	8(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	12(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,44(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,4(%esp)
	xorl	%esi,%edx
	addl	16(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	24(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,20(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	883997877(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	52(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%esi,%eax
	movl	40(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	48(%esp),%ebx
	shrl	$10,%edi
	addl	84(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	4(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	8(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,48(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	addl	12(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	20(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,16(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	958139571(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	56(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	28(%esp),%edx
	addl	%ecx,%ebp
	movl	44(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	52(%esp),%ebx
	shrl	$10,%edi
	addl	88(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	4(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,52(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,28(%esp)
	xorl	%esi,%edx
	addl	8(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	16(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,12(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	1322822218(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	60(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%esi,%eax
	movl	48(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	56(%esp),%ebx
	shrl	$10,%edi
	addl	92(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	28(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,56(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	addl	4(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	12(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,8(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	1537002063(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	64(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	20(%esp),%edx
	addl	%ecx,%ebp
	movl	52(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	60(%esp),%ebx
	shrl	$10,%edi
	addl	32(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	24(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	28(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,60(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,20(%esp)
	xorl	%esi,%edx
	addl	(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	8(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,4(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	1747873779(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	68(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%esi,%eax
	movl	56(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	64(%esp),%ebx
	shrl	$10,%edi
	addl	36(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	20(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	24(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,64(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	addl	28(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	4(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	1955562222(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	72(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	12(%esp),%edx
	addl	%ecx,%ebp
	movl	60(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	68(%esp),%ebx
	shrl	$10,%edi
	addl	40(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	16(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	20(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,68(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,12(%esp)
	xorl	%esi,%edx
	addl	24(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,28(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	2024104815(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	76(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%esi,%eax
	movl	64(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	72(%esp),%ebx
	shrl	$10,%edi
	addl	44(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	12(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	16(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,72(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	addl	20(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	28(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,24(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	2227730452(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	80(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	4(%esp),%edx
	addl	%ecx,%ebp
	movl	68(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	76(%esp),%ebx
	shrl	$10,%edi
	addl	48(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	8(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	12(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,76(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,4(%esp)
	xorl	%esi,%edx
	addl	16(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	24(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,20(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	2361852424(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	84(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%esi,%eax
	movl	72(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	80(%esp),%ebx
	shrl	$10,%edi
	addl	52(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	4(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	8(%esp),%edi
	xorl	%ecx,%edx
	movl	%ebx,80(%esp)
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	addl	12(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	20(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,16(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	2428436474(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	88(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	28(%esp),%edx
	addl	%ecx,%ebp
	movl	76(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	84(%esp),%ebx
	shrl	$10,%edi
	addl	56(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	4(%esp),%edi
	xorl	%esi,%edx
	movl	%ebx,84(%esp)
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,28(%esp)
	xorl	%esi,%edx
	addl	8(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	16(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,12(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	2756734187(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	movl	92(%esp),%ecx
	rorl	$2,%esi
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%esi,%eax
	movl	80(%esp),%esi
	movl	%ecx,%ebx
	rorl	$11,%ecx
	movl	%esi,%edi
	rorl	$2,%esi
	xorl	%ebx,%ecx
	shrl	$3,%ebx
	rorl	$7,%ecx
	xorl	%edi,%esi
	xorl	%ecx,%ebx
	rorl	$17,%esi
	addl	88(%esp),%ebx
	shrl	$10,%edi
	addl	60(%esp),%ebx
	movl	%edx,%ecx
	xorl	%esi,%edi
	movl	28(%esp),%esi
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	(%esp),%edi
	xorl	%ecx,%edx
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	addl	4(%esp),%ebx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%ebx
	rorl	$9,%ecx
	movl	%eax,%esi
	movl	12(%esp),%edi
	xorl	%eax,%ecx
	movl	%eax,8(%esp)
	xorl	%edi,%eax
	rorl	$11,%ecx
	andl	%eax,%ebp
	leal	3204031479(%ebx,%edx,1),%edx
	xorl	%esi,%ecx
	xorl	%edi,%ebp
	movl	32(%esp),%esi
	rorl	$2,%ecx
	addl	%edx,%ebp
	addl	20(%esp),%edx
	addl	%ecx,%ebp
	movl	84(%esp),%ecx
	movl	%esi,%ebx
	rorl	$11,%esi
	movl	%ecx,%edi
	rorl	$2,%ecx
	xorl	%ebx,%esi
	shrl	$3,%ebx
	rorl	$7,%esi
	xorl	%edi,%ecx
	xorl	%esi,%ebx
	rorl	$17,%ecx
	addl	92(%esp),%ebx
	shrl	$10,%edi
	addl	64(%esp),%ebx
	movl	%edx,%esi
	xorl	%ecx,%edi
	movl	24(%esp),%ecx
	rorl	$14,%edx
	addl	%edi,%ebx
	movl	28(%esp),%edi
	xorl	%esi,%edx
	xorl	%edi,%ecx
	rorl	$5,%edx
	andl	%esi,%ecx
	movl	%esi,20(%esp)
	xorl	%esi,%edx
	addl	(%esp),%ebx
	xorl	%ecx,%edi
	rorl	$6,%edx
	movl	%ebp,%esi
	addl	%edi,%ebx
	rorl	$9,%esi
	movl	%ebp,%ecx
	movl	8(%esp),%edi
	xorl	%ebp,%esi
	movl	%ebp,4(%esp)
	xorl	%edi,%ebp
	rorl	$11,%esi
	andl	%ebp,%eax
	leal	3329325298(%ebx,%edx,1),%edx
	xorl	%ecx,%esi
	xorl	%edi,%eax
	rorl	$2,%esi
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%esi,%eax
	movl	96(%esp),%esi
	xorl	%edi,%ebp
	movl	12(%esp),%ecx
	addl	(%esi),%eax
	addl	4(%esi),%ebp
	addl	8(%esi),%edi
	addl	12(%esi),%ecx
	movl	%eax,(%esi)
	movl	%ebp,4(%esi)
	movl	%edi,8(%esi)
	movl	%ecx,12(%esi)
	movl	%ebp,4(%esp)
	xorl	%edi,%ebp
	movl	%edi,8(%esp)
	movl	%ecx,12(%esp)
	movl	20(%esp),%edi
	movl	24(%esp),%ebx
	movl	28(%esp),%ecx
	addl	16(%esi),%edx
	addl	20(%esi),%edi
	addl	24(%esi),%ebx
	addl	28(%esi),%ecx
	movl	%edx,16(%esi)
	movl	%edi,20(%esi)
	movl	%ebx,24(%esi)
	movl	%ecx,28(%esi)
	movl	%edi,20(%esp)
	movl	100(%esp),%edi
	movl	%ebx,24(%esp)
	movl	%ecx,28(%esp)
	cmpl	104(%esp),%edi
	jb	.L010grand_loop
	movl	108(%esp),%esp
	popl	%edi
	popl	%esi
	popl	%ebx
	popl	%ebp
	ret
.align	32
.L004shaext:
	subl	$32,%esp
	movdqu	(%esi),%xmm1
	leal	128(%ebp),%ebp
	movdqu	16(%esi),%xmm2
	movdqa	128(%ebp),%xmm7
	pshufd	$27,%xmm1,%xmm0
	pshufd	$177,%xmm1,%xmm1
	pshufd	$27,%xmm2,%xmm2
.byte	102,15,58,15,202,8
	punpcklqdq	%xmm0,%xmm2
	jmp	.L011loop_shaext
.align	16
.L011loop_shaext:
	movdqu	(%edi),%xmm3
	movdqu	16(%edi),%xmm4
	movdqu	32(%edi),%xmm5
.byte	102,15,56,0,223
	movdqu	48(%edi),%xmm6
	movdqa	%xmm2,16(%esp)
	movdqa	-128(%ebp),%xmm0
	paddd	%xmm3,%xmm0
.byte	102,15,56,0,231
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	nop
	movdqa	%xmm1,(%esp)
.byte	15,56,203,202
	movdqa	-112(%ebp),%xmm0
	paddd	%xmm4,%xmm0
.byte	102,15,56,0,239
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	leal	64(%edi),%edi
.byte	15,56,204,220
.byte	15,56,203,202
	movdqa	-96(%ebp),%xmm0
	paddd	%xmm5,%xmm0
.byte	102,15,56,0,247
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm6,%xmm7
.byte	102,15,58,15,253,4
	nop
	paddd	%xmm7,%xmm3
.byte	15,56,204,229
.byte	15,56,203,202
	movdqa	-80(%ebp),%xmm0
	paddd	%xmm6,%xmm0
.byte	15,56,205,222
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm3,%xmm7
.byte	102,15,58,15,254,4
	nop
	paddd	%xmm7,%xmm4
.byte	15,56,204,238
.byte	15,56,203,202
	movdqa	-64(%ebp),%xmm0
	paddd	%xmm3,%xmm0
.byte	15,56,205,227
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm4,%xmm7
.byte	102,15,58,15,251,4
	nop
	paddd	%xmm7,%xmm5
.byte	15,56,204,243
.byte	15,56,203,202
	movdqa	-48(%ebp),%xmm0
	paddd	%xmm4,%xmm0
.byte	15,56,205,236
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm5,%xmm7
.byte	102,15,58,15,252,4
	nop
	paddd	%xmm7,%xmm6
.byte	15,56,204,220
.byte	15,56,203,202
	movdqa	-32(%ebp),%xmm0
	paddd	%xmm5,%xmm0
.byte	15,56,205,245
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm6,%xmm7
.byte	102,15,58,15,253,4
	nop
	paddd	%xmm7,%xmm3
.byte	15,56,204,229
.byte	15,56,203,202
	movdqa	-16(%ebp),%xmm0
	paddd	%xmm6,%xmm0
.byte	15,56,205,222
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm3,%xmm7
.byte	102,15,58,15,254,4
	nop
	paddd	%xmm7,%xmm4
.byte	15,56,204,238
.byte	15,56,203,202
	movdqa	(%ebp),%xmm0
	paddd	%xmm3,%xmm0
.byte	15,56,205,227
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm4,%xmm7
.byte	102,15,58,15,251,4
	nop
	paddd	%xmm7,%xmm5
.byte	15,56,204,243
.byte	15,56,203,202
	movdqa	16(%ebp),%xmm0
	paddd	%xmm4,%xmm0
.byte	15,56,205,236
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm5,%xmm7
.byte	102,15,58,15,252,4
	nop
	paddd	%xmm7,%xmm6
.byte	15,56,204,220
.byte	15,56,203,202
	movdqa	32(%ebp),%xmm0
	paddd	%xmm5,%xmm0
.byte	15,56,205,245
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm6,%xmm7
.byte	102,15,58,15,253,4
	nop
	paddd	%xmm7,%xmm3
.byte	15,56,204,229
.byte	15,56,203,202
	movdqa	48(%ebp),%xmm0
	paddd	%xmm6,%xmm0
.byte	15,56,205,222
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm3,%xmm7
.byte	102,15,58,15,254,4
	nop
	paddd	%xmm7,%xmm4
.byte	15,56,204,238
.byte	15,56,203,202
	movdqa	64(%ebp),%xmm0
	paddd	%xmm3,%xmm0
.byte	15,56,205,227
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm4,%xmm7
.byte	102,15,58,15,251,4
	nop
	paddd	%xmm7,%xmm5
.byte	15,56,204,243
.byte	15,56,203,202
	movdqa	80(%ebp),%xmm0
	paddd	%xmm4,%xmm0
.byte	15,56,205,236
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	movdqa	%xmm5,%xmm7
.byte	102,15,58,15,252,4
.byte	15,56,203,202
	paddd	%xmm7,%xmm6
	movdqa	96(%ebp),%xmm0
	paddd	%xmm5,%xmm0
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
.byte	15,56,205,245
	movdqa	128(%ebp),%xmm7
.byte	15,56,203,202
	movdqa	112(%ebp),%xmm0
	paddd	%xmm6,%xmm0
	nop
.byte	15,56,203,209
	pshufd	$14,%xmm0,%xmm0
	cmpl	%edi,%eax
	nop
.byte	15,56,203,202
	paddd	16(%esp),%xmm2
	paddd	(%esp),%xmm1
	jnz	.L011loop_shaext
	pshufd	$177,%xmm2,%xmm2
	pshufd	$27,%xmm1,%xmm7
	pshufd	$177,%xmm1,%xmm1
	punpckhqdq	%xmm2,%xmm1
.byte	102,15,58,15,215,8
	movl	44(%esp),%esp
	movdqu	%xmm1,(%esi)
	movdqu	%xmm2,16(%esi)
	popl	%edi
	popl	%esi
	popl	%ebx
	popl	%ebp
	ret
.align	32
.L006SSSE3:
	leal	-96(%esp),%esp
	movl	(%esi),%eax
	movl	4(%esi),%ebx
	movl	8(%esi),%ecx
	movl	12(%esi),%edi
	movl	%ebx,4(%esp)
	xorl	%ecx,%ebx
	movl	%ecx,8(%esp)
	movl	%edi,12(%esp)
	movl	16(%esi),%edx
	movl	20(%esi),%edi
	movl	24(%esi),%ecx
	movl	28(%esi),%esi
	movl	%edi,20(%esp)
	movl	100(%esp),%edi
	movl	%ecx,24(%esp)
	movl	%esi,28(%esp)
	movdqa	256(%ebp),%xmm7
	jmp	.L012grand_ssse3
.align	16
.L012grand_ssse3:
	movdqu	(%edi),%xmm0
	movdqu	16(%edi),%xmm1
	movdqu	32(%edi),%xmm2
	movdqu	48(%edi),%xmm3
	addl	$64,%edi
.byte	102,15,56,0,199
	movl	%edi,100(%esp)
.byte	102,15,56,0,207
	movdqa	(%ebp),%xmm4
.byte	102,15,56,0,215
	movdqa	16(%ebp),%xmm5
	paddd	%xmm0,%xmm4
.byte	102,15,56,0,223
	movdqa	32(%ebp),%xmm6
	paddd	%xmm1,%xmm5
	movdqa	48(%ebp),%xmm7
	movdqa	%xmm4,32(%esp)
	paddd	%xmm2,%xmm6
	movdqa	%xmm5,48(%esp)
	paddd	%xmm3,%xmm7
	movdqa	%xmm6,64(%esp)
	movdqa	%xmm7,80(%esp)
	jmp	.L013ssse3_00_47
.align	16
.L013ssse3_00_47:
	addl	$64,%ebp
	movl	%edx,%ecx
	movdqa	%xmm1,%xmm4
	rorl	$14,%edx
	movl	20(%esp),%esi
	movdqa	%xmm3,%xmm7
	xorl	%ecx,%edx
	movl	24(%esp),%edi
.byte	102,15,58,15,224,4
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
.byte	102,15,58,15,250,4
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	movdqa	%xmm4,%xmm5
	rorl	$6,%edx
	movl	%eax,%ecx
	movdqa	%xmm4,%xmm6
	addl	%edi,%edx
	movl	4(%esp),%edi
	psrld	$3,%xmm4
	movl	%eax,%esi
	rorl	$9,%ecx
	paddd	%xmm7,%xmm0
	movl	%eax,(%esp)
	xorl	%eax,%ecx
	psrld	$7,%xmm6
	xorl	%edi,%eax
	addl	28(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	pshufd	$250,%xmm3,%xmm7
	xorl	%esi,%ecx
	addl	32(%esp),%edx
	pslld	$14,%xmm5
	xorl	%edi,%ebx
	rorl	$2,%ecx
	pxor	%xmm6,%xmm4
	addl	%edx,%ebx
	addl	12(%esp),%edx
	psrld	$11,%xmm6
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	pxor	%xmm5,%xmm4
	movl	16(%esp),%esi
	xorl	%ecx,%edx
	pslld	$11,%xmm5
	movl	20(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	pxor	%xmm6,%xmm4
	andl	%ecx,%esi
	movl	%ecx,12(%esp)
	movdqa	%xmm7,%xmm6
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	pxor	%xmm5,%xmm4
	movl	%ebx,%ecx
	addl	%edi,%edx
	psrld	$10,%xmm7
	movl	(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	paddd	%xmm4,%xmm0
	movl	%ebx,28(%esp)
	xorl	%ebx,%ecx
	psrlq	$17,%xmm6
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	rorl	$11,%ecx
	pxor	%xmm6,%xmm7
	andl	%ebx,%eax
	xorl	%esi,%ecx
	psrlq	$2,%xmm6
	addl	36(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	pxor	%xmm6,%xmm7
	addl	%edx,%eax
	addl	8(%esp),%edx
	pshufd	$128,%xmm7,%xmm7
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	12(%esp),%esi
	xorl	%ecx,%edx
	movl	16(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	psrldq	$8,%xmm7
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	paddd	%xmm7,%xmm0
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	28(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,24(%esp)
	pshufd	$80,%xmm0,%xmm7
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	movdqa	%xmm7,%xmm6
	rorl	$11,%ecx
	psrld	$10,%xmm7
	andl	%eax,%ebx
	psrlq	$17,%xmm6
	xorl	%esi,%ecx
	addl	40(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	pxor	%xmm6,%xmm7
	addl	%edx,%ebx
	addl	4(%esp),%edx
	psrlq	$2,%xmm6
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	pxor	%xmm6,%xmm7
	movl	8(%esp),%esi
	xorl	%ecx,%edx
	movl	12(%esp),%edi
	pshufd	$8,%xmm7,%xmm7
	xorl	%edi,%esi
	rorl	$5,%edx
	movdqa	(%ebp),%xmm6
	andl	%ecx,%esi
	movl	%ecx,4(%esp)
	pslldq	$8,%xmm7
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	24(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	paddd	%xmm7,%xmm0
	movl	%ebx,20(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	paddd	%xmm0,%xmm6
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	44(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%ecx,%eax
	movdqa	%xmm6,32(%esp)
	movl	%edx,%ecx
	movdqa	%xmm2,%xmm4
	rorl	$14,%edx
	movl	4(%esp),%esi
	movdqa	%xmm0,%xmm7
	xorl	%ecx,%edx
	movl	8(%esp),%edi
.byte	102,15,58,15,225,4
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
.byte	102,15,58,15,251,4
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	movdqa	%xmm4,%xmm5
	rorl	$6,%edx
	movl	%eax,%ecx
	movdqa	%xmm4,%xmm6
	addl	%edi,%edx
	movl	20(%esp),%edi
	psrld	$3,%xmm4
	movl	%eax,%esi
	rorl	$9,%ecx
	paddd	%xmm7,%xmm1
	movl	%eax,16(%esp)
	xorl	%eax,%ecx
	psrld	$7,%xmm6
	xorl	%edi,%eax
	addl	12(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	pshufd	$250,%xmm0,%xmm7
	xorl	%esi,%ecx
	addl	48(%esp),%edx
	pslld	$14,%xmm5
	xorl	%edi,%ebx
	rorl	$2,%ecx
	pxor	%xmm6,%xmm4
	addl	%edx,%ebx
	addl	28(%esp),%edx
	psrld	$11,%xmm6
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	pxor	%xmm5,%xmm4
	movl	(%esp),%esi
	xorl	%ecx,%edx
	pslld	$11,%xmm5
	movl	4(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	pxor	%xmm6,%xmm4
	andl	%ecx,%esi
	movl	%ecx,28(%esp)
	movdqa	%xmm7,%xmm6
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	pxor	%xmm5,%xmm4
	movl	%ebx,%ecx
	addl	%edi,%edx
	psrld	$10,%xmm7
	movl	16(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	paddd	%xmm4,%xmm1
	movl	%ebx,12(%esp)
	xorl	%ebx,%ecx
	psrlq	$17,%xmm6
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	rorl	$11,%ecx
	pxor	%xmm6,%xmm7
	andl	%ebx,%eax
	xorl	%esi,%ecx
	psrlq	$2,%xmm6
	addl	52(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	pxor	%xmm6,%xmm7
	addl	%edx,%eax
	addl	24(%esp),%edx
	pshufd	$128,%xmm7,%xmm7
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	28(%esp),%esi
	xorl	%ecx,%edx
	movl	(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	psrldq	$8,%xmm7
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	paddd	%xmm7,%xmm1
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	12(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,8(%esp)
	pshufd	$80,%xmm1,%xmm7
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	movdqa	%xmm7,%xmm6
	rorl	$11,%ecx
	psrld	$10,%xmm7
	andl	%eax,%ebx
	psrlq	$17,%xmm6
	xorl	%esi,%ecx
	addl	56(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	pxor	%xmm6,%xmm7
	addl	%edx,%ebx
	addl	20(%esp),%edx
	psrlq	$2,%xmm6
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	pxor	%xmm6,%xmm7
	movl	24(%esp),%esi
	xorl	%ecx,%edx
	movl	28(%esp),%edi
	pshufd	$8,%xmm7,%xmm7
	xorl	%edi,%esi
	rorl	$5,%edx
	movdqa	16(%ebp),%xmm6
	andl	%ecx,%esi
	movl	%ecx,20(%esp)
	pslldq	$8,%xmm7
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	8(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	paddd	%xmm7,%xmm1
	movl	%ebx,4(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	paddd	%xmm1,%xmm6
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	60(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%ecx,%eax
	movdqa	%xmm6,48(%esp)
	movl	%edx,%ecx
	movdqa	%xmm3,%xmm4
	rorl	$14,%edx
	movl	20(%esp),%esi
	movdqa	%xmm1,%xmm7
	xorl	%ecx,%edx
	movl	24(%esp),%edi
.byte	102,15,58,15,226,4
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
.byte	102,15,58,15,248,4
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	movdqa	%xmm4,%xmm5
	rorl	$6,%edx
	movl	%eax,%ecx
	movdqa	%xmm4,%xmm6
	addl	%edi,%edx
	movl	4(%esp),%edi
	psrld	$3,%xmm4
	movl	%eax,%esi
	rorl	$9,%ecx
	paddd	%xmm7,%xmm2
	movl	%eax,(%esp)
	xorl	%eax,%ecx
	psrld	$7,%xmm6
	xorl	%edi,%eax
	addl	28(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	pshufd	$250,%xmm1,%xmm7
	xorl	%esi,%ecx
	addl	64(%esp),%edx
	pslld	$14,%xmm5
	xorl	%edi,%ebx
	rorl	$2,%ecx
	pxor	%xmm6,%xmm4
	addl	%edx,%ebx
	addl	12(%esp),%edx
	psrld	$11,%xmm6
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	pxor	%xmm5,%xmm4
	movl	16(%esp),%esi
	xorl	%ecx,%edx
	pslld	$11,%xmm5
	movl	20(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	pxor	%xmm6,%xmm4
	andl	%ecx,%esi
	movl	%ecx,12(%esp)
	movdqa	%xmm7,%xmm6
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	pxor	%xmm5,%xmm4
	movl	%ebx,%ecx
	addl	%edi,%edx
	psrld	$10,%xmm7
	movl	(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	paddd	%xmm4,%xmm2
	movl	%ebx,28(%esp)
	xorl	%ebx,%ecx
	psrlq	$17,%xmm6
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	rorl	$11,%ecx
	pxor	%xmm6,%xmm7
	andl	%ebx,%eax
	xorl	%esi,%ecx
	psrlq	$2,%xmm6
	addl	68(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	pxor	%xmm6,%xmm7
	addl	%edx,%eax
	addl	8(%esp),%edx
	pshufd	$128,%xmm7,%xmm7
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	12(%esp),%esi
	xorl	%ecx,%edx
	movl	16(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	psrldq	$8,%xmm7
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	paddd	%xmm7,%xmm2
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	28(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,24(%esp)
	pshufd	$80,%xmm2,%xmm7
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	movdqa	%xmm7,%xmm6
	rorl	$11,%ecx
	psrld	$10,%xmm7
	andl	%eax,%ebx
	psrlq	$17,%xmm6
	xorl	%esi,%ecx
	addl	72(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	pxor	%xmm6,%xmm7
	addl	%edx,%ebx
	addl	4(%esp),%edx
	psrlq	$2,%xmm6
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	pxor	%xmm6,%xmm7
	movl	8(%esp),%esi
	xorl	%ecx,%edx
	movl	12(%esp),%edi
	pshufd	$8,%xmm7,%xmm7
	xorl	%edi,%esi
	rorl	$5,%edx
	movdqa	32(%ebp),%xmm6
	andl	%ecx,%esi
	movl	%ecx,4(%esp)
	pslldq	$8,%xmm7
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	24(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	paddd	%xmm7,%xmm2
	movl	%ebx,20(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	paddd	%xmm2,%xmm6
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	76(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%ecx,%eax
	movdqa	%xmm6,64(%esp)
	movl	%edx,%ecx
	movdqa	%xmm0,%xmm4
	rorl	$14,%edx
	movl	4(%esp),%esi
	movdqa	%xmm2,%xmm7
	xorl	%ecx,%edx
	movl	8(%esp),%edi
.byte	102,15,58,15,227,4
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
.byte	102,15,58,15,249,4
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	movdqa	%xmm4,%xmm5
	rorl	$6,%edx
	movl	%eax,%ecx
	movdqa	%xmm4,%xmm6
	addl	%edi,%edx
	movl	20(%esp),%edi
	psrld	$3,%xmm4
	movl	%eax,%esi
	rorl	$9,%ecx
	paddd	%xmm7,%xmm3
	movl	%eax,16(%esp)
	xorl	%eax,%ecx
	psrld	$7,%xmm6
	xorl	%edi,%eax
	addl	12(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	pshufd	$250,%xmm2,%xmm7
	xorl	%esi,%ecx
	addl	80(%esp),%edx
	pslld	$14,%xmm5
	xorl	%edi,%ebx
	rorl	$2,%ecx
	pxor	%xmm6,%xmm4
	addl	%edx,%ebx
	addl	28(%esp),%edx
	psrld	$11,%xmm6
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	pxor	%xmm5,%xmm4
	movl	(%esp),%esi
	xorl	%ecx,%edx
	pslld	$11,%xmm5
	movl	4(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	pxor	%xmm6,%xmm4
	andl	%ecx,%esi
	movl	%ecx,28(%esp)
	movdqa	%xmm7,%xmm6
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	pxor	%xmm5,%xmm4
	movl	%ebx,%ecx
	addl	%edi,%edx
	psrld	$10,%xmm7
	movl	16(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	paddd	%xmm4,%xmm3
	movl	%ebx,12(%esp)
	xorl	%ebx,%ecx
	psrlq	$17,%xmm6
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	rorl	$11,%ecx
	pxor	%xmm6,%xmm7
	andl	%ebx,%eax
	xorl	%esi,%ecx
	psrlq	$2,%xmm6
	addl	84(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	pxor	%xmm6,%xmm7
	addl	%edx,%eax
	addl	24(%esp),%edx
	pshufd	$128,%xmm7,%xmm7
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	28(%esp),%esi
	xorl	%ecx,%edx
	movl	(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	psrldq	$8,%xmm7
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	paddd	%xmm7,%xmm3
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	12(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,8(%esp)
	pshufd	$80,%xmm3,%xmm7
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	movdqa	%xmm7,%xmm6
	rorl	$11,%ecx
	psrld	$10,%xmm7
	andl	%eax,%ebx
	psrlq	$17,%xmm6
	xorl	%esi,%ecx
	addl	88(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	pxor	%xmm6,%xmm7
	addl	%edx,%ebx
	addl	20(%esp),%edx
	psrlq	$2,%xmm6
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	pxor	%xmm6,%xmm7
	movl	24(%esp),%esi
	xorl	%ecx,%edx
	movl	28(%esp),%edi
	pshufd	$8,%xmm7,%xmm7
	xorl	%edi,%esi
	rorl	$5,%edx
	movdqa	48(%ebp),%xmm6
	andl	%ecx,%esi
	movl	%ecx,20(%esp)
	pslldq	$8,%xmm7
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	8(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	paddd	%xmm7,%xmm3
	movl	%ebx,4(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	paddd	%xmm3,%xmm6
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	92(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%ecx,%eax
	movdqa	%xmm6,80(%esp)
	cmpl	$66051,64(%ebp)
	jne	.L013ssse3_00_47
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	20(%esp),%esi
	xorl	%ecx,%edx
	movl	24(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	4(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	28(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	32(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	addl	%edx,%ebx
	addl	12(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	16(%esp),%esi
	xorl	%ecx,%edx
	movl	20(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,12(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	movl	%ebx,28(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	36(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	12(%esp),%esi
	xorl	%ecx,%edx
	movl	16(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	28(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,24(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	40(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	addl	%edx,%ebx
	addl	4(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	8(%esp),%esi
	xorl	%ecx,%edx
	movl	12(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,4(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	24(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	movl	%ebx,20(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	44(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	4(%esp),%esi
	xorl	%ecx,%edx
	movl	8(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	20(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,16(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	12(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	48(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	addl	%edx,%ebx
	addl	28(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	(%esp),%esi
	xorl	%ecx,%edx
	movl	4(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,28(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	16(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	movl	%ebx,12(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	52(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	28(%esp),%esi
	xorl	%ecx,%edx
	movl	(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	12(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,8(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	56(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	addl	%edx,%ebx
	addl	20(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	24(%esp),%esi
	xorl	%ecx,%edx
	movl	28(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,20(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	8(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	movl	%ebx,4(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	60(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	20(%esp),%esi
	xorl	%ecx,%edx
	movl	24(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	4(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	28(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	64(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	addl	%edx,%ebx
	addl	12(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	16(%esp),%esi
	xorl	%ecx,%edx
	movl	20(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,12(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	movl	%ebx,28(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	68(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	12(%esp),%esi
	xorl	%ecx,%edx
	movl	16(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	28(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,24(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	72(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	addl	%edx,%ebx
	addl	4(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	8(%esp),%esi
	xorl	%ecx,%edx
	movl	12(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,4(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	24(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	movl	%ebx,20(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	76(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	4(%esp),%esi
	xorl	%ecx,%edx
	movl	8(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	20(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,16(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	12(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	80(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	addl	%edx,%ebx
	addl	28(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	(%esp),%esi
	xorl	%ecx,%edx
	movl	4(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,28(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	16(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	movl	%ebx,12(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	84(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	28(%esp),%esi
	xorl	%ecx,%edx
	movl	(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	12(%esp),%edi
	movl	%eax,%esi
	rorl	$9,%ecx
	movl	%eax,8(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	rorl	$11,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	88(%esp),%edx
	xorl	%edi,%ebx
	rorl	$2,%ecx
	addl	%edx,%ebx
	addl	20(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	rorl	$14,%edx
	movl	24(%esp),%esi
	xorl	%ecx,%edx
	movl	28(%esp),%edi
	xorl	%edi,%esi
	rorl	$5,%edx
	andl	%ecx,%esi
	movl	%ecx,20(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	rorl	$6,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	8(%esp),%edi
	movl	%ebx,%esi
	rorl	$9,%ecx
	movl	%ebx,4(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	rorl	$11,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	92(%esp),%edx
	xorl	%edi,%eax
	rorl	$2,%ecx
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%ecx,%eax
	movl	96(%esp),%esi
	xorl	%edi,%ebx
	movl	12(%esp),%ecx
	addl	(%esi),%eax
	addl	4(%esi),%ebx
	addl	8(%esi),%edi
	addl	12(%esi),%ecx
	movl	%eax,(%esi)
	movl	%ebx,4(%esi)
	movl	%edi,8(%esi)
	movl	%ecx,12(%esi)
	movl	%ebx,4(%esp)
	xorl	%edi,%ebx
	movl	%edi,8(%esp)
	movl	%ecx,12(%esp)
	movl	20(%esp),%edi
	movl	24(%esp),%ecx
	addl	16(%esi),%edx
	addl	20(%esi),%edi
	addl	24(%esi),%ecx
	movl	%edx,16(%esi)
	movl	%edi,20(%esi)
	movl	%edi,20(%esp)
	movl	28(%esp),%edi
	movl	%ecx,24(%esi)
	addl	28(%esi),%edi
	movl	%ecx,24(%esp)
	movl	%edi,28(%esi)
	movl	%edi,28(%esp)
	movl	100(%esp),%edi
	movdqa	64(%ebp),%xmm7
	subl	$192,%ebp
	cmpl	104(%esp),%edi
	jb	.L012grand_ssse3
	movl	108(%esp),%esp
	popl	%edi
	popl	%esi
	popl	%ebx
	popl	%ebp
	ret
.align	32
.L005AVX:
	andl	$264,%edx
	cmpl	$264,%edx
	je	.L014AVX_BMI
	leal	-96(%esp),%esp
	vzeroall
	movl	(%esi),%eax
	movl	4(%esi),%ebx
	movl	8(%esi),%ecx
	movl	12(%esi),%edi
	movl	%ebx,4(%esp)
	xorl	%ecx,%ebx
	movl	%ecx,8(%esp)
	movl	%edi,12(%esp)
	movl	16(%esi),%edx
	movl	20(%esi),%edi
	movl	24(%esi),%ecx
	movl	28(%esi),%esi
	movl	%edi,20(%esp)
	movl	100(%esp),%edi
	movl	%ecx,24(%esp)
	movl	%esi,28(%esp)
	vmovdqa	256(%ebp),%xmm7
	jmp	.L015grand_avx
.align	32
.L015grand_avx:
	vmovdqu	(%edi),%xmm0
	vmovdqu	16(%edi),%xmm1
	vmovdqu	32(%edi),%xmm2
	vmovdqu	48(%edi),%xmm3
	addl	$64,%edi
	vpshufb	%xmm7,%xmm0,%xmm0
	movl	%edi,100(%esp)
	vpshufb	%xmm7,%xmm1,%xmm1
	vpshufb	%xmm7,%xmm2,%xmm2
	vpaddd	(%ebp),%xmm0,%xmm4
	vpshufb	%xmm7,%xmm3,%xmm3
	vpaddd	16(%ebp),%xmm1,%xmm5
	vpaddd	32(%ebp),%xmm2,%xmm6
	vpaddd	48(%ebp),%xmm3,%xmm7
	vmovdqa	%xmm4,32(%esp)
	vmovdqa	%xmm5,48(%esp)
	vmovdqa	%xmm6,64(%esp)
	vmovdqa	%xmm7,80(%esp)
	jmp	.L016avx_00_47
.align	16
.L016avx_00_47:
	addl	$64,%ebp
	vpalignr	$4,%xmm0,%xmm1,%xmm4
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	20(%esp),%esi
	vpalignr	$4,%xmm2,%xmm3,%xmm7
	xorl	%ecx,%edx
	movl	24(%esp),%edi
	xorl	%edi,%esi
	vpsrld	$7,%xmm4,%xmm6
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	vpaddd	%xmm7,%xmm0,%xmm0
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrld	$3,%xmm4,%xmm7
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	4(%esp),%edi
	vpslld	$14,%xmm4,%xmm5
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,(%esp)
	vpxor	%xmm6,%xmm7,%xmm4
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	28(%esp),%edx
	vpshufd	$250,%xmm3,%xmm7
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	vpsrld	$11,%xmm6,%xmm6
	addl	32(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	vpxor	%xmm5,%xmm4,%xmm4
	addl	%edx,%ebx
	addl	12(%esp),%edx
	addl	%ecx,%ebx
	vpslld	$11,%xmm5,%xmm5
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	16(%esp),%esi
	vpxor	%xmm6,%xmm4,%xmm4
	xorl	%ecx,%edx
	movl	20(%esp),%edi
	xorl	%edi,%esi
	vpsrld	$10,%xmm7,%xmm6
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,12(%esp)
	vpxor	%xmm5,%xmm4,%xmm4
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrlq	$17,%xmm7,%xmm5
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	(%esp),%edi
	vpaddd	%xmm4,%xmm0,%xmm0
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,28(%esp)
	vpxor	%xmm5,%xmm6,%xmm6
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	vpsrlq	$19,%xmm7,%xmm7
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	vpxor	%xmm7,%xmm6,%xmm6
	addl	36(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	vpshufd	$132,%xmm6,%xmm7
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%ecx,%eax
	vpsrldq	$8,%xmm7,%xmm7
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	12(%esp),%esi
	vpaddd	%xmm7,%xmm0,%xmm0
	xorl	%ecx,%edx
	movl	16(%esp),%edi
	xorl	%edi,%esi
	vpshufd	$80,%xmm0,%xmm7
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	vpsrld	$10,%xmm7,%xmm6
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrlq	$17,%xmm7,%xmm5
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	28(%esp),%edi
	vpxor	%xmm5,%xmm6,%xmm6
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,24(%esp)
	vpsrlq	$19,%xmm7,%xmm7
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	vpxor	%xmm7,%xmm6,%xmm6
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	vpshufd	$232,%xmm6,%xmm7
	addl	40(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	vpslldq	$8,%xmm7,%xmm7
	addl	%edx,%ebx
	addl	4(%esp),%edx
	addl	%ecx,%ebx
	vpaddd	%xmm7,%xmm0,%xmm0
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	8(%esp),%esi
	vpaddd	(%ebp),%xmm0,%xmm6
	xorl	%ecx,%edx
	movl	12(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,4(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	24(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,20(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	44(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%ecx,%eax
	vmovdqa	%xmm6,32(%esp)
	vpalignr	$4,%xmm1,%xmm2,%xmm4
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	4(%esp),%esi
	vpalignr	$4,%xmm3,%xmm0,%xmm7
	xorl	%ecx,%edx
	movl	8(%esp),%edi
	xorl	%edi,%esi
	vpsrld	$7,%xmm4,%xmm6
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	vpaddd	%xmm7,%xmm1,%xmm1
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrld	$3,%xmm4,%xmm7
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	20(%esp),%edi
	vpslld	$14,%xmm4,%xmm5
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,16(%esp)
	vpxor	%xmm6,%xmm7,%xmm4
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	12(%esp),%edx
	vpshufd	$250,%xmm0,%xmm7
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	vpsrld	$11,%xmm6,%xmm6
	addl	48(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	vpxor	%xmm5,%xmm4,%xmm4
	addl	%edx,%ebx
	addl	28(%esp),%edx
	addl	%ecx,%ebx
	vpslld	$11,%xmm5,%xmm5
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	(%esp),%esi
	vpxor	%xmm6,%xmm4,%xmm4
	xorl	%ecx,%edx
	movl	4(%esp),%edi
	xorl	%edi,%esi
	vpsrld	$10,%xmm7,%xmm6
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,28(%esp)
	vpxor	%xmm5,%xmm4,%xmm4
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrlq	$17,%xmm7,%xmm5
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	16(%esp),%edi
	vpaddd	%xmm4,%xmm1,%xmm1
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,12(%esp)
	vpxor	%xmm5,%xmm6,%xmm6
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	vpsrlq	$19,%xmm7,%xmm7
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	vpxor	%xmm7,%xmm6,%xmm6
	addl	52(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	vpshufd	$132,%xmm6,%xmm7
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%ecx,%eax
	vpsrldq	$8,%xmm7,%xmm7
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	28(%esp),%esi
	vpaddd	%xmm7,%xmm1,%xmm1
	xorl	%ecx,%edx
	movl	(%esp),%edi
	xorl	%edi,%esi
	vpshufd	$80,%xmm1,%xmm7
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	vpsrld	$10,%xmm7,%xmm6
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrlq	$17,%xmm7,%xmm5
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	12(%esp),%edi
	vpxor	%xmm5,%xmm6,%xmm6
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,8(%esp)
	vpsrlq	$19,%xmm7,%xmm7
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	vpxor	%xmm7,%xmm6,%xmm6
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	vpshufd	$232,%xmm6,%xmm7
	addl	56(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	vpslldq	$8,%xmm7,%xmm7
	addl	%edx,%ebx
	addl	20(%esp),%edx
	addl	%ecx,%ebx
	vpaddd	%xmm7,%xmm1,%xmm1
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	24(%esp),%esi
	vpaddd	16(%ebp),%xmm1,%xmm6
	xorl	%ecx,%edx
	movl	28(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,20(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	8(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,4(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	60(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%ecx,%eax
	vmovdqa	%xmm6,48(%esp)
	vpalignr	$4,%xmm2,%xmm3,%xmm4
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	20(%esp),%esi
	vpalignr	$4,%xmm0,%xmm1,%xmm7
	xorl	%ecx,%edx
	movl	24(%esp),%edi
	xorl	%edi,%esi
	vpsrld	$7,%xmm4,%xmm6
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	vpaddd	%xmm7,%xmm2,%xmm2
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrld	$3,%xmm4,%xmm7
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	4(%esp),%edi
	vpslld	$14,%xmm4,%xmm5
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,(%esp)
	vpxor	%xmm6,%xmm7,%xmm4
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	28(%esp),%edx
	vpshufd	$250,%xmm1,%xmm7
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	vpsrld	$11,%xmm6,%xmm6
	addl	64(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	vpxor	%xmm5,%xmm4,%xmm4
	addl	%edx,%ebx
	addl	12(%esp),%edx
	addl	%ecx,%ebx
	vpslld	$11,%xmm5,%xmm5
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	16(%esp),%esi
	vpxor	%xmm6,%xmm4,%xmm4
	xorl	%ecx,%edx
	movl	20(%esp),%edi
	xorl	%edi,%esi
	vpsrld	$10,%xmm7,%xmm6
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,12(%esp)
	vpxor	%xmm5,%xmm4,%xmm4
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrlq	$17,%xmm7,%xmm5
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	(%esp),%edi
	vpaddd	%xmm4,%xmm2,%xmm2
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,28(%esp)
	vpxor	%xmm5,%xmm6,%xmm6
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	vpsrlq	$19,%xmm7,%xmm7
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	vpxor	%xmm7,%xmm6,%xmm6
	addl	68(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	vpshufd	$132,%xmm6,%xmm7
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%ecx,%eax
	vpsrldq	$8,%xmm7,%xmm7
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	12(%esp),%esi
	vpaddd	%xmm7,%xmm2,%xmm2
	xorl	%ecx,%edx
	movl	16(%esp),%edi
	xorl	%edi,%esi
	vpshufd	$80,%xmm2,%xmm7
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	vpsrld	$10,%xmm7,%xmm6
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrlq	$17,%xmm7,%xmm5
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	28(%esp),%edi
	vpxor	%xmm5,%xmm6,%xmm6
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,24(%esp)
	vpsrlq	$19,%xmm7,%xmm7
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	vpxor	%xmm7,%xmm6,%xmm6
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	vpshufd	$232,%xmm6,%xmm7
	addl	72(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	vpslldq	$8,%xmm7,%xmm7
	addl	%edx,%ebx
	addl	4(%esp),%edx
	addl	%ecx,%ebx
	vpaddd	%xmm7,%xmm2,%xmm2
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	8(%esp),%esi
	vpaddd	32(%ebp),%xmm2,%xmm6
	xorl	%ecx,%edx
	movl	12(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,4(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	24(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,20(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	76(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%ecx,%eax
	vmovdqa	%xmm6,64(%esp)
	vpalignr	$4,%xmm3,%xmm0,%xmm4
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	4(%esp),%esi
	vpalignr	$4,%xmm1,%xmm2,%xmm7
	xorl	%ecx,%edx
	movl	8(%esp),%edi
	xorl	%edi,%esi
	vpsrld	$7,%xmm4,%xmm6
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	vpaddd	%xmm7,%xmm3,%xmm3
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrld	$3,%xmm4,%xmm7
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	20(%esp),%edi
	vpslld	$14,%xmm4,%xmm5
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,16(%esp)
	vpxor	%xmm6,%xmm7,%xmm4
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	12(%esp),%edx
	vpshufd	$250,%xmm2,%xmm7
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	vpsrld	$11,%xmm6,%xmm6
	addl	80(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	vpxor	%xmm5,%xmm4,%xmm4
	addl	%edx,%ebx
	addl	28(%esp),%edx
	addl	%ecx,%ebx
	vpslld	$11,%xmm5,%xmm5
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	(%esp),%esi
	vpxor	%xmm6,%xmm4,%xmm4
	xorl	%ecx,%edx
	movl	4(%esp),%edi
	xorl	%edi,%esi
	vpsrld	$10,%xmm7,%xmm6
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,28(%esp)
	vpxor	%xmm5,%xmm4,%xmm4
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrlq	$17,%xmm7,%xmm5
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	16(%esp),%edi
	vpaddd	%xmm4,%xmm3,%xmm3
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,12(%esp)
	vpxor	%xmm5,%xmm6,%xmm6
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	vpsrlq	$19,%xmm7,%xmm7
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	vpxor	%xmm7,%xmm6,%xmm6
	addl	84(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	vpshufd	$132,%xmm6,%xmm7
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%ecx,%eax
	vpsrldq	$8,%xmm7,%xmm7
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	28(%esp),%esi
	vpaddd	%xmm7,%xmm3,%xmm3
	xorl	%ecx,%edx
	movl	(%esp),%edi
	xorl	%edi,%esi
	vpshufd	$80,%xmm3,%xmm7
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	vpsrld	$10,%xmm7,%xmm6
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	vpsrlq	$17,%xmm7,%xmm5
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	12(%esp),%edi
	vpxor	%xmm5,%xmm6,%xmm6
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,8(%esp)
	vpsrlq	$19,%xmm7,%xmm7
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	vpxor	%xmm7,%xmm6,%xmm6
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	vpshufd	$232,%xmm6,%xmm7
	addl	88(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	vpslldq	$8,%xmm7,%xmm7
	addl	%edx,%ebx
	addl	20(%esp),%edx
	addl	%ecx,%ebx
	vpaddd	%xmm7,%xmm3,%xmm3
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	24(%esp),%esi
	vpaddd	48(%ebp),%xmm3,%xmm6
	xorl	%ecx,%edx
	movl	28(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,20(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	8(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,4(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	92(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%ecx,%eax
	vmovdqa	%xmm6,80(%esp)
	cmpl	$66051,64(%ebp)
	jne	.L016avx_00_47
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	20(%esp),%esi
	xorl	%ecx,%edx
	movl	24(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	4(%esp),%edi
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	28(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	32(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	addl	%edx,%ebx
	addl	12(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	16(%esp),%esi
	xorl	%ecx,%edx
	movl	20(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,12(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,28(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	36(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	12(%esp),%esi
	xorl	%ecx,%edx
	movl	16(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	28(%esp),%edi
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,24(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	40(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	addl	%edx,%ebx
	addl	4(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	8(%esp),%esi
	xorl	%ecx,%edx
	movl	12(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,4(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	24(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,20(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	44(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	4(%esp),%esi
	xorl	%ecx,%edx
	movl	8(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	20(%esp),%edi
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,16(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	12(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	48(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	addl	%edx,%ebx
	addl	28(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	(%esp),%esi
	xorl	%ecx,%edx
	movl	4(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,28(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	16(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,12(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	52(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	28(%esp),%esi
	xorl	%ecx,%edx
	movl	(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	12(%esp),%edi
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,8(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	56(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	addl	%edx,%ebx
	addl	20(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	24(%esp),%esi
	xorl	%ecx,%edx
	movl	28(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,20(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	8(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,4(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	60(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	20(%esp),%esi
	xorl	%ecx,%edx
	movl	24(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,16(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	4(%esp),%edi
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	28(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	64(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	addl	%edx,%ebx
	addl	12(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	16(%esp),%esi
	xorl	%ecx,%edx
	movl	20(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,12(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,28(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	68(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	8(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	12(%esp),%esi
	xorl	%ecx,%edx
	movl	16(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,8(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	28(%esp),%edi
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,24(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	72(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	addl	%edx,%ebx
	addl	4(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	8(%esp),%esi
	xorl	%ecx,%edx
	movl	12(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,4(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	24(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,20(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	76(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	4(%esp),%esi
	xorl	%ecx,%edx
	movl	8(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	20(%esp),%edi
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,16(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	12(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	80(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	addl	%edx,%ebx
	addl	28(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	(%esp),%esi
	xorl	%ecx,%edx
	movl	4(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,28(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	16(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,12(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	84(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	24(%esp),%edx
	addl	%ecx,%eax
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	28(%esp),%esi
	xorl	%ecx,%edx
	movl	(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,24(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%eax,%ecx
	addl	%edi,%edx
	movl	12(%esp),%edi
	movl	%eax,%esi
	shrdl	$9,%ecx,%ecx
	movl	%eax,8(%esp)
	xorl	%eax,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%eax,%ebx
	xorl	%esi,%ecx
	addl	88(%esp),%edx
	xorl	%edi,%ebx
	shrdl	$2,%ecx,%ecx
	addl	%edx,%ebx
	addl	20(%esp),%edx
	addl	%ecx,%ebx
	movl	%edx,%ecx
	shrdl	$14,%edx,%edx
	movl	24(%esp),%esi
	xorl	%ecx,%edx
	movl	28(%esp),%edi
	xorl	%edi,%esi
	shrdl	$5,%edx,%edx
	andl	%ecx,%esi
	movl	%ecx,20(%esp)
	xorl	%ecx,%edx
	xorl	%esi,%edi
	shrdl	$6,%edx,%edx
	movl	%ebx,%ecx
	addl	%edi,%edx
	movl	8(%esp),%edi
	movl	%ebx,%esi
	shrdl	$9,%ecx,%ecx
	movl	%ebx,4(%esp)
	xorl	%ebx,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	shrdl	$11,%ecx,%ecx
	andl	%ebx,%eax
	xorl	%esi,%ecx
	addl	92(%esp),%edx
	xorl	%edi,%eax
	shrdl	$2,%ecx,%ecx
	addl	%edx,%eax
	addl	16(%esp),%edx
	addl	%ecx,%eax
	movl	96(%esp),%esi
	xorl	%edi,%ebx
	movl	12(%esp),%ecx
	addl	(%esi),%eax
	addl	4(%esi),%ebx
	addl	8(%esi),%edi
	addl	12(%esi),%ecx
	movl	%eax,(%esi)
	movl	%ebx,4(%esi)
	movl	%edi,8(%esi)
	movl	%ecx,12(%esi)
	movl	%ebx,4(%esp)
	xorl	%edi,%ebx
	movl	%edi,8(%esp)
	movl	%ecx,12(%esp)
	movl	20(%esp),%edi
	movl	24(%esp),%ecx
	addl	16(%esi),%edx
	addl	20(%esi),%edi
	addl	24(%esi),%ecx
	movl	%edx,16(%esi)
	movl	%edi,20(%esi)
	movl	%edi,20(%esp)
	movl	28(%esp),%edi
	movl	%ecx,24(%esi)
	addl	28(%esi),%edi
	movl	%ecx,24(%esp)
	movl	%edi,28(%esi)
	movl	%edi,28(%esp)
	movl	100(%esp),%edi
	vmovdqa	64(%ebp),%xmm7
	subl	$192,%ebp
	cmpl	104(%esp),%edi
	jb	.L015grand_avx
	movl	108(%esp),%esp
	vzeroall
	popl	%edi
	popl	%esi
	popl	%ebx
	popl	%ebp
	ret
.align	32
.L014AVX_BMI:
	leal	-96(%esp),%esp
	vzeroall
	movl	(%esi),%eax
	movl	4(%esi),%ebx
	movl	8(%esi),%ecx
	movl	12(%esi),%edi
	movl	%ebx,4(%esp)
	xorl	%ecx,%ebx
	movl	%ecx,8(%esp)
	movl	%edi,12(%esp)
	movl	16(%esi),%edx
	movl	20(%esi),%edi
	movl	24(%esi),%ecx
	movl	28(%esi),%esi
	movl	%edi,20(%esp)
	movl	100(%esp),%edi
	movl	%ecx,24(%esp)
	movl	%esi,28(%esp)
	vmovdqa	256(%ebp),%xmm7
	jmp	.L017grand_avx_bmi
.align	32
.L017grand_avx_bmi:
	vmovdqu	(%edi),%xmm0
	vmovdqu	16(%edi),%xmm1
	vmovdqu	32(%edi),%xmm2
	vmovdqu	48(%edi),%xmm3
	addl	$64,%edi
	vpshufb	%xmm7,%xmm0,%xmm0
	movl	%edi,100(%esp)
	vpshufb	%xmm7,%xmm1,%xmm1
	vpshufb	%xmm7,%xmm2,%xmm2
	vpaddd	(%ebp),%xmm0,%xmm4
	vpshufb	%xmm7,%xmm3,%xmm3
	vpaddd	16(%ebp),%xmm1,%xmm5
	vpaddd	32(%ebp),%xmm2,%xmm6
	vpaddd	48(%ebp),%xmm3,%xmm7
	vmovdqa	%xmm4,32(%esp)
	vmovdqa	%xmm5,48(%esp)
	vmovdqa	%xmm6,64(%esp)
	vmovdqa	%xmm7,80(%esp)
	jmp	.L018avx_bmi_00_47
.align	16
.L018avx_bmi_00_47:
	addl	$64,%ebp
	vpalignr	$4,%xmm0,%xmm1,%xmm4
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,16(%esp)
	vpalignr	$4,%xmm2,%xmm3,%xmm7
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	24(%esp),%edx,%esi
	vpsrld	$7,%xmm4,%xmm6
	xorl	%edi,%ecx
	andl	20(%esp),%edx
	movl	%eax,(%esp)
	vpaddd	%xmm7,%xmm0,%xmm0
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	vpsrld	$3,%xmm4,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	vpslld	$14,%xmm4,%xmm5
	movl	4(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	vpxor	%xmm6,%xmm7,%xmm4
	addl	28(%esp),%edx
	andl	%eax,%ebx
	addl	32(%esp),%edx
	vpshufd	$250,%xmm3,%xmm7
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	12(%esp),%edx
	vpsrld	$11,%xmm6,%xmm6
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpxor	%xmm5,%xmm4,%xmm4
	movl	%edx,12(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpslld	$11,%xmm5,%xmm5
	andnl	20(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	16(%esp),%edx
	vpxor	%xmm6,%xmm4,%xmm4
	movl	%ebx,28(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	vpsrld	$10,%xmm7,%xmm6
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	vpxor	%xmm5,%xmm4,%xmm4
	movl	(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	vpsrlq	$17,%xmm7,%xmm5
	addl	24(%esp),%edx
	andl	%ebx,%eax
	addl	36(%esp),%edx
	vpaddd	%xmm4,%xmm0,%xmm0
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	8(%esp),%edx
	vpxor	%xmm5,%xmm6,%xmm6
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpsrlq	$19,%xmm7,%xmm7
	movl	%edx,8(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpxor	%xmm7,%xmm6,%xmm6
	andnl	16(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	12(%esp),%edx
	vpshufd	$132,%xmm6,%xmm7
	movl	%eax,24(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	vpsrldq	$8,%xmm7,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	vpaddd	%xmm7,%xmm0,%xmm0
	movl	28(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	vpshufd	$80,%xmm0,%xmm7
	addl	20(%esp),%edx
	andl	%eax,%ebx
	addl	40(%esp),%edx
	vpsrld	$10,%xmm7,%xmm6
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	4(%esp),%edx
	vpsrlq	$17,%xmm7,%xmm5
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpxor	%xmm5,%xmm6,%xmm6
	movl	%edx,4(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpsrlq	$19,%xmm7,%xmm7
	andnl	12(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	8(%esp),%edx
	vpxor	%xmm7,%xmm6,%xmm6
	movl	%ebx,20(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	vpshufd	$232,%xmm6,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	vpslldq	$8,%xmm7,%xmm7
	movl	24(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	vpaddd	%xmm7,%xmm0,%xmm0
	addl	16(%esp),%edx
	andl	%ebx,%eax
	addl	44(%esp),%edx
	vpaddd	(%ebp),%xmm0,%xmm6
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	vmovdqa	%xmm6,32(%esp)
	vpalignr	$4,%xmm1,%xmm2,%xmm4
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,(%esp)
	vpalignr	$4,%xmm3,%xmm0,%xmm7
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	8(%esp),%edx,%esi
	vpsrld	$7,%xmm4,%xmm6
	xorl	%edi,%ecx
	andl	4(%esp),%edx
	movl	%eax,16(%esp)
	vpaddd	%xmm7,%xmm1,%xmm1
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	vpsrld	$3,%xmm4,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	vpslld	$14,%xmm4,%xmm5
	movl	20(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	vpxor	%xmm6,%xmm7,%xmm4
	addl	12(%esp),%edx
	andl	%eax,%ebx
	addl	48(%esp),%edx
	vpshufd	$250,%xmm0,%xmm7
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	28(%esp),%edx
	vpsrld	$11,%xmm6,%xmm6
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpxor	%xmm5,%xmm4,%xmm4
	movl	%edx,28(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpslld	$11,%xmm5,%xmm5
	andnl	4(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	(%esp),%edx
	vpxor	%xmm6,%xmm4,%xmm4
	movl	%ebx,12(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	vpsrld	$10,%xmm7,%xmm6
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	vpxor	%xmm5,%xmm4,%xmm4
	movl	16(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	vpsrlq	$17,%xmm7,%xmm5
	addl	8(%esp),%edx
	andl	%ebx,%eax
	addl	52(%esp),%edx
	vpaddd	%xmm4,%xmm1,%xmm1
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	24(%esp),%edx
	vpxor	%xmm5,%xmm6,%xmm6
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpsrlq	$19,%xmm7,%xmm7
	movl	%edx,24(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpxor	%xmm7,%xmm6,%xmm6
	andnl	(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	28(%esp),%edx
	vpshufd	$132,%xmm6,%xmm7
	movl	%eax,8(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	vpsrldq	$8,%xmm7,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	vpaddd	%xmm7,%xmm1,%xmm1
	movl	12(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	vpshufd	$80,%xmm1,%xmm7
	addl	4(%esp),%edx
	andl	%eax,%ebx
	addl	56(%esp),%edx
	vpsrld	$10,%xmm7,%xmm6
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	20(%esp),%edx
	vpsrlq	$17,%xmm7,%xmm5
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpxor	%xmm5,%xmm6,%xmm6
	movl	%edx,20(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpsrlq	$19,%xmm7,%xmm7
	andnl	28(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	24(%esp),%edx
	vpxor	%xmm7,%xmm6,%xmm6
	movl	%ebx,4(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	vpshufd	$232,%xmm6,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	vpslldq	$8,%xmm7,%xmm7
	movl	8(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	vpaddd	%xmm7,%xmm1,%xmm1
	addl	(%esp),%edx
	andl	%ebx,%eax
	addl	60(%esp),%edx
	vpaddd	16(%ebp),%xmm1,%xmm6
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	16(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	vmovdqa	%xmm6,48(%esp)
	vpalignr	$4,%xmm2,%xmm3,%xmm4
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,16(%esp)
	vpalignr	$4,%xmm0,%xmm1,%xmm7
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	24(%esp),%edx,%esi
	vpsrld	$7,%xmm4,%xmm6
	xorl	%edi,%ecx
	andl	20(%esp),%edx
	movl	%eax,(%esp)
	vpaddd	%xmm7,%xmm2,%xmm2
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	vpsrld	$3,%xmm4,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	vpslld	$14,%xmm4,%xmm5
	movl	4(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	vpxor	%xmm6,%xmm7,%xmm4
	addl	28(%esp),%edx
	andl	%eax,%ebx
	addl	64(%esp),%edx
	vpshufd	$250,%xmm1,%xmm7
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	12(%esp),%edx
	vpsrld	$11,%xmm6,%xmm6
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpxor	%xmm5,%xmm4,%xmm4
	movl	%edx,12(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpslld	$11,%xmm5,%xmm5
	andnl	20(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	16(%esp),%edx
	vpxor	%xmm6,%xmm4,%xmm4
	movl	%ebx,28(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	vpsrld	$10,%xmm7,%xmm6
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	vpxor	%xmm5,%xmm4,%xmm4
	movl	(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	vpsrlq	$17,%xmm7,%xmm5
	addl	24(%esp),%edx
	andl	%ebx,%eax
	addl	68(%esp),%edx
	vpaddd	%xmm4,%xmm2,%xmm2
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	8(%esp),%edx
	vpxor	%xmm5,%xmm6,%xmm6
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpsrlq	$19,%xmm7,%xmm7
	movl	%edx,8(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpxor	%xmm7,%xmm6,%xmm6
	andnl	16(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	12(%esp),%edx
	vpshufd	$132,%xmm6,%xmm7
	movl	%eax,24(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	vpsrldq	$8,%xmm7,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	vpaddd	%xmm7,%xmm2,%xmm2
	movl	28(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	vpshufd	$80,%xmm2,%xmm7
	addl	20(%esp),%edx
	andl	%eax,%ebx
	addl	72(%esp),%edx
	vpsrld	$10,%xmm7,%xmm6
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	4(%esp),%edx
	vpsrlq	$17,%xmm7,%xmm5
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpxor	%xmm5,%xmm6,%xmm6
	movl	%edx,4(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpsrlq	$19,%xmm7,%xmm7
	andnl	12(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	8(%esp),%edx
	vpxor	%xmm7,%xmm6,%xmm6
	movl	%ebx,20(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	vpshufd	$232,%xmm6,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	vpslldq	$8,%xmm7,%xmm7
	movl	24(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	vpaddd	%xmm7,%xmm2,%xmm2
	addl	16(%esp),%edx
	andl	%ebx,%eax
	addl	76(%esp),%edx
	vpaddd	32(%ebp),%xmm2,%xmm6
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	vmovdqa	%xmm6,64(%esp)
	vpalignr	$4,%xmm3,%xmm0,%xmm4
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,(%esp)
	vpalignr	$4,%xmm1,%xmm2,%xmm7
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	8(%esp),%edx,%esi
	vpsrld	$7,%xmm4,%xmm6
	xorl	%edi,%ecx
	andl	4(%esp),%edx
	movl	%eax,16(%esp)
	vpaddd	%xmm7,%xmm3,%xmm3
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	vpsrld	$3,%xmm4,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	vpslld	$14,%xmm4,%xmm5
	movl	20(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	vpxor	%xmm6,%xmm7,%xmm4
	addl	12(%esp),%edx
	andl	%eax,%ebx
	addl	80(%esp),%edx
	vpshufd	$250,%xmm2,%xmm7
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	28(%esp),%edx
	vpsrld	$11,%xmm6,%xmm6
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpxor	%xmm5,%xmm4,%xmm4
	movl	%edx,28(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpslld	$11,%xmm5,%xmm5
	andnl	4(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	(%esp),%edx
	vpxor	%xmm6,%xmm4,%xmm4
	movl	%ebx,12(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	vpsrld	$10,%xmm7,%xmm6
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	vpxor	%xmm5,%xmm4,%xmm4
	movl	16(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	vpsrlq	$17,%xmm7,%xmm5
	addl	8(%esp),%edx
	andl	%ebx,%eax
	addl	84(%esp),%edx
	vpaddd	%xmm4,%xmm3,%xmm3
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	24(%esp),%edx
	vpxor	%xmm5,%xmm6,%xmm6
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpsrlq	$19,%xmm7,%xmm7
	movl	%edx,24(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpxor	%xmm7,%xmm6,%xmm6
	andnl	(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	28(%esp),%edx
	vpshufd	$132,%xmm6,%xmm7
	movl	%eax,8(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	vpsrldq	$8,%xmm7,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	vpaddd	%xmm7,%xmm3,%xmm3
	movl	12(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	vpshufd	$80,%xmm3,%xmm7
	addl	4(%esp),%edx
	andl	%eax,%ebx
	addl	88(%esp),%edx
	vpsrld	$10,%xmm7,%xmm6
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	20(%esp),%edx
	vpsrlq	$17,%xmm7,%xmm5
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	vpxor	%xmm5,%xmm6,%xmm6
	movl	%edx,20(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	vpsrlq	$19,%xmm7,%xmm7
	andnl	28(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	24(%esp),%edx
	vpxor	%xmm7,%xmm6,%xmm6
	movl	%ebx,4(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	vpshufd	$232,%xmm6,%xmm7
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	vpslldq	$8,%xmm7,%xmm7
	movl	8(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	vpaddd	%xmm7,%xmm3,%xmm3
	addl	(%esp),%edx
	andl	%ebx,%eax
	addl	92(%esp),%edx
	vpaddd	48(%ebp),%xmm3,%xmm6
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	16(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	vmovdqa	%xmm6,80(%esp)
	cmpl	$66051,64(%ebp)
	jne	.L018avx_bmi_00_47
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,16(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	24(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	20(%esp),%edx
	movl	%eax,(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	movl	4(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	addl	28(%esp),%edx
	andl	%eax,%ebx
	addl	32(%esp),%edx
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	12(%esp),%edx
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,12(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	20(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	16(%esp),%edx
	movl	%ebx,28(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	movl	(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	andl	%ebx,%eax
	addl	36(%esp),%edx
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	8(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,8(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	16(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	12(%esp),%edx
	movl	%eax,24(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	movl	28(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	andl	%eax,%ebx
	addl	40(%esp),%edx
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	4(%esp),%edx
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,4(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	12(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	8(%esp),%edx
	movl	%ebx,20(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	movl	24(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	andl	%ebx,%eax
	addl	44(%esp),%edx
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	8(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	4(%esp),%edx
	movl	%eax,16(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	movl	20(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	addl	12(%esp),%edx
	andl	%eax,%ebx
	addl	48(%esp),%edx
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	28(%esp),%edx
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,28(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	4(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	(%esp),%edx
	movl	%ebx,12(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	movl	16(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	andl	%ebx,%eax
	addl	52(%esp),%edx
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	24(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,24(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	28(%esp),%edx
	movl	%eax,8(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	movl	12(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	andl	%eax,%ebx
	addl	56(%esp),%edx
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	20(%esp),%edx
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,20(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	28(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	24(%esp),%edx
	movl	%ebx,4(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	movl	8(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	andl	%ebx,%eax
	addl	60(%esp),%edx
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	16(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,16(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	24(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	20(%esp),%edx
	movl	%eax,(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	movl	4(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	addl	28(%esp),%edx
	andl	%eax,%ebx
	addl	64(%esp),%edx
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	12(%esp),%edx
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,12(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	20(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	16(%esp),%edx
	movl	%ebx,28(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	movl	(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	addl	24(%esp),%edx
	andl	%ebx,%eax
	addl	68(%esp),%edx
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	8(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,8(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	16(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	12(%esp),%edx
	movl	%eax,24(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	movl	28(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	addl	20(%esp),%edx
	andl	%eax,%ebx
	addl	72(%esp),%edx
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	4(%esp),%edx
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,4(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	12(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	8(%esp),%edx
	movl	%ebx,20(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	movl	24(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	addl	16(%esp),%edx
	andl	%ebx,%eax
	addl	76(%esp),%edx
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	8(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	4(%esp),%edx
	movl	%eax,16(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	movl	20(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	addl	12(%esp),%edx
	andl	%eax,%ebx
	addl	80(%esp),%edx
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	28(%esp),%edx
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,28(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	4(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	(%esp),%edx
	movl	%ebx,12(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	movl	16(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	addl	8(%esp),%edx
	andl	%ebx,%eax
	addl	84(%esp),%edx
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	24(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,24(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	28(%esp),%edx
	movl	%eax,8(%esp)
	orl	%esi,%edx
	rorxl	$2,%eax,%edi
	rorxl	$13,%eax,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%eax,%ecx
	xorl	%edi,%esi
	movl	12(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%eax
	addl	4(%esp),%edx
	andl	%eax,%ebx
	addl	88(%esp),%edx
	xorl	%edi,%ebx
	addl	%edx,%ecx
	addl	20(%esp),%edx
	leal	(%ebx,%ecx,1),%ebx
	rorxl	$6,%edx,%ecx
	rorxl	$11,%edx,%esi
	movl	%edx,20(%esp)
	rorxl	$25,%edx,%edi
	xorl	%esi,%ecx
	andnl	28(%esp),%edx,%esi
	xorl	%edi,%ecx
	andl	24(%esp),%edx
	movl	%ebx,4(%esp)
	orl	%esi,%edx
	rorxl	$2,%ebx,%edi
	rorxl	$13,%ebx,%esi
	leal	(%edx,%ecx,1),%edx
	rorxl	$22,%ebx,%ecx
	xorl	%edi,%esi
	movl	8(%esp),%edi
	xorl	%esi,%ecx
	xorl	%edi,%ebx
	addl	(%esp),%edx
	andl	%ebx,%eax
	addl	92(%esp),%edx
	xorl	%edi,%eax
	addl	%edx,%ecx
	addl	16(%esp),%edx
	leal	(%eax,%ecx,1),%eax
	movl	96(%esp),%esi
	xorl	%edi,%ebx
	movl	12(%esp),%ecx
	addl	(%esi),%eax
	addl	4(%esi),%ebx
	addl	8(%esi),%edi
	addl	12(%esi),%ecx
	movl	%eax,(%esi)
	movl	%ebx,4(%esi)
	movl	%edi,8(%esi)
	movl	%ecx,12(%esi)
	movl	%ebx,4(%esp)
	xorl	%edi,%ebx
	movl	%edi,8(%esp)
	movl	%ecx,12(%esp)
	movl	20(%esp),%edi
	movl	24(%esp),%ecx
	addl	16(%esi),%edx
	addl	20(%esi),%edi
	addl	24(%esi),%ecx
	movl	%edx,16(%esi)
	movl	%edi,20(%esi)
	movl	%edi,20(%esp)
	movl	28(%esp),%edi
	movl	%ecx,24(%esi)
	addl	28(%esi),%edi
	movl	%ecx,24(%esp)
	movl	%edi,28(%esi)
	movl	%edi,28(%esp)
	movl	100(%esp),%edi
	vmovdqa	64(%ebp),%xmm7
	subl	$192,%ebp
	cmpl	104(%esp),%edi
	jb	.L017grand_avx_bmi
	movl	108(%esp),%esp
	vzeroall
	popl	%edi
	popl	%esi
	popl	%ebx
	popl	%ebp
	ret
.size	sha256_block_data_order,.-.L_sha256_block_data_order_begin
.comm	OPENSSL_ia32cap_P,16,4

	.section ".note.gnu.property", "a"
	.p2align 2
	.long 1f - 0f
	.long 4f - 1f
	.long 5
0:
	.asciz "GNU"
1:
	.p2align 2
	.long 0xc0000002
	.long 3f - 2f
2:
	.long 3
3:
	.p2align 2
4:
