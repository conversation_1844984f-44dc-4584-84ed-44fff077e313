LIBS=../libssl

#Needed for the multiblock code in rec_layer_s3.c
IF[{- !$disabled{asm} -}]
  $AESDEF_x86=AES_ASM
  $AESDEF_x86_64=AES_ASM

  IF[$AESDEF_{- $target{asm_arch} -}]
    $AESDEF=$AESDEF_{- $target{asm_arch} -}
  ENDIF
ENDIF

$KTLSSRC=
IF[{- !$disabled{ktls} -}]
  $KTLSSRC=ktls.c
ENDIF

SOURCE[../libssl]=\
        pqueue.c \
        statem/statem_srvr.c statem/statem_clnt.c  s3_lib.c  s3_enc.c record/rec_layer_s3.c \
        statem/statem_lib.c statem/extensions.c statem/extensions_srvr.c \
        statem/extensions_clnt.c statem/extensions_cust.c s3_msg.c \
        methods.c   t1_lib.c  t1_enc.c tls13_enc.c \
        d1_lib.c  record/rec_layer_d1.c d1_msg.c \
        statem/statem_dtls.c d1_srtp.c \
        ssl_lib.c ssl_cert.c ssl_sess.c \
        ssl_ciph.c ssl_stat.c ssl_rsa.c \
        ssl_asn1.c ssl_txt.c ssl_init.c ssl_conf.c  ssl_mcnf.c \
        bio_ssl.c ssl_err.c ssl_err_legacy.c tls_srp.c t1_trce.c ssl_utst.c \
        record/ssl3_buffer.c record/ssl3_record.c record/dtls1_bitmap.c \
        statem/statem.c record/ssl3_record_tls13.c \
        tls_depr.c $KTLSSRC
# For shared builds we need to include the libcrypto packet.c and sources
# needed in providers (s3_cbc.c and record/tls_pad.c) in libssl as well.
SHARED_SOURCE[../libssl]=record/tls_pad.c ../crypto/packet.c
IF[{- !$disabled{'deprecated-3.0'} -}]
  SHARED_SOURCE[../libssl]=s3_cbc.c
  SOURCE[../libssl]=ssl_rsa_legacy.c
ENDIF
DEFINE[../libssl]=$AESDEF

IF[{- !$disabled{quic} -}]
  SOURCE[../libssl]=ssl_quic.c statem/statem_quic.c
ENDIF

SOURCE[../providers/libcommon.a]=record/tls_pad.c
SOURCE[../providers/libdefault.a ../providers/libfips.a]=s3_cbc.c
