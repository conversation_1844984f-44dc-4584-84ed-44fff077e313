.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\" SPDX-License-Identifier: MIT
.\"
.TH ARES_PARSE_NAPTR_REPLY 3 "23 February 2012"
.SH NAME
ares_parse_naptr_reply \- Parse a reply to a DNS query of type NAPTR
.SH SYNOPSIS
.nf
#include <ares.h>

int ares_parse_naptr_reply(const unsigned char* \fIabuf\fP, int \fIalen\fP,
                           struct ares_naptr_reply** \fInaptr_out\fP);
.fi
.SH DESCRIPTION
The
.B ares_parse_naptr_reply
function parses the response to a query of type NAPTR into a
linked list of
.I struct ares_naptr_reply 
The parameters
.I abuf
and
.I alen
give the contents of the response.  The result is stored in allocated
memory and a pointer to it stored into the variable pointed to by
.IR naptr_out .
It is the caller's responsibility to free the resulting
.IR naptr_out
structure when it is no longer needed using the function
\fBares_free_data(3)\fP.
.PP
The structure 
.I ares_naptr_reply
contains the following fields:
.sp
.in +4n
.nf
struct ares_naptr_reply {
    struct ares_naptr_reply *next;
    unsigned char *flags;
    unsigned char *service;
    unsigned char *regexp;
    char *replacement;
    unsigned short order;
    unsigned short preference;
};
.fi
.in
.PP
.SH RETURN VALUES
.B ares_parse_naptr_reply
can return any of the following values:
.TP 15
.B ARES_SUCCESS
The response was successfully parsed.
.TP 15
.B ARES_EBADRESP
The response was malformatted.
.TP 15
.B ARES_ENODATA
The response did not contain an answer to the query.
.TP 15
.B ARES_ENOMEM
Memory was exhausted.
.SH AVAILABILITY
This function was first introduced in c-ares version 1.7.6.
.SH SEE ALSO
.BR ares_query (3)
.BR ares_free_data (3)
