// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@cppObjectLayoutDefinition
extern class HeapNumber extends PrimitiveHeapObject {
  // TODO(v8:13070): With 8GB+ pointer compression, the number in a HeapNumber
  // is unaligned. Modify the HeapNumber layout so it remains aligned.
  value: float64;
}

// The HeapNumber value NaN
type NaN extends HeapNumber;
