/*
 * WARNING: do not edit!
 * Generated by <PERSON><PERSON><PERSON> from include/openssl/opensslv.h.in
 *
 * Copyright 1999-2020 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OPENSSL_OPENSSLV_H
# define OPENSSL_OPENSSLV_H
# pragma once

# ifdef  __cplusplus
extern "C" {
# endif

/*
 * SECTION 1: VERSION DATA.  These will change for each release
 */

/*
 * Base version macros
 *
 * These macros express version number MAJOR.MINOR.PATCH exactly
 */
# define OPENSSL_VERSION_MAJOR  3
# define OPENSSL_VERSION_MINOR  0
# define OPENSSL_VERSION_PATCH  15

/*
 * Additional version information
 *
 * These are also part of the new version scheme, but aren't part
 * of the version number itself.
 */

/* Could be: #define OPENSSL_VERSION_PRE_RELEASE "-alpha.1" */
# define OPENSSL_VERSION_PRE_RELEASE ""
/* Could be: #define OPENSSL_VERSION_BUILD_METADATA "+fips" */
/* Could be: #define OPENSSL_VERSION_BUILD_METADATA "+vendor.1" */
# define OPENSSL_VERSION_BUILD_METADATA "+quic"

/*
 * Note: The OpenSSL Project will never define OPENSSL_VERSION_BUILD_METADATA
 * to be anything but the empty string.  Its use is entirely reserved for
 * others
 */

/*
 * Shared library version
 *
 * This is strictly to express ABI version, which may or may not
 * be related to the API version expressed with the macros above.
 * This is defined in free form.
 */
# define OPENSSL_SHLIB_VERSION 81.3

/*
 * SECTION 2: USEFUL MACROS
 */

/* For checking general API compatibility when preprocessing */
# define OPENSSL_VERSION_PREREQ(maj,min)                                \
    ((OPENSSL_VERSION_MAJOR << 16) + OPENSSL_VERSION_MINOR >= ((maj) << 16) + (min))

/*
 * Macros to get the version in easily digested string form, both the short
 * "MAJOR.MINOR.PATCH" variant (where MAJOR, MINOR and PATCH are replaced
 * with the values from the corresponding OPENSSL_VERSION_ macros) and the
 * longer variant with OPENSSL_VERSION_PRE_RELEASE_STR and
 * OPENSSL_VERSION_BUILD_METADATA_STR appended.
 */
# define OPENSSL_VERSION_STR "3.0.15"
# define OPENSSL_FULL_VERSION_STR "3.0.15+quic"

/*
 * SECTION 3: ADDITIONAL METADATA
 *
 * These strings are defined separately to allow them to be parsable.
 */
# define OPENSSL_RELEASE_DATE "3 Sep 2024"

/*
 * SECTION 4: BACKWARD COMPATIBILITY
 */

# define OPENSSL_VERSION_TEXT "OpenSSL 3.0.15+quic 3 Sep 2024"

/* Synthesize OPENSSL_VERSION_NUMBER with the layout 0xMNN00PPSL */
# ifdef OPENSSL_VERSION_PRE_RELEASE
#  define _OPENSSL_VERSION_PRE_RELEASE 0x0L
# else
#  define _OPENSSL_VERSION_PRE_RELEASE 0xfL
# endif
# define OPENSSL_VERSION_NUMBER          \
    ( (OPENSSL_VERSION_MAJOR<<28)        \
      |(OPENSSL_VERSION_MINOR<<20)       \
      |(OPENSSL_VERSION_PATCH<<4)        \
      |_OPENSSL_VERSION_PRE_RELEASE )

# ifdef  __cplusplus
}
# endif

# include <openssl/macros.h>
# ifndef OPENSSL_NO_DEPRECATED_3_0
#  define HEADER_OPENSSLV_H
# endif

#endif                          /* OPENSSL_OPENSSLV_H */
