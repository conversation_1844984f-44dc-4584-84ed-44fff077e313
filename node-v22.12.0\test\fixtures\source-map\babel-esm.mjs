var _obj$a;

import { foo } from './esm-dep.mjs';
const obj = {
  a: {
    b: 22
  }
};
if ((obj === null || obj === void 0 ? void 0 : (_obj$a = obj.a) === null || _obj$a === void 0 ? void 0 : _obj$a.b) === 22) throw Error('an exception');
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImJhYmVsLWVzbS1vcmlnaW5hbC5tanMiXSwibmFtZXMiOlsiZm9vIiwib2JqIiwiYSIsImIiLCJFcnJvciJdLCJtYXBwaW5ncyI6Ijs7QUFBQSxTQUFRQSxHQUFSLFFBQWtCLGVBQWxCO0FBRUEsTUFBTUMsR0FBRyxHQUFHO0FBQ1ZDLEVBQUFBLENBQUMsRUFBRTtBQUNEQyxJQUFBQSxDQUFDLEVBQUU7QUFERjtBQURPLENBQVo7QUFNQSxJQUFJLENBQUFGLEdBQUcsU0FBSCxJQUFBQSxHQUFHLFdBQUgsc0JBQUFBLEdBQUcsQ0FBRUMsQ0FBTCxrREFBUUMsQ0FBUixNQUFjLEVBQWxCLEVBQXNCLE1BQU1DLEtBQUssQ0FBQyxjQUFELENBQVgiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Zvb30gZnJvbSAnLi9lc20tZGVwLm1qcyc7XG5cbmNvbnN0IG9iaiA9IHtcbiAgYToge1xuICAgIGI6IDIyXG4gIH1cbn07XG5cbmlmIChvYmo/LmE/LmIgPT09IDIyKSB0aHJvdyBFcnJvcignYW4gZXhjZXB0aW9uJyk7XG5cbiJdfQ==
