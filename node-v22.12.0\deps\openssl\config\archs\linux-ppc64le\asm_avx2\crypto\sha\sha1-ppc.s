.machine	"any"
.abiversion	2
.text

.globl	sha1_block_data_order
.type	sha1_block_data_order,@function
.align	4
sha1_block_data_order:
.localentry	sha1_block_data_order,0

	stdu	1,-256(1)
	mflr	0
	std	15,120(1)
	std	16,128(1)
	std	17,136(1)
	std	18,144(1)
	std	19,152(1)
	std	20,160(1)
	std	21,168(1)
	std	22,176(1)
	std	23,184(1)
	std	24,192(1)
	std	25,200(1)
	std	26,208(1)
	std	27,216(1)
	std	28,224(1)
	std	29,232(1)
	std	30,240(1)
	std	31,248(1)
	std	0,272(1)
	lwz	7,0(3)
	lwz	8,4(3)
	lwz	9,8(3)
	lwz	10,12(3)
	lwz	11,16(3)
	andi.	0,4,3
	bne	.Lunaligned
.Laligned:
	mtctr	5
	bl	.Lsha1_block_private
	b	.<PERSON><PERSON>e







.align	4
.Lunaligned:
	subfic	6,4,4096
	andi.	6,6,4095
	srwi.	6,6,6
	beq	.Lcross_page
	cmpld	5,6
	ble	.<PERSON>igned
	mtctr	6
	subfc	5,6,5
	bl	.Lsha1_block_private
.Lcross_page:
	li	6,16
	mtctr	6
	addi	20,1,48
.Lmemcpy:
	lbz	16,0(4)
	lbz	17,1(4)
	lbz	18,2(4)
	lbz	19,3(4)
	addi	4,4,4
	stb	16,0(20)
	stb	17,1(20)
	stb	18,2(20)
	stb	19,3(20)
	addi	20,20,4
	bdnz	.Lmemcpy

	std	4,112(1)
	li	6,1
	addi	4,1,48
	mtctr	6
	bl	.Lsha1_block_private
	ld	4,112(1)
	addic.	5,5,-1
	bne	.Lunaligned

.Ldone:
	ld	0,272(1)
	ld	15,120(1)
	ld	16,128(1)
	ld	17,136(1)
	ld	18,144(1)
	ld	19,152(1)
	ld	20,160(1)
	ld	21,168(1)
	ld	22,176(1)
	ld	23,184(1)
	ld	24,192(1)
	ld	25,200(1)
	ld	26,208(1)
	ld	27,216(1)
	ld	28,224(1)
	ld	29,232(1)
	ld	30,240(1)
	ld	31,248(1)
	mtlr	0
	addi	1,1,256
	blr	
.long	0
.byte	0,12,4,1,0x80,18,3,0
.long	0
.align	4
.Lsha1_block_private:
	lis	0,0x5a82
	ori	0,0,0x7999
	lwz	12,0(4)
	rotlwi	16,12,8
	rlwimi	16,12,24,0,7
	rlwimi	16,12,24,16,23
	lwz	12,4(4)
	rotlwi	17,12,8
	rlwimi	17,12,24,0,7
	rlwimi	17,12,24,16,23
	add	12,0,11
	rotlwi	11,7,5
	add	12,12,16
	and	15,9,8
	add	12,12,11
	andc	6,10,8
	rotlwi	8,8,30
	or	15,15,6
	add	12,12,15
	lwz	11,8(4)
	rotlwi	18,11,8
	rlwimi	18,11,24,0,7
	rlwimi	18,11,24,16,23
	add	11,0,10
	rotlwi	10,12,5
	add	11,11,17
	and	15,8,7
	add	11,11,10
	andc	6,9,7
	rotlwi	7,7,30
	or	15,15,6
	add	11,11,15
	lwz	10,12(4)
	rotlwi	19,10,8
	rlwimi	19,10,24,0,7
	rlwimi	19,10,24,16,23
	add	10,0,9
	rotlwi	9,11,5
	add	10,10,18
	and	15,7,12
	add	10,10,9
	andc	6,8,12
	rotlwi	12,12,30
	or	15,15,6
	add	10,10,15
	lwz	9,16(4)
	rotlwi	20,9,8
	rlwimi	20,9,24,0,7
	rlwimi	20,9,24,16,23
	add	9,0,8
	rotlwi	8,10,5
	add	9,9,19
	and	15,12,11
	add	9,9,8
	andc	6,7,11
	rotlwi	11,11,30
	or	15,15,6
	add	9,9,15
	lwz	8,20(4)
	rotlwi	21,8,8
	rlwimi	21,8,24,0,7
	rlwimi	21,8,24,16,23
	add	8,0,7
	rotlwi	7,9,5
	add	8,8,20
	and	15,11,10
	add	8,8,7
	andc	6,12,10
	rotlwi	10,10,30
	or	15,15,6
	add	8,8,15
	lwz	7,24(4)
	rotlwi	22,7,8
	rlwimi	22,7,24,0,7
	rlwimi	22,7,24,16,23
	add	7,0,12
	rotlwi	12,8,5
	add	7,7,21
	and	15,10,9
	add	7,7,12
	andc	6,11,9
	rotlwi	9,9,30
	or	15,15,6
	add	7,7,15
	lwz	12,28(4)
	rotlwi	23,12,8
	rlwimi	23,12,24,0,7
	rlwimi	23,12,24,16,23
	add	12,0,11
	rotlwi	11,7,5
	add	12,12,22
	and	15,9,8
	add	12,12,11
	andc	6,10,8
	rotlwi	8,8,30
	or	15,15,6
	add	12,12,15
	lwz	11,32(4)
	rotlwi	24,11,8
	rlwimi	24,11,24,0,7
	rlwimi	24,11,24,16,23
	add	11,0,10
	rotlwi	10,12,5
	add	11,11,23
	and	15,8,7
	add	11,11,10
	andc	6,9,7
	rotlwi	7,7,30
	or	15,15,6
	add	11,11,15
	lwz	10,36(4)
	rotlwi	25,10,8
	rlwimi	25,10,24,0,7
	rlwimi	25,10,24,16,23
	add	10,0,9
	rotlwi	9,11,5
	add	10,10,24
	and	15,7,12
	add	10,10,9
	andc	6,8,12
	rotlwi	12,12,30
	or	15,15,6
	add	10,10,15
	lwz	9,40(4)
	rotlwi	26,9,8
	rlwimi	26,9,24,0,7
	rlwimi	26,9,24,16,23
	add	9,0,8
	rotlwi	8,10,5
	add	9,9,25
	and	15,12,11
	add	9,9,8
	andc	6,7,11
	rotlwi	11,11,30
	or	15,15,6
	add	9,9,15
	lwz	8,44(4)
	rotlwi	27,8,8
	rlwimi	27,8,24,0,7
	rlwimi	27,8,24,16,23
	add	8,0,7
	rotlwi	7,9,5
	add	8,8,26
	and	15,11,10
	add	8,8,7
	andc	6,12,10
	rotlwi	10,10,30
	or	15,15,6
	add	8,8,15
	lwz	7,48(4)
	rotlwi	28,7,8
	rlwimi	28,7,24,0,7
	rlwimi	28,7,24,16,23
	add	7,0,12
	rotlwi	12,8,5
	add	7,7,27
	and	15,10,9
	add	7,7,12
	andc	6,11,9
	rotlwi	9,9,30
	or	15,15,6
	add	7,7,15
	lwz	12,52(4)
	rotlwi	29,12,8
	rlwimi	29,12,24,0,7
	rlwimi	29,12,24,16,23
	add	12,0,11
	rotlwi	11,7,5
	add	12,12,28
	and	15,9,8
	add	12,12,11
	andc	6,10,8
	rotlwi	8,8,30
	or	15,15,6
	add	12,12,15
	lwz	11,56(4)
	rotlwi	30,11,8
	rlwimi	30,11,24,0,7
	rlwimi	30,11,24,16,23
	add	11,0,10
	rotlwi	10,12,5
	add	11,11,29
	and	15,8,7
	add	11,11,10
	andc	6,9,7
	rotlwi	7,7,30
	or	15,15,6
	add	11,11,15
	lwz	10,60(4)
	rotlwi	31,10,8
	rlwimi	31,10,24,0,7
	rlwimi	31,10,24,16,23
	add	10,0,9
	rotlwi	9,11,5
	add	10,10,30
	and	15,7,12
	add	10,10,9
	andc	6,8,12
	rotlwi	12,12,30
	or	15,15,6
	add	10,10,15
	add	9,0,8
	rotlwi	8,10,5
	xor	16,16,18
	add	9,9,31
	and	15,12,11
	xor	16,16,24
	add	9,9,8
	andc	6,7,11
	rotlwi	11,11,30
	or	15,15,6
	xor	16,16,29
	add	9,9,15
	rotlwi	16,16,1
	add	8,0,7
	rotlwi	7,9,5
	xor	17,17,19
	add	8,8,16
	and	15,11,10
	xor	17,17,25
	add	8,8,7
	andc	6,12,10
	rotlwi	10,10,30
	or	15,15,6
	xor	17,17,30
	add	8,8,15
	rotlwi	17,17,1
	add	7,0,12
	rotlwi	12,8,5
	xor	18,18,20
	add	7,7,17
	and	15,10,9
	xor	18,18,26
	add	7,7,12
	andc	6,11,9
	rotlwi	9,9,30
	or	15,15,6
	xor	18,18,31
	add	7,7,15
	rotlwi	18,18,1
	add	12,0,11
	rotlwi	11,7,5
	xor	19,19,21
	add	12,12,18
	and	15,9,8
	xor	19,19,27
	add	12,12,11
	andc	6,10,8
	rotlwi	8,8,30
	or	15,15,6
	xor	19,19,16
	add	12,12,15
	rotlwi	19,19,1
	add	11,0,10
	rotlwi	10,12,5
	xor	20,20,22
	add	11,11,19
	and	15,8,7
	xor	20,20,28
	add	11,11,10
	andc	6,9,7
	rotlwi	7,7,30
	or	15,15,6
	xor	20,20,17
	add	11,11,15
	rotlwi	20,20,1
	lis	0,0x6ed9
	ori	0,0,0xeba1
	add	10,0,9
	xor	15,12,8
	rotlwi	9,11,5
	xor	21,21,23
	add	10,10,20
	xor	15,15,7
	xor	21,21,29
	add	10,10,15
	rotlwi	12,12,30
	xor	21,21,18
	add	10,10,9
	rotlwi	21,21,1
	add	9,0,8
	xor	15,11,7
	rotlwi	8,10,5
	xor	22,22,24
	add	9,9,21
	xor	15,15,12
	xor	22,22,30
	add	9,9,15
	rotlwi	11,11,30
	xor	22,22,19
	add	9,9,8
	rotlwi	22,22,1
	add	8,0,7
	xor	15,10,12
	rotlwi	7,9,5
	xor	23,23,25
	add	8,8,22
	xor	15,15,11
	xor	23,23,31
	add	8,8,15
	rotlwi	10,10,30
	xor	23,23,20
	add	8,8,7
	rotlwi	23,23,1
	add	7,0,12
	xor	15,9,11
	rotlwi	12,8,5
	xor	24,24,26
	add	7,7,23
	xor	15,15,10
	xor	24,24,16
	add	7,7,15
	rotlwi	9,9,30
	xor	24,24,21
	add	7,7,12
	rotlwi	24,24,1
	add	12,0,11
	xor	15,8,10
	rotlwi	11,7,5
	xor	25,25,27
	add	12,12,24
	xor	15,15,9
	xor	25,25,17
	add	12,12,15
	rotlwi	8,8,30
	xor	25,25,22
	add	12,12,11
	rotlwi	25,25,1
	add	11,0,10
	xor	15,7,9
	rotlwi	10,12,5
	xor	26,26,28
	add	11,11,25
	xor	15,15,8
	xor	26,26,18
	add	11,11,15
	rotlwi	7,7,30
	xor	26,26,23
	add	11,11,10
	rotlwi	26,26,1
	add	10,0,9
	xor	15,12,8
	rotlwi	9,11,5
	xor	27,27,29
	add	10,10,26
	xor	15,15,7
	xor	27,27,19
	add	10,10,15
	rotlwi	12,12,30
	xor	27,27,24
	add	10,10,9
	rotlwi	27,27,1
	add	9,0,8
	xor	15,11,7
	rotlwi	8,10,5
	xor	28,28,30
	add	9,9,27
	xor	15,15,12
	xor	28,28,20
	add	9,9,15
	rotlwi	11,11,30
	xor	28,28,25
	add	9,9,8
	rotlwi	28,28,1
	add	8,0,7
	xor	15,10,12
	rotlwi	7,9,5
	xor	29,29,31
	add	8,8,28
	xor	15,15,11
	xor	29,29,21
	add	8,8,15
	rotlwi	10,10,30
	xor	29,29,26
	add	8,8,7
	rotlwi	29,29,1
	add	7,0,12
	xor	15,9,11
	rotlwi	12,8,5
	xor	30,30,16
	add	7,7,29
	xor	15,15,10
	xor	30,30,22
	add	7,7,15
	rotlwi	9,9,30
	xor	30,30,27
	add	7,7,12
	rotlwi	30,30,1
	add	12,0,11
	xor	15,8,10
	rotlwi	11,7,5
	xor	31,31,17
	add	12,12,30
	xor	15,15,9
	xor	31,31,23
	add	12,12,15
	rotlwi	8,8,30
	xor	31,31,28
	add	12,12,11
	rotlwi	31,31,1
	add	11,0,10
	xor	15,7,9
	rotlwi	10,12,5
	xor	16,16,18
	add	11,11,31
	xor	15,15,8
	xor	16,16,24
	add	11,11,15
	rotlwi	7,7,30
	xor	16,16,29
	add	11,11,10
	rotlwi	16,16,1
	add	10,0,9
	xor	15,12,8
	rotlwi	9,11,5
	xor	17,17,19
	add	10,10,16
	xor	15,15,7
	xor	17,17,25
	add	10,10,15
	rotlwi	12,12,30
	xor	17,17,30
	add	10,10,9
	rotlwi	17,17,1
	add	9,0,8
	xor	15,11,7
	rotlwi	8,10,5
	xor	18,18,20
	add	9,9,17
	xor	15,15,12
	xor	18,18,26
	add	9,9,15
	rotlwi	11,11,30
	xor	18,18,31
	add	9,9,8
	rotlwi	18,18,1
	add	8,0,7
	xor	15,10,12
	rotlwi	7,9,5
	xor	19,19,21
	add	8,8,18
	xor	15,15,11
	xor	19,19,27
	add	8,8,15
	rotlwi	10,10,30
	xor	19,19,16
	add	8,8,7
	rotlwi	19,19,1
	add	7,0,12
	xor	15,9,11
	rotlwi	12,8,5
	xor	20,20,22
	add	7,7,19
	xor	15,15,10
	xor	20,20,28
	add	7,7,15
	rotlwi	9,9,30
	xor	20,20,17
	add	7,7,12
	rotlwi	20,20,1
	add	12,0,11
	xor	15,8,10
	rotlwi	11,7,5
	xor	21,21,23
	add	12,12,20
	xor	15,15,9
	xor	21,21,29
	add	12,12,15
	rotlwi	8,8,30
	xor	21,21,18
	add	12,12,11
	rotlwi	21,21,1
	add	11,0,10
	xor	15,7,9
	rotlwi	10,12,5
	xor	22,22,24
	add	11,11,21
	xor	15,15,8
	xor	22,22,30
	add	11,11,15
	rotlwi	7,7,30
	xor	22,22,19
	add	11,11,10
	rotlwi	22,22,1
	add	10,0,9
	xor	15,12,8
	rotlwi	9,11,5
	xor	23,23,25
	add	10,10,22
	xor	15,15,7
	xor	23,23,31
	add	10,10,15
	rotlwi	12,12,30
	xor	23,23,20
	add	10,10,9
	rotlwi	23,23,1
	add	9,0,8
	xor	15,11,7
	rotlwi	8,10,5
	xor	24,24,26
	add	9,9,23
	xor	15,15,12
	xor	24,24,16
	add	9,9,15
	rotlwi	11,11,30
	xor	24,24,21
	add	9,9,8
	rotlwi	24,24,1
	lis	0,0x8f1b
	ori	0,0,0xbcdc
	add	8,0,7
	rotlwi	7,9,5
	xor	25,25,27
	add	8,8,24
	and	15,10,11
	xor	25,25,17
	add	8,8,7
	or	6,10,11
	rotlwi	10,10,30
	xor	25,25,22
	and	6,6,12
	or	15,15,6
	rotlwi	25,25,1
	add	8,8,15
	add	7,0,12
	rotlwi	12,8,5
	xor	26,26,28
	add	7,7,25
	and	15,9,10
	xor	26,26,18
	add	7,7,12
	or	6,9,10
	rotlwi	9,9,30
	xor	26,26,23
	and	6,6,11
	or	15,15,6
	rotlwi	26,26,1
	add	7,7,15
	add	12,0,11
	rotlwi	11,7,5
	xor	27,27,29
	add	12,12,26
	and	15,8,9
	xor	27,27,19
	add	12,12,11
	or	6,8,9
	rotlwi	8,8,30
	xor	27,27,24
	and	6,6,10
	or	15,15,6
	rotlwi	27,27,1
	add	12,12,15
	add	11,0,10
	rotlwi	10,12,5
	xor	28,28,30
	add	11,11,27
	and	15,7,8
	xor	28,28,20
	add	11,11,10
	or	6,7,8
	rotlwi	7,7,30
	xor	28,28,25
	and	6,6,9
	or	15,15,6
	rotlwi	28,28,1
	add	11,11,15
	add	10,0,9
	rotlwi	9,11,5
	xor	29,29,31
	add	10,10,28
	and	15,12,7
	xor	29,29,21
	add	10,10,9
	or	6,12,7
	rotlwi	12,12,30
	xor	29,29,26
	and	6,6,8
	or	15,15,6
	rotlwi	29,29,1
	add	10,10,15
	add	9,0,8
	rotlwi	8,10,5
	xor	30,30,16
	add	9,9,29
	and	15,11,12
	xor	30,30,22
	add	9,9,8
	or	6,11,12
	rotlwi	11,11,30
	xor	30,30,27
	and	6,6,7
	or	15,15,6
	rotlwi	30,30,1
	add	9,9,15
	add	8,0,7
	rotlwi	7,9,5
	xor	31,31,17
	add	8,8,30
	and	15,10,11
	xor	31,31,23
	add	8,8,7
	or	6,10,11
	rotlwi	10,10,30
	xor	31,31,28
	and	6,6,12
	or	15,15,6
	rotlwi	31,31,1
	add	8,8,15
	add	7,0,12
	rotlwi	12,8,5
	xor	16,16,18
	add	7,7,31
	and	15,9,10
	xor	16,16,24
	add	7,7,12
	or	6,9,10
	rotlwi	9,9,30
	xor	16,16,29
	and	6,6,11
	or	15,15,6
	rotlwi	16,16,1
	add	7,7,15
	add	12,0,11
	rotlwi	11,7,5
	xor	17,17,19
	add	12,12,16
	and	15,8,9
	xor	17,17,25
	add	12,12,11
	or	6,8,9
	rotlwi	8,8,30
	xor	17,17,30
	and	6,6,10
	or	15,15,6
	rotlwi	17,17,1
	add	12,12,15
	add	11,0,10
	rotlwi	10,12,5
	xor	18,18,20
	add	11,11,17
	and	15,7,8
	xor	18,18,26
	add	11,11,10
	or	6,7,8
	rotlwi	7,7,30
	xor	18,18,31
	and	6,6,9
	or	15,15,6
	rotlwi	18,18,1
	add	11,11,15
	add	10,0,9
	rotlwi	9,11,5
	xor	19,19,21
	add	10,10,18
	and	15,12,7
	xor	19,19,27
	add	10,10,9
	or	6,12,7
	rotlwi	12,12,30
	xor	19,19,16
	and	6,6,8
	or	15,15,6
	rotlwi	19,19,1
	add	10,10,15
	add	9,0,8
	rotlwi	8,10,5
	xor	20,20,22
	add	9,9,19
	and	15,11,12
	xor	20,20,28
	add	9,9,8
	or	6,11,12
	rotlwi	11,11,30
	xor	20,20,17
	and	6,6,7
	or	15,15,6
	rotlwi	20,20,1
	add	9,9,15
	add	8,0,7
	rotlwi	7,9,5
	xor	21,21,23
	add	8,8,20
	and	15,10,11
	xor	21,21,29
	add	8,8,7
	or	6,10,11
	rotlwi	10,10,30
	xor	21,21,18
	and	6,6,12
	or	15,15,6
	rotlwi	21,21,1
	add	8,8,15
	add	7,0,12
	rotlwi	12,8,5
	xor	22,22,24
	add	7,7,21
	and	15,9,10
	xor	22,22,30
	add	7,7,12
	or	6,9,10
	rotlwi	9,9,30
	xor	22,22,19
	and	6,6,11
	or	15,15,6
	rotlwi	22,22,1
	add	7,7,15
	add	12,0,11
	rotlwi	11,7,5
	xor	23,23,25
	add	12,12,22
	and	15,8,9
	xor	23,23,31
	add	12,12,11
	or	6,8,9
	rotlwi	8,8,30
	xor	23,23,20
	and	6,6,10
	or	15,15,6
	rotlwi	23,23,1
	add	12,12,15
	add	11,0,10
	rotlwi	10,12,5
	xor	24,24,26
	add	11,11,23
	and	15,7,8
	xor	24,24,16
	add	11,11,10
	or	6,7,8
	rotlwi	7,7,30
	xor	24,24,21
	and	6,6,9
	or	15,15,6
	rotlwi	24,24,1
	add	11,11,15
	add	10,0,9
	rotlwi	9,11,5
	xor	25,25,27
	add	10,10,24
	and	15,12,7
	xor	25,25,17
	add	10,10,9
	or	6,12,7
	rotlwi	12,12,30
	xor	25,25,22
	and	6,6,8
	or	15,15,6
	rotlwi	25,25,1
	add	10,10,15
	add	9,0,8
	rotlwi	8,10,5
	xor	26,26,28
	add	9,9,25
	and	15,11,12
	xor	26,26,18
	add	9,9,8
	or	6,11,12
	rotlwi	11,11,30
	xor	26,26,23
	and	6,6,7
	or	15,15,6
	rotlwi	26,26,1
	add	9,9,15
	add	8,0,7
	rotlwi	7,9,5
	xor	27,27,29
	add	8,8,26
	and	15,10,11
	xor	27,27,19
	add	8,8,7
	or	6,10,11
	rotlwi	10,10,30
	xor	27,27,24
	and	6,6,12
	or	15,15,6
	rotlwi	27,27,1
	add	8,8,15
	add	7,0,12
	rotlwi	12,8,5
	xor	28,28,30
	add	7,7,27
	and	15,9,10
	xor	28,28,20
	add	7,7,12
	or	6,9,10
	rotlwi	9,9,30
	xor	28,28,25
	and	6,6,11
	or	15,15,6
	rotlwi	28,28,1
	add	7,7,15
	lis	0,0xca62
	ori	0,0,0xc1d6
	add	12,0,11
	xor	15,8,10
	rotlwi	11,7,5
	xor	29,29,31
	add	12,12,28
	xor	15,15,9
	xor	29,29,21
	add	12,12,15
	rotlwi	8,8,30
	xor	29,29,26
	add	12,12,11
	rotlwi	29,29,1
	add	11,0,10
	xor	15,7,9
	rotlwi	10,12,5
	xor	30,30,16
	add	11,11,29
	xor	15,15,8
	xor	30,30,22
	add	11,11,15
	rotlwi	7,7,30
	xor	30,30,27
	add	11,11,10
	rotlwi	30,30,1
	add	10,0,9
	xor	15,12,8
	rotlwi	9,11,5
	xor	31,31,17
	add	10,10,30
	xor	15,15,7
	xor	31,31,23
	add	10,10,15
	rotlwi	12,12,30
	xor	31,31,28
	add	10,10,9
	rotlwi	31,31,1
	add	9,0,8
	xor	15,11,7
	rotlwi	8,10,5
	xor	16,16,18
	add	9,9,31
	xor	15,15,12
	xor	16,16,24
	add	9,9,15
	rotlwi	11,11,30
	xor	16,16,29
	add	9,9,8
	rotlwi	16,16,1
	add	8,0,7
	xor	15,10,12
	rotlwi	7,9,5
	xor	17,17,19
	add	8,8,16
	xor	15,15,11
	xor	17,17,25
	add	8,8,15
	rotlwi	10,10,30
	xor	17,17,30
	add	8,8,7
	rotlwi	17,17,1
	add	7,0,12
	xor	15,9,11
	rotlwi	12,8,5
	xor	18,18,20
	add	7,7,17
	xor	15,15,10
	xor	18,18,26
	add	7,7,15
	rotlwi	9,9,30
	xor	18,18,31
	add	7,7,12
	rotlwi	18,18,1
	add	12,0,11
	xor	15,8,10
	rotlwi	11,7,5
	xor	19,19,21
	add	12,12,18
	xor	15,15,9
	xor	19,19,27
	add	12,12,15
	rotlwi	8,8,30
	xor	19,19,16
	add	12,12,11
	rotlwi	19,19,1
	add	11,0,10
	xor	15,7,9
	rotlwi	10,12,5
	xor	20,20,22
	add	11,11,19
	xor	15,15,8
	xor	20,20,28
	add	11,11,15
	rotlwi	7,7,30
	xor	20,20,17
	add	11,11,10
	rotlwi	20,20,1
	add	10,0,9
	xor	15,12,8
	rotlwi	9,11,5
	xor	21,21,23
	add	10,10,20
	xor	15,15,7
	xor	21,21,29
	add	10,10,15
	rotlwi	12,12,30
	xor	21,21,18
	add	10,10,9
	rotlwi	21,21,1
	add	9,0,8
	xor	15,11,7
	rotlwi	8,10,5
	xor	22,22,24
	add	9,9,21
	xor	15,15,12
	xor	22,22,30
	add	9,9,15
	rotlwi	11,11,30
	xor	22,22,19
	add	9,9,8
	rotlwi	22,22,1
	add	8,0,7
	xor	15,10,12
	rotlwi	7,9,5
	xor	23,23,25
	add	8,8,22
	xor	15,15,11
	xor	23,23,31
	add	8,8,15
	rotlwi	10,10,30
	xor	23,23,20
	add	8,8,7
	rotlwi	23,23,1
	add	7,0,12
	xor	15,9,11
	rotlwi	12,8,5
	xor	24,24,26
	add	7,7,23
	xor	15,15,10
	xor	24,24,16
	add	7,7,15
	rotlwi	9,9,30
	xor	24,24,21
	add	7,7,12
	rotlwi	24,24,1
	add	12,0,11
	xor	15,8,10
	rotlwi	11,7,5
	xor	25,25,27
	add	12,12,24
	xor	15,15,9
	xor	25,25,17
	add	12,12,15
	rotlwi	8,8,30
	xor	25,25,22
	add	12,12,11
	rotlwi	25,25,1
	add	11,0,10
	xor	15,7,9
	rotlwi	10,12,5
	xor	26,26,28
	add	11,11,25
	xor	15,15,8
	xor	26,26,18
	add	11,11,15
	rotlwi	7,7,30
	xor	26,26,23
	add	11,11,10
	rotlwi	26,26,1
	add	10,0,9
	xor	15,12,8
	rotlwi	9,11,5
	xor	27,27,29
	add	10,10,26
	xor	15,15,7
	xor	27,27,19
	add	10,10,15
	rotlwi	12,12,30
	xor	27,27,24
	add	10,10,9
	rotlwi	27,27,1
	add	9,0,8
	xor	15,11,7
	rotlwi	8,10,5
	xor	28,28,30
	add	9,9,27
	xor	15,15,12
	xor	28,28,20
	add	9,9,15
	rotlwi	11,11,30
	xor	28,28,25
	add	9,9,8
	rotlwi	28,28,1
	add	8,0,7
	xor	15,10,12
	rotlwi	7,9,5
	xor	29,29,31
	add	8,8,28
	xor	15,15,11
	xor	29,29,21
	add	8,8,15
	rotlwi	10,10,30
	xor	29,29,26
	add	8,8,7
	rotlwi	29,29,1
	add	7,0,12
	xor	15,9,11
	rotlwi	12,8,5
	xor	30,30,16
	add	7,7,29
	xor	15,15,10
	xor	30,30,22
	add	7,7,15
	rotlwi	9,9,30
	xor	30,30,27
	add	7,7,12
	rotlwi	30,30,1
	add	12,0,11
	xor	15,8,10
	rotlwi	11,7,5
	xor	31,31,17
	add	12,12,30
	xor	15,15,9
	xor	31,31,23
	add	12,12,15
	rotlwi	8,8,30
	xor	31,31,28
	add	12,12,11
	rotlwi	31,31,1
	add	11,0,10
	xor	15,7,9
	rotlwi	10,12,5
	lwz	16,0(3)
	add	11,11,31
	xor	15,15,8
	lwz	17,4(3)
	add	11,11,15
	rotlwi	7,7,30
	lwz	18,8(3)
	lwz	19,12(3)
	add	11,11,10
	lwz	20,16(3)
	add	16,16,11
	add	17,17,12
	add	18,18,7
	add	19,19,8
	add	20,20,9
	stw	16,0(3)
	mr	7,16
	stw	17,4(3)
	mr	8,17
	stw	18,8(3)
	mr	9,18
	stw	19,12(3)
	mr	10,19
	stw	20,16(3)
	mr	11,20
	addi	4,4,64
	bdnz	.Lsha1_block_private
	blr	
.long	0
.byte	0,12,0x14,0,0,0,0,0
.size	sha1_block_data_order,.-sha1_block_data_order
.byte	83,72,65,49,32,98,108,111,99,107,32,116,114,97,110,115,102,111,114,109,32,102,111,114,32,80,80,67,44,67,82,89,80,84,79,71,65,77,83,32,98,121,32,60,97,112,112,114,111,64,102,121,46,99,104,97,108,109,101,114,115,46,115,101,62,0
.align	2
