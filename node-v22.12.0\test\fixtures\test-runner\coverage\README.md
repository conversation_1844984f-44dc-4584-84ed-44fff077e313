The files in the directory are generated by the
following commands:

```sh
npx esbuild a.test.ts --sourcemap --outdir=. --out-extension:.js=.mjs --sources-content=false --minify --bundle --platform=node --format=esm
echo "import { test } from 'node:test';
test('ok', () => {});

function uncovered() {
  return 'uncovered';
}
" | npx esbuild --sourcemap --sourcefile=stdin.test.ts --sources-content=true --bundle --platform=node --outfile="stdin.test.js"
```