<!DOCTYPE html>
<html>
<head>
<title>Console Number Format Specifiers on Symbols</title>
<meta name="author" title="<PERSON>" href="mailto:<EMAIL>">
<meta name="assert" content="Console format specifiers on Symbols">
<link rel="help" href="https://console.spec.whatwg.org/#formatter">
</head>
<body>
<p>Open the console inside the developer tools. It should contain 15 entries, each of which are:</p>
<p><code>NaN</code></p>

<script>
const methods = ["log", "dirxml", "trace", "group", "groupCollapsed"];

for (method of methods) {
  console[method]("%i", Symbol.for("description"));
  if (method == "group" || method == "groupCollapsed") console.groupEnd();
  console[method]("%d", Symbol.for("description"));
  if (method == "group" || method == "groupCollapsed") console.groupEnd();
  console[method]("%f", Symbol.for("description"));
  if (method == "group" || method == "groupCollapsed") console.groupEnd();
}
</script>
</body>
</html>
