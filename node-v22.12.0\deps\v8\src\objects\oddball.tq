// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@cppObjectLayoutDefinition
@apiExposedInstanceTypeValue(0x83)
@highestInstanceTypeWithinParentClassRange
extern class Odd<PERSON> extends PrimitiveHeapObject {
  to_number_raw: float64;
  to_string: String;
  to_number: Number;
  type_of: String;
  kind: Smi;
}

@cppObjectLayoutDefinition
@hasSameInstanceTypeAsParent
@doNotGenerateCast
extern class Null extends Oddball {}

@cppObjectLayoutDefinition
@hasSameInstanceTypeAsParent
@doNotGenerateCast
extern class Undefined extends Oddball {}

@cppObjectLayoutDefinition
@hasSameInstanceTypeAsParent
@doNotGenerateCast
extern class Boolean extends Oddball {}

@cppObjectLayoutDefinition
@hasSameInstanceTypeAsParent
@doNotGenerateCast
extern class True extends Boolean {}

@cppObjectLayoutDefinition
@hasSameInstanceTypeAsParent
@doNotGenerateCast
extern class False extends Boolean {}
