<!DOCTYPE html>
<html>
<head>
<meta charset="iso-2022-jp"> <!-- test breaks if the server overrides this -->
<title>ISO 2022-JP encoding (href)</title>
<meta name="timeout" content="long">
<meta name="variant" content="?1-1000">
<meta name="variant" content="?1001-2000">
<meta name="variant" content="?2001-3000">
<meta name="variant" content="?3001-4000">
<meta name="variant" content="?4001-5000">
<meta name="variant" content="?5001-6000">
<meta name="variant" content="?6001-7000">
<meta name="variant" content="?7001-last">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/common/subset-tests.js"></script>
<script src="jis0208_index.js"></script>
<script src="iso2022jp-encoder.js"></script>
<link rel="author" title="<PERSON>" href="mailto:<EMAIL>">
<link rel="help" href="https://encoding.spec.whatwg.org/#iso-2022-jp">
<meta name="assert" content="The browser produces the expected byte sequences for all characters in the iso-2022-jp encoding after 0x9F when writing characters to an href value, using the encoder steps in the specification.">
<script src="../../resources/ranges.js"></script>
<script>
var errors = false;
var encoder = iso2022jpEncoder;
var ranges = rangesAll;
function expect(result, codepoint) {
  return "%" + result.replace(/ /g, "%").replace(/%1B%28%42$/, "");;
}
</script>
<script src="../../resources/encode-href-common.js"></script>
</head>
<body>
<div id="log"></div>
</body>
</html>
