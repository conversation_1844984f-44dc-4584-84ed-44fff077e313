TAP version 13
# Subtest: Coverage Print Fixed Width 150
ok 1 - Coverage Print Fixed Width 150
  ---
  duration_ms: *
  ...
1..1
# tests 1
# suites 0
# pass 1
# fail 0
# cancelled 0
# skipped 0
# todo 0
# duration_ms *
# start of coverage report
# ----------------------------------------------------------------------------------------------------------------------------------------------------
# file                                       | line % | branch % | funcs % | uncovered lines
# ----------------------------------------------------------------------------------------------------------------------------------------------------
# test                                       |        |          |         | 
#  fixtures                                  |        |          |         | 
#   test-runner                              |        |          |         | 
#    coverage-snap                           |        |          |         | 
#     a.js                                   |  55.77 |   100.00 |    0.00 | 5-7 9-11 13-15 17-19 29-30 40-42 45-47 50-52
#     b.js                                   |  45.45 |   100.00 |    0.00 | 5-7 9-11
#     many-uncovered-lines.js                |  50.99 |    42.86 |    1.92 | 5-7 9-11 13-15 17-19 29-30 40-42 45-47 50-52 55-57 59-61 63-65 67-69 91…
#    output                                  |        |          |         | 
#     coverage-width-150-uncovered-lines.mjs | 100.00 |   100.00 |  100.00 | 
# ----------------------------------------------------------------------------------------------------------------------------------------------------
# all files                                  |  52.80 |    60.00 |    1.61 | 
# ----------------------------------------------------------------------------------------------------------------------------------------------------
# end of coverage report
