const express = require('express');
const cors = require('cors');
const path = require('path');
const axios = require('axios');

const app = express();
const port = process.env.PORT || 3000;

// 启用 CORS
app.use(cors());

// 设置静态文件目录
app.use(express.static('public'));

// 添加API代理路由 - 使用新浪财经API
app.get('/api/stocks', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = Math.min(parseInt(req.query.pageSize) || 100, 200); // 限制最大200条

        console.log(`代理请求: 第${page}页, 每页${pageSize}条`);

        // 使用新浪财经API获取A股数据，支持多页请求
        let allStocks = [];

        // 如果请求的是第一页，我们获取多页数据来增加股票数量
        if (page === 1) {
            const maxPages = 50; // 大幅增加页数以获取更多股票
            const promises = [];

            // 分批请求，避免同时发起太多请求
            const batchSize = 10;
            for (let batch = 0; batch < Math.ceil(maxPages / batchSize); batch++) {
                const batchPromises = [];
                const startPage = batch * batchSize + 1;
                const endPage = Math.min(startPage + batchSize - 1, maxPages);

                for (let p = startPage; p <= endPage; p++) {
                    batchPromises.push(
                        axios.get('http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData', {
                            params: {
                                page: p,
                                num: 200, // 每页200条
                                sort: 'symbol', // 按股票代码排序，确保获取不同的股票
                                asc: 1,
                                node: 'hs_a', // 沪深A股
                                symbol: '',
                                _s_r_a: 'init'
                            },
                            timeout: 15000
                        }).catch(error => {
                            console.log(`第${p}页请求失败: ${error.message}`);
                            return null;
                        })
                    );
                }

                // 等待当前批次完成
                const batchResults = await Promise.allSettled(batchPromises);
                promises.push(...batchResults);

                // 批次间稍作延迟，避免请求过于频繁
                if (batch < Math.ceil(maxPages / batchSize) - 1) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            // 处理所有批次的结果
            let pageCount = 0;
            promises.forEach((result) => {
                pageCount++;
                if (result.status === 'fulfilled' && result.value && result.value.data && Array.isArray(result.value.data)) {
                    console.log(`第${pageCount}页获取到${result.value.data.length}条数据`);
                    allStocks = allStocks.concat(result.value.data);
                } else if (result.status === 'fulfilled' && result.value === null) {
                    console.log(`第${pageCount}页请求被跳过（错误处理）`);
                } else {
                    console.log(`第${pageCount}页请求失败`);
                }
            });

            // 去重处理（根据股票代码）
            const uniqueStocks = [];
            const seenCodes = new Set();
            allStocks.forEach(stock => {
                if (!seenCodes.has(stock.symbol)) {
                    seenCodes.add(stock.symbol);
                    uniqueStocks.push(stock);
                }
            });

            allStocks = uniqueStocks;

        } else {
            // 非第一页的请求，直接获取单页数据
            const response = await axios.get('http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData', {
                params: {
                    page: page,
                    num: pageSize,
                    sort: 'changepercent',
                    asc: 0,
                    node: 'hs_a', // 沪深A股
                    symbol: '',
                    _s_r_a: 'init'
                },
                timeout: 10000
            });

            if (!response.data || !Array.isArray(response.data)) {
                throw new Error('新浪API返回数据格式不正确');
            }

            allStocks = response.data;
        }

        if (!allStocks || !Array.isArray(allStocks) || allStocks.length === 0) {
            throw new Error('新浪API返回数据格式不正确或无数据');
        }

        // 转换新浪API数据格式为东方财富格式，保持前端兼容性
        const convertedData = {
            data: {
                diff: allStocks.map(item => ({
                    f12: item.symbol, // 股票代码
                    f14: item.name, // 股票名称
                    f2: parseFloat(item.trade) || 0, // 最新价
                    f3: parseFloat(item.changepercent) || 0, // 涨跌幅
                    f17: parseFloat(item.open) || 0, // 今开
                    f18: parseFloat(item.settlement) || 0, // 昨收
                    f5: parseInt(item.volume) || 0, // 成交量
                    f6: parseFloat(item.amount) || 0, // 成交额
                    f10: parseFloat(item.per) || 0, // 市盈率
                    f8: parseFloat(item.turnoverratio) || 0, // 换手率
                    f1: 1,
                    f4: parseFloat(item.change) || 0, // 涨跌额
                    f7: parseFloat(item.change) || 0,
                    f9: parseFloat(item.high) || 0, // 最高
                    f11: parseFloat(item.low) || 0, // 最低
                    f15: parseFloat(item.amount) || 0,
                    f16: parseFloat(item.per) || 0,
                    f20: parseFloat(item.amount) || 0,
                    f21: parseFloat(item.amount) || 0,
                    f22: parseFloat(item.changepercent) || 0,
                    f23: parseFloat(item.trade) || 0,
                    f24: parseFloat(item.high) || 0,
                    f25: parseFloat(item.low) || 0,
                    f46: parseInt(item.volume) || 0
                }))
            }
        };

        console.log(`成功获取新浪财经数据，原始数据${allStocks.length}条，去重后${convertedData.data.diff.length}条记录`);
        res.json(convertedData);

    } catch (error) {
        console.error('新浪财经API代理错误:', error.message);
        res.status(500).json({
            error: '无法获取真实股票数据',
            message: '新浪财经API访问失败，请稍后重试',
            details: error.message
        });
    }
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 启动服务器
app.listen(port, () => {
    console.log(`服务器运行在 http://localhost:${port}`);
});