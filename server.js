const express = require('express');
const cors = require('cors');
const path = require('path');
const axios = require('axios');

const app = express();
const port = process.env.PORT || 3000;

// 启用 CORS
app.use(cors());

// 设置静态文件目录
app.use(express.static('public'));

// 添加API代理路由 - 使用新浪财经API
app.get('/api/stocks', async (req, res) => {
    try {
        const page = req.query.page || 1;
        const pageSize = Math.min(req.query.pageSize || 100, 200); // 限制最大200条

        console.log(`代理请求: 第${page}页, 每页${pageSize}条`);

        // 使用新浪财经API获取A股数据
        const response = await axios.get('http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData', {
            params: {
                page: page,
                num: pageSize,
                sort: 'changepercent',
                asc: 0,
                node: 'hs_a', // 沪深A股
                symbol: '',
                _s_r_a: 'init'
            },
            timeout: 10000
        });

        if (!response.data || !Array.isArray(response.data)) {
            throw new Error('新浪API返回数据格式不正确');
        }

        // 转换新浪API数据格式为东方财富格式，保持前端兼容性
        const convertedData = {
            data: {
                diff: response.data.map(item => ({
                    f12: item.symbol, // 股票代码
                    f14: item.name, // 股票名称
                    f2: parseFloat(item.trade) || 0, // 最新价
                    f3: parseFloat(item.changepercent) || 0, // 涨跌幅
                    f17: parseFloat(item.open) || 0, // 今开
                    f18: parseFloat(item.settlement) || 0, // 昨收
                    f5: parseInt(item.volume) || 0, // 成交量
                    f6: parseFloat(item.amount) || 0, // 成交额
                    f10: parseFloat(item.per) || 0, // 市盈率
                    f8: parseFloat(item.turnoverratio) || 0, // 换手率
                    f1: 1,
                    f4: parseFloat(item.change) || 0, // 涨跌额
                    f7: parseFloat(item.change) || 0,
                    f9: parseFloat(item.high) || 0, // 最高
                    f11: parseFloat(item.low) || 0, // 最低
                    f15: parseFloat(item.amount) || 0,
                    f16: parseFloat(item.per) || 0,
                    f20: parseFloat(item.amount) || 0,
                    f21: parseFloat(item.amount) || 0,
                    f22: parseFloat(item.changepercent) || 0,
                    f23: parseFloat(item.trade) || 0,
                    f24: parseFloat(item.high) || 0,
                    f25: parseFloat(item.low) || 0,
                    f46: parseInt(item.volume) || 0
                }))
            }
        };

        console.log(`成功获取新浪财经数据，返回${convertedData.data.diff.length}条记录`);
        res.json(convertedData);

    } catch (error) {
        console.error('新浪财经API代理错误:', error.message);
        res.status(500).json({
            error: '无法获取真实股票数据',
            message: '新浪财经API访问失败，请稍后重试',
            details: error.message
        });
    }
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 启动服务器
app.listen(port, () => {
    console.log(`服务器运行在 http://localhost:${port}`);
});