const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const port = process.env.PORT || 3000;

// 启用 CORS
app.use(cors());

// 设置静态文件目录
app.use(express.static('public'));

// 添加API代理路由
app.get('/api/stocks', async (req, res) => {
    try {
        const { default: fetch } = await import('node-fetch');

        const page = req.query.page || 1;
        const pageSize = req.query.pageSize || 100;

        const url = `https://push2.eastmoney.com/api/qt/clist/get?pn=${page}&pz=${pageSize}&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81,m:1+t:3&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f46&_=${Date.now()}`;

        console.log(`代理请求: 第${page}页, 每页${pageSize}条`);

        const response = await fetch(url);
        const data = await response.json();

        res.json(data);
    } catch (error) {
        console.error('API代理错误:', error);
        res.status(500).json({ error: '获取股票数据失败' });
    }
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 启动服务器
app.listen(port, () => {
    console.log(`服务器运行在 http://localhost:${port}`);
});