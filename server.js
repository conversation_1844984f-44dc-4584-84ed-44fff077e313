const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const port = process.env.PORT || 3000;

// 启用 CORS
app.use(cors());

// 设置静态文件目录
app.use(express.static('public'));

// 添加API代理路由
app.get('/api/stocks', async (req, res) => {
    try {
        const { default: fetch } = await import('node-fetch');

        const page = req.query.page || 1;
        const pageSize = req.query.pageSize || 100;

        const url = `https://push2.eastmoney.com/api/qt/clist/get?pn=${page}&pz=${pageSize}&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f46&_=${Date.now()}`;

        console.log(`代理请求: 第${page}页, 每页${pageSize}条`);

        // 添加请求头模拟浏览器
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'http://quote.eastmoney.com/',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            },
            timeout: 10000 // 10秒超时
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log(`成功获取数据，返回${data.data?.diff?.length || 0}条记录`);

        res.json(data);
    } catch (error) {
        console.error('API代理错误:', error.message);

        // 返回模拟数据作为备用方案
        const mockData = {
            data: {
                diff: generateMockStockData(100) // 生成100条模拟股票数据
            }
        };

        console.log('返回模拟数据作为备用方案');
        res.json(mockData);
    }
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 启动服务器
app.listen(port, () => {
    console.log(`服务器运行在 http://localhost:${port}`);
});