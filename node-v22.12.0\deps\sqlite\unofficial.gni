# This file is used by GN for building, which is NOT the build system used for
# building official binaries.
# Please edit the gyp files if you are making changes to build system.

# The actual configurations are put inside a template in unofficial.gni to
# prevent accidental edits from contributors.
template("sqlite_gn_build") {
  config("sqlite_config") {
    include_dirs = [ "." ]
  }

  gypi_values = exec_script("../../tools/gypi_to_gn.py",
                            [ rebase_path("sqlite.gyp") ],
                            "scope",
                            [ "sqlite.gyp" ])

  source_set(target_name) {
    forward_variables_from(invoker, "*")
    public_configs = [ ":sqlite_config" ]
    sources = gypi_values.sqlite_sources
    cflags_c = [
      "-Wno-implicit-fallthrough",
      "-Wno-unreachable-code-return",
      "-Wno-unreachable-code-break",
      "-Wno-unreachable-code",
    ]
    if (is_win) {
      cflags_c += [
        "-Wno-sign-compare",
        "-Wno-unused-but-set-variable",
        "-Wno-unused-function",
        "-Wno-unused-variable",
      ]
    }
  }
}
