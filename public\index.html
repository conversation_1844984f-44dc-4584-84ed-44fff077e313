<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A股股票列表</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <style>
        .stock-chart-tooltip {
            position: fixed;
            z-index: 9999;
            background: rgba(0, 0, 0, 0.85);
            border-radius: 8px;
            padding: 10px;
            display: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stock-chart-container {
            width: 500px;
            height: 300px;
            background: #1f1f1f;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="page-title">A股股票监控</h1>

        <div class="strategy-section">
            <!-- 过滤条件 -->
            <div class="filter-container">
                <div class="filter-row">
                    <div class="filter-item">
                        <label>开盘涨跌:</label>
                        <div class="input-group">
                            <input type="number" id="minOpenHigher2" class="form-control" step="0.01" placeholder="最小值">
                            <span class="input-group-text">-</span>
                            <input type="number" id="maxOpenHigher2" class="form-control" step="0.01" placeholder="最大值">
                        </div>
                    </div>
                    <div class="filter-item">
                        <label>开盘价:</label>
                        <div class="input-group">
                            <input type="number" id="minOpenPrice" class="form-control" step="0.01" placeholder="最小值">
                            <span class="input-group-text">-</span>
                            <input type="number" id="maxOpenPrice" class="form-control" step="0.01" placeholder="最大值">
                        </div>
                    </div>
                    <div class="filter-item">
                        <label>竞价量比:</label>
                        <div class="input-group">
                            <input type="number" id="minVolRatio" class="form-control" step="0.01" placeholder="最小值">
                            <span class="input-group-text">-</span>
                            <input type="number" id="maxVolRatio" class="form-control" step="0.01" placeholder="最大值">
                        </div>
                    </div>
                    <div class="filter-item">
                        <label>竞价动能:</label>
                        <div class="input-group">
                            <input type="number" id="minAuctionMomentum" class="form-control" step="0.01" placeholder="最小值">
                            <span class="input-group-text">-</span>
                            <input type="number" id="maxAuctionMomentum" class="form-control" step="0.01" placeholder="最大值">
                        </div>
                    </div>
                    <div class="filter-item">
                        <label>竞价评分:</label>
                        <div class="input-group">
                            <input type="number" id="minAuctionScore" class="form-control" step="0.01" placeholder="最小值">
                            <span class="input-group-text">-</span>
                            <input type="number" id="maxAuctionScore" class="form-control" step="0.01" placeholder="最大值">
                        </div>
                    </div>
                </div>
                <div class="filter-row justify-content-end">
                    <button class="btn btn-secondary" onclick="resetFilters()">重置过滤器</button>
                </div>
            </div>

            <!-- 股票列表 -->
            <div class="list-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="stats">
                        <h3>A股股票列表</h3>
                        <span id="stockCount" class="text-muted">加载中...</span>
                    </div>
                    <div class="search-container">
                        <input type="text" id="searchInput" class="form-control" placeholder="搜索股票代码或名称...">
                    </div>
                </div>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th class="sorting" data-column="index">序号</th>
                            <th class="sorting" data-column="code">代码</th>
                            <th class="sorting" data-column="name">名称</th>
                            <th class="sorting" data-column="price">最新价</th>
                            <th class="sorting" data-column="changePercent">涨跌幅</th>
                            <th class="sorting" data-column="open">今开</th>
                            <th class="sorting" data-column="openHigherPercent">开盘涨跌</th>
                            <th class="sorting" data-column="preClose">昨收</th>
                            <th class="sorting" data-column="volRatio">量比</th>
                            <th class="sorting" data-column="auctionVolRatio">竞价量比</th>
                            <th class="sorting" data-column="auctionTurnover">竞价换手</th>
                            <th class="sorting" data-column="auctionMomentum">竞价动能</th>
                            <th class="sorting" data-column="auctionScore">竞价评分</th>
                            <th class="sorting" data-column="turnover">换手率</th>
                            <th class="sorting" data-column="dayProfit">当日收益</th>
                            <th class="sorting" data-column="amount">成交额</th>
                        </tr>
                    </thead>
                    <tbody id="stockList">
                        <!-- 股票数据将通过JavaScript动态插入 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div id="stockChartTooltip" class="stock-chart-tooltip">
        <div id="stockChartContainer" class="stock-chart-container"></div>
    </div>

    <script>
        // 全局变量
        let stockData = [];
        let filteredData = [];
        let currentSortColumn = null;
        let currentSortDirection = 'desc';
        const FILTER_STORAGE_KEY = 'stockFilterValues';
        let stockChart = null;
        let currentHoverCode = null;
        let chartTooltip = null;
        let chartContainer = null;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化应用...');
            loadFilterValues(); // 加载保存的过滤器值
            initializeSortListeners();
            initializeSearchListener();
            initializeFilterListeners();
            loadStockData();
            
            // 增加刷新间隔到10秒
            setInterval(loadStockData, 10000);
            
            // 初始化图表
            initializeChart();
        });

        // 初始化搜索功能
        function initializeSearchListener() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', () => {
                    filterAndUpdateList();
                });
            }
        }

        // 初始化排序功能
        function initializeSortListeners() {
            const headers = document.querySelectorAll('.sorting');
            headers.forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    if (currentSortColumn === column) {
                        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSortColumn = column;
                        currentSortDirection = 'desc';
                    }
                    
                    headers.forEach(h => h.classList.remove('sorted-asc', 'sorted-desc'));
                    this.classList.add(`sorted-${currentSortDirection}`);
                    
                    filterAndUpdateList();
                });
            });
        }

        // 初始化过滤条件监听器
        function initializeFilterListeners() {
            const filterInputs = document.querySelectorAll('.filter-item input');
            filterInputs.forEach(input => {
                input.addEventListener('input', () => {
                    filterAndUpdateList();
                    saveFilterValues(); // 当值改变时保存
                });
            });
        }

        // 加载股票数据
        async function loadStockData() {
            try {
                console.log('开始加载股票数据...');

                // 先尝试简单的单页请求，获取更多数据
                const url = 'https://push2.eastmoney.com/api/qt/clist/get?pn=1&pz=100&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81,m:1+t:3&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f46&_=' + Date.now();

                console.log('请求URL:', url);
                const response = await fetch(url);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('接收到数据:', data);

                if (!data.data?.diff) {
                    throw new Error('数据格式不正确: ' + JSON.stringify(data));
                }

                // 添加数据预处理的日志
                console.log('原始数据条数:', data.data.diff.length);

                stockData = data.data.diff

                    .filter(item => {
                        const name = item.f14;
                        const open = parseFloat(item.f17);
                        const preClose = parseFloat(item.f18);

                        // 只过滤掉数据异常的股票，保留所有正常交易的股票（包括ST股票）
                        const isValid = name && !isNaN(open) && !isNaN(preClose) &&
                                      open > 0 && preClose > 0;

                        if (!isValid) {
                            console.log('过滤掉股票:', name, '开盘价:', open, '昨收:', preClose);
                        }

                        return isValid;
                    })
                    .map(item => {
                        const open = parseFloat(item.f17);
                        const preClose = parseFloat(item.f18);
                        const price = parseFloat(item.f2);
                        const volume = parseInt(item.f5);
                        const amount = parseFloat(item.f6);
                        const openHigherPercent = ((open - preClose) / preClose * 100);
                        
                        // 计算当日收益
                        const dayProfit = ((price / open - 1) * 100);
                        
                        // 计算竞价相关指标
                        const auctionAmount = (item.f15 || 0) / 10000;
                        const auctionVolume = item.f5 || 0;
                        const auctionTurnover = ((auctionAmount / (open * item.f46 * 100)) * 100) || 0;
                        const auctionVolRatio = item.f10 || 0;
                        const auctionMomentum = ((open - preClose) * auctionVolume / 10000) || 0;
                        const auctionScore = (openHigherPercent * auctionTurnover) || 0;

                        return {
                            code: item.f12,
                            name: item.f14,
                            price: price,
                            changePercent: parseFloat(item.f3),
                            open: open,
                            preClose: preClose,
                            volume: volume,
                            amount: amount,
                            volRatio: parseFloat(item.f10),
                            turnover: parseFloat(item.f8),
                            openHigherPercent: openHigherPercent,
                            auctionVolRatio: auctionVolRatio,
                            auctionTurnover: auctionTurnover,
                            auctionMomentum: auctionMomentum,
                            auctionScore: auctionScore,
                            dayProfit: dayProfit
                        };
                    });

                console.log('过滤后数据条数:', stockData.length);
                
                if (stockData.length === 0) {
                    throw new Error('过滤后没有符合条件的数据');
                }

                filterAndUpdateList();
            } catch (error) {
                console.error('加载股票数据失败:', error);
                document.getElementById('stockCount').textContent = '数据加载失败: ' + error.message;
                
                // 如果是网络错误，5秒后重试
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    console.log('5秒后重试加载数据...');
                    setTimeout(loadStockData, 5000);
                }
            }
        }

        // 过滤并更新列表
        function filterAndUpdateList() {
            try {
                const searchText = document.getElementById('searchInput')?.value.toLowerCase() || '';
                
                // 获取过滤条件值
                const minOpenPrice = parseFloat(document.getElementById('minOpenPrice')?.value) || 0;
                const maxOpenPrice = parseFloat(document.getElementById('maxOpenPrice')?.value) || Infinity;
                const minOpenHigher2 = parseFloat(document.getElementById('minOpenHigher2')?.value) || -Infinity;
                const maxOpenHigher2 = parseFloat(document.getElementById('maxOpenHigher2')?.value) || Infinity;
                const minVolRatio = parseFloat(document.getElementById('minVolRatio')?.value) || 0;
                const maxVolRatio = parseFloat(document.getElementById('maxVolRatio')?.value) || Infinity;
                const minAuctionMomentum = parseFloat(document.getElementById('minAuctionMomentum')?.value) || -Infinity;
                const maxAuctionMomentum = parseFloat(document.getElementById('maxAuctionMomentum')?.value) || Infinity;
                const minAuctionScore = parseFloat(document.getElementById('minAuctionScore')?.value) || -Infinity;
                const maxAuctionScore = parseFloat(document.getElementById('maxAuctionScore')?.value) || Infinity;

                filteredData = stockData.filter(stock => {
                    if (searchText && !stock.code.includes(searchText) && !stock.name.toLowerCase().includes(searchText)) {
                        return false;
                    }

                    if (stock.open < minOpenPrice || stock.open > maxOpenPrice) return false;
                    if (stock.openHigherPercent < minOpenHigher2 || stock.openHigherPercent > maxOpenHigher2) return false;
                    if (stock.volRatio < minVolRatio || stock.volRatio > maxVolRatio) return false;
                    if (stock.auctionMomentum < minAuctionMomentum || stock.auctionMomentum > maxAuctionMomentum) return false;
                    if (stock.auctionScore < minAuctionScore || stock.auctionScore > maxAuctionScore) return false;

                    return true;
                });

                if (currentSortColumn) {
                    filteredData.sort((a, b) => {
                        let aValue = a[currentSortColumn];
                        let bValue = b[currentSortColumn];
                        
                        if (typeof aValue === 'string') {
                            return currentSortDirection === 'asc' 
                                ? aValue.localeCompare(bValue)
                                : bValue.localeCompare(aValue);
                        }
                        
                        return currentSortDirection === 'asc' 
                            ? aValue - bValue 
                            : bValue - aValue;
                    });
                }

                updateStockList();
            } catch (error) {
                console.error('过滤数据时出错:', error);
            }
        }

        // 更新股票列表显示
        function updateStockList() {
            try {
                const tbody = document.getElementById('stockList');
                if (!tbody) return;

                tbody.innerHTML = '';

                if (!filteredData || filteredData.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="12" class="text-center">没有找到符合条件的股票</td></tr>';
                    document.getElementById('stockCount').textContent = '没有找到符合条件的股票';
                    return;
                }

                filteredData.forEach((stock, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${stock.code}</td>
                        <td>${stock.name}</td>
                        <td>${stock.price.toFixed(2)}</td>
                        <td class="${stock.changePercent >= 0 ? 'text-danger' : 'text-success'}">${stock.changePercent.toFixed(2)}%</td>
                        <td>${stock.open.toFixed(2)}</td>
                        <td class="${stock.openHigherPercent >= 0 ? 'text-danger' : 'text-success'}">${stock.openHigherPercent.toFixed(2)}%</td>
                        <td>${stock.preClose.toFixed(2)}</td>
                        <td>${stock.volRatio.toFixed(2)}</td>
                        <td>${stock.auctionVolRatio.toFixed(2)}</td>
                        <td>${stock.auctionTurnover.toFixed(2)}%</td>
                        <td>${stock.auctionMomentum.toFixed(2)}</td>
                        <td>${stock.auctionScore.toFixed(2)}</td>
                        <td>${stock.turnover.toFixed(2)}%</td>
                        <td class="${stock.dayProfit >= 0 ? 'text-danger' : 'text-success'}">${stock.dayProfit.toFixed(2)}%</td>
                        <td>${(stock.amount/10000).toFixed(0)}</td>
                    `;
                    tbody.appendChild(row);
                });

                document.getElementById('stockCount').textContent = `共找到 ${filteredData.length} 只股票`;
            } catch (error) {
                console.error('更新列表显示时出错:', error);
            }
        }

        // 添加保存和加载过滤器值的函数
        function saveFilterValues() {
            const filterValues = {
                minOpenHigher2: document.getElementById('minOpenHigher2').value,
                maxOpenHigher2: document.getElementById('maxOpenHigher2').value,
                minOpenPrice: document.getElementById('minOpenPrice').value,
                maxOpenPrice: document.getElementById('maxOpenPrice').value,
                minVolRatio: document.getElementById('minVolRatio').value,
                maxVolRatio: document.getElementById('maxVolRatio').value,
                minAuctionMomentum: document.getElementById('minAuctionMomentum').value,
                maxAuctionMomentum: document.getElementById('maxAuctionMomentum').value,
                minAuctionScore: document.getElementById('minAuctionScore').value,
                maxAuctionScore: document.getElementById('maxAuctionScore').value
            };
            localStorage.setItem(FILTER_STORAGE_KEY, JSON.stringify(filterValues));
        }

        function loadFilterValues() {
            try {
                const savedValues = localStorage.getItem(FILTER_STORAGE_KEY);
                if (savedValues) {
                    const filterValues = JSON.parse(savedValues);
                    Object.entries(filterValues).forEach(([id, value]) => {
                        const element = document.getElementById(id);
                        if (element && value !== '') {
                            element.value = value;
                        }
                    });
                }
            } catch (error) {
                console.error('加载过滤器值时出错:', error);
            }
        }

        function resetFilters() {
            localStorage.removeItem(FILTER_STORAGE_KEY);
            document.querySelectorAll('.filter-item input').forEach(input => {
                input.value = '';
            });
            filterAndUpdateList();
        }

        // 添加图表初始化和更新函数
        function initializeChart() {
            chartTooltip = document.getElementById('stockChartTooltip');
            chartContainer = document.getElementById('stockChartContainer');
            stockChart = echarts.init(chartContainer);
            
            // 监听表格中的股票代码单元格
            document.getElementById('stockList').addEventListener('mouseover', async (e) => {
                const cell = e.target.closest('td');
                if (!cell) return;
                
                const row = cell.parentElement;
                if (!row) return;
                
                const codeCell = row.children[1]; // 股票代码所在的单元格
                if (cell === codeCell) {
                    const code = cell.textContent;
                    if (code !== currentHoverCode) {
                        currentHoverCode = code;
                        await updateStockChart(code);
                    }
                    showChartTooltip(e);
                }
            });

            // 监听鼠标移出
            document.getElementById('stockList').addEventListener('mouseout', (e) => {
                const cell = e.target.closest('td');
                if (!cell) return;
                
                const row = cell.parentElement;
                if (!row) return;
                
                const codeCell = row.children[1];
                if (cell === codeCell) {
                    hideChartTooltip();
                }
            });
        }

        async function updateStockChart(code) {
            try {
                // 显示加载状态
                stockChart.showLoading({
                    text: '加载中...',
                    color: '#fff',
                    textColor: '#fff',
                    maskColor: 'rgba(0, 0, 0, 0.3)'
                });

                // 获取股票竞价分时数据
                const response = await fetch(`https://push2.eastmoney.com/api/qt/stock/trends2/get?fields1=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13&fields2=f51,f52,f53,f54,f55,f56,f57,f58&ut=fa5fd1943c7b386f172d6893dbfba10b&iscr=0&ndays=1&secid=${getSecId(code)}&_=${Date.now()}`);
                const data = await response.json();
                
                if (!data.data?.trends) {
                    throw new Error('无法获取分时数据');
                }

                const trends = data.data.trends.map(item => {
                    const [time, price] = item.split(',');
                    return [time, parseFloat(price)];
                });

                const option = {
                    backgroundColor: '#1f1f1f',
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        textStyle: {
                            color: '#fff'
                        }
                    },
                    grid: {
                        left: '5%',
                        right: '5%',
                        bottom: '10%',
                        top: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: trends.map(item => item[0]),
                        axisLine: { 
                            lineStyle: { color: '#666' } 
                        },
                        axisLabel: { 
                            color: '#999',
                            fontSize: 10,
                            formatter: (value) => value.substring(value.length - 5) // 只显示时间部分
                        }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true,
                        axisLine: { 
                            lineStyle: { color: '#666' } 
                        },
                        axisLabel: { 
                            color: '#999',
                            fontSize: 10
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#333'
                            }
                        }
                    },
                    series: [{
                        name: '价格',
                        data: trends.map(item => item[1]),
                        type: 'line',
                        smooth: true,
                        symbol: 'none',
                        lineStyle: { 
                            width: 2,
                            color: '#ff4444'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: 'rgba(255, 68, 68, 0.3)'
                            }, {
                                offset: 1,
                                color: 'rgba(255, 68, 68, 0.05)'
                            }])
                        }
                    }]
                };

                stockChart.hideLoading();
                stockChart.setOption(option, true);
            } catch (error) {
                console.error('更新股票图表失败:', error);
                stockChart.hideLoading();
            }
        }

        function showChartTooltip(event) {
            const tooltip = document.getElementById('stockChartTooltip');
            tooltip.style.display = 'block';
            
            // 计算位置，确保不超出视窗
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const tooltipWidth = 500; // 与 CSS 中的宽度一致
            const tooltipHeight = 300; // 与 CSS 中的高度一致
            
            let left = event.pageX + 20;
            let top = event.pageY - tooltipHeight/2;
            
            // 确保不超出右边界
            if (left + tooltipWidth > viewportWidth) {
                left = event.pageX - tooltipWidth - 20;
            }
            
            // 确保不超出上下边界
            if (top < 10) {
                top = 10;
            } else if (top + tooltipHeight > viewportHeight - 10) {
                top = viewportHeight - tooltipHeight - 10;
            }
            
            tooltip.style.left = `${left}px`;
            tooltip.style.top = `${top}px`;
            
            // 重新调整图表大小
            if (stockChart) {
                stockChart.resize();
            }
        }

        function hideChartTooltip() {
            const tooltip = document.getElementById('stockChartTooltip');
            tooltip.style.display = 'none';
            currentHoverCode = null;
        }

        // 辅助函数：获取股票的 secid
        function getSecId(code) {
            const market = code.startsWith('6') ? '1' : '0';
            return `${market}.${code}`;
        }
    </script>
</body>
</html> 