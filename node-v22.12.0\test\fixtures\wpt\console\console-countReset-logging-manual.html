<!DOCTYPE html>
<html>
<head>
<title>Console Count Reset - Logging Manual Test</title>
<meta name="author" title="<PERSON> Farolino" href="mailto:domfar<PERSON><EMAIL>">
<meta name="assert" content="Console countReset method">
<link rel="help" href="https://console.spec.whatwg.org/#countreset">
</head>
<body>
<p>Open the console inside the developer tools. It should contain entries whose contents are:</p>
<p><code>default: 1</code></p>
<p><code>default: 1</code></p>
<p><code>default: 1</code></p>
<p><code>default: 1</code></p>
<p><code>default: 1</code></p>
<p><code>default: 1</code></p>
<p><code>default: 1</code></p>
<p><code>default: 1</code></p>
<p><code>a label: 1</code></p>
<p><code>a label: 1</code></p>
<p style="color:grey;">[some warning message indicating that a count for label "b" does not exist]</p>

<script>
console.count();
console.countReset();
console.count();
console.countReset();

console.count(undefined);
console.countReset(undefined);
console.count(undefined);
console.countReset(undefined);

console.count("default");
console.countReset("default");
console.count("default");
console.countReset("default");

console.count({toString() {return "default"}});
console.countReset({toString() {return "default"}});
console.count({toString() {return "default"}});
console.countReset({toString() {return "default"}});

console.count("a label");
console.countReset("a label");
console.count("a label");

console.countReset("b"); // should produce a warning
</script>
</body>
</html>
