function foo(str) {
    return str;
}
foo('noop');
// To recreate (Windows only):
//
// const filePath = require.resolve('./test/fixtures/source-map/ts-node.ts');
// const compiled = require('ts-node').create({ transpileOnly: true }).compile(fs.readFileSync(filePath, 'utf8'), filePath);
// fs.writeFileSync('test/fixtures/source-map/ts-node-win32.js', compiled);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiRDovd29ya3NwYWNlcy9ub2RlL3Rlc3QvZml4dHVyZXMvc291cmNlLW1hcC90cy1ub2RlLnRzIiwic291cmNlcyI6WyJEOi93b3Jrc3BhY2VzL25vZGUvdGVzdC9maXh0dXJlcy9zb3VyY2UtbWFwL3RzLW5vZGUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsU0FBUyxHQUFHLENBQUMsR0FBVztJQUN0QixPQUFPLEdBQUcsQ0FBQztBQUNiLENBQUM7QUFFRCxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUM7QUFFWiw4QkFBOEI7QUFDOUIsRUFBRTtBQUNGLDZFQUE2RTtBQUM3RSw0SEFBNEg7QUFDNUgsMkVBQTJFIiwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZm9vKHN0cjogc3RyaW5nKSB7XG4gIHJldHVybiBzdHI7XG59XG5cbmZvbygnbm9vcCcpO1xuXG4vLyBUbyByZWNyZWF0ZSAoV2luZG93cyBvbmx5KTpcbi8vXG4vLyBjb25zdCBmaWxlUGF0aCA9IHJlcXVpcmUucmVzb2x2ZSgnLi90ZXN0L2ZpeHR1cmVzL3NvdXJjZS1tYXAvdHMtbm9kZS50cycpO1xuLy8gY29uc3QgY29tcGlsZWQgPSByZXF1aXJlKCd0cy1ub2RlJykuY3JlYXRlKHsgdHJhbnNwaWxlT25seTogdHJ1ZSB9KS5jb21waWxlKGZzLnJlYWRGaWxlU3luYyhmaWxlUGF0aCwgJ3V0ZjgnKSwgZmlsZVBhdGgpO1xuLy8gZnMud3JpdGVGaWxlU3luYygndGVzdC9maXh0dXJlcy9zb3VyY2UtbWFwL3RzLW5vZGUtd2luMzIuanMnLCBjb21waWxlZCk7XG4iXX0=
