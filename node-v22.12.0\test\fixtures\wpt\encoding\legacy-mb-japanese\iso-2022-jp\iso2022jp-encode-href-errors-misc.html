<!DOCTYPE html>
<html>
<head>
<meta charset="iso-2022-jp"> <!-- test breaks if the server overrides this -->
<title>ISO 2022-JP encoding errors (href, misc)</title>
<meta name="timeout" content="long">
<meta name="variant" content="?1-1000">
<meta name="variant" content="?1001-2000">
<meta name="variant" content="?2001-3000">
<meta name="variant" content="?3001-last">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/common/subset-tests.js"></script>
<script src="jis0208_index.js"></script>
<script src="iso2022jp-encoder.js"></script>
<link rel="author" title="Richard Ishida" href="mailto:<EMAIL>">
<link rel="help" href="https://encoding.spec.whatwg.org/#iso-2022-jp">
<meta name="assert" content="The browser produces percent-escaped character references when writing characters to an href value and encoding miscellaneous characters that are not in the iso-2022-jp encoding.">
<script src="../../resources/ranges.js"></script>
<script>
var errors = true;
var encoder = iso2022jpEncoder;
var ranges = rangesMisc;
function expect(result, codepoint) {
  return "%26%23" + codepoint + "%3B";
}
// Overwrite normalizeStr
function normalizeStr(str) {
  return str;
}
</script>
<script src="../../resources/encode-href-common.js"></script>
</head>
<body>
<div id="log"></div>
</body>
</html>
