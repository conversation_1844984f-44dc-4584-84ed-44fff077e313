#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于通达信API的股票数据服务器
获取所有A股数据
"""

from pytdx.hq import TdxHq_API
import time
from flask import Flask, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# 可用的通达信服务器列表（按响应时间排序）
TDX_SERVERS = [
    {"ip": "sztdx.gtjas.com", "port": 7709},
    {"ip": "************", "port": 7709},
    {"ip": "**************", "port": 7709},
    {"ip": "**************", "port": 7709},
    {"ip": "**************", "port": 7709},
    {"ip": "**************", "port": 7709},
    {"ip": "shtdx.gtjas.com", "port": 7709},
    {"ip": "*************", "port": 7709},
    {"ip": "jstdx.gtjas.com", "port": 7709}
]

class TdxStockAPI:
    def __init__(self):
        self.api = TdxHq_API(raise_exception=False)
        self.current_server = None
        self.connect_to_best_server()
    
    def connect_to_best_server(self):
        """连接到最佳服务器"""
        for server in TDX_SERVERS:
            try:
                print(f"尝试连接服务器: {server['ip']}:{server['port']}")
                if self.api.connect(server['ip'], server['port']):
                    # 测试连接是否正常工作
                    count = self.api.get_security_count(0)
                    if count > 0:
                        self.current_server = server
                        print(f"✅ 成功连接到: {server['ip']}:{server['port']}")
                        return True
                    else:
                        self.api.disconnect()
            except Exception as e:
                print(f"❌ 连接失败: {server['ip']}:{server['port']} - {str(e)}")
                continue
        
        print("❌ 无法连接到任何服务器")
        return False
    
    def get_all_stocks(self):
        """获取所有A股股票列表"""
        try:
            all_stocks = []

            # 获取上海A股 - 扫描更大范围寻找真正的股票
            print("获取上海A股...")
            for start_pos in range(0, 5000, 1000):  # 扫描更大范围
                batch = self.api.get_security_list(0, start_pos)
                if not batch:
                    break

                # 过滤A股
                a_stocks = []
                for stock in batch:
                    code = stock['code']
                    name = stock['name']
                    # 上海A股：6开头的股票，长度为6位数字
                    if code.startswith('6') and len(code) == 6 and code.isdigit():
                        # 排除指数和其他非股票
                        if not any(keyword in name for keyword in ['指数', '基金', 'ETF', 'LOF', 'REIT', '债券', '回购', 'Ａ股', 'Ｂ股', 'DR']):
                            a_stocks.append(stock)

                all_stocks.extend(a_stocks)
                print(f"上海A股 {start_pos}-{start_pos+len(batch)}: {len(a_stocks)}只A股 (原始{len(batch)}只)")
                if len(batch) > 0:
                    print(f"  样本代码: {[s['code'] for s in batch[:3]]}")

                time.sleep(0.1)

            # 获取深圳A股
            print("获取深圳A股...")
            for start_pos in range(0, 5000, 1000):  # 扫描更大范围
                batch = self.api.get_security_list(1, start_pos)
                if not batch:
                    break

                # 过滤A股
                a_stocks = []
                for stock in batch:
                    code = stock['code']
                    name = stock['name']
                    # 深圳A股：000、001、002、003、300开头的股票，长度为6位数字
                    if (code.startswith(('000', '001', '002', '003', '300')) and
                        len(code) == 6 and code.isdigit()):
                        # 排除指数和其他非股票
                        if not any(keyword in name for keyword in ['指数', '基金', 'ETF', 'LOF', 'REIT', '债券', '回购', 'Ａ股', 'Ｂ股', 'DR']):
                            a_stocks.append(stock)

                all_stocks.extend(a_stocks)
                print(f"深圳A股 {start_pos}-{start_pos+len(batch)}: {len(a_stocks)}只A股 (原始{len(batch)}只)")
                if len(batch) > 0:
                    print(f"  样本代码: {[s['code'] for s in batch[:3]]}")

                time.sleep(0.1)

            print(f"总共获取A股: {len(all_stocks)}只")
            return all_stocks

        except Exception as e:
            print(f"获取股票列表失败: {str(e)}")
            return []
    
    def get_stock_quotes(self, stocks):
        """获取股票行情数据"""
        try:
            quotes = []
            
            # 分批获取行情数据
            batch_size = 100
            for i in range(0, len(stocks), batch_size):
                batch_stocks = stocks[i:i+batch_size]
                
                for stock in batch_stocks:
                    try:
                        # 确定市场代码
                        market = 0 if stock['code'].startswith(('6', '9')) else 1
                        
                        # 获取实时行情
                        quote = self.api.get_security_quotes([(market, stock['code'])])
                        if quote and len(quote) > 0:
                            q = quote[0]
                            # 价格需要除以100转换为正确格式
                            price = q['price'] / 100.0
                            open_price = q['open'] / 100.0
                            last_close = q['last_close'] / 100.0
                            high = q['high'] / 100.0
                            low = q['low'] / 100.0

                            quotes.append({
                                'f12': stock['code'],  # 股票代码
                                'f14': stock['name'],  # 股票名称
                                'f2': price,           # 最新价
                                'f3': round((price - last_close) / last_close * 100, 2) if last_close > 0 else 0,  # 涨跌幅
                                'f17': open_price,     # 今开
                                'f18': last_close,     # 昨收
                                'f5': q['vol'],        # 成交量
                                'f6': q['amount'],     # 成交额
                                'f10': 0,              # 市盈率（通达信API不直接提供）
                                'f8': 0,               # 换手率（通达信API不直接提供）
                                'f15': high,           # 最高
                                'f16': low,            # 最低
                            })
                    except Exception as e:
                        print(f"获取 {stock['code']} 行情失败: {str(e)}")
                        continue
                
                # 每批次之间稍作延迟
                time.sleep(0.1)
                print(f"已获取 {len(quotes)} 只股票行情数据")
            
            return quotes
            
        except Exception as e:
            print(f"获取行情数据失败: {str(e)}")
            return []
    
    def disconnect(self):
        """断开连接"""
        if self.api:
            self.api.disconnect()

# 全局API实例
tdx_api = TdxStockAPI()

@app.route('/api/stocks')
def get_stocks():
    """获取股票数据API"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 200))
        
        print(f"请求第{page}页，每页{page_size}条数据")
        
        # 获取所有股票列表
        all_stocks = tdx_api.get_all_stocks()
        
        if not all_stocks:
            return jsonify({
                'error': '无法获取股票数据',
                'message': '通达信API访问失败'
            }), 500
        
        # 获取行情数据
        quotes = tdx_api.get_stock_quotes(all_stocks)
        
        # 分页处理
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_quotes = quotes[start_idx:end_idx]
        
        # 转换为东方财富格式
        result = {
            'rc': 0,
            'rt': 6,
            'svr': 181669084,
            'lt': 1,
            'full': 1,
            'data': {
                'total': len(quotes),
                'diff': page_quotes
            }
        }
        
        print(f"返回 {len(page_quotes)} 条股票数据，总共 {len(quotes)} 只股票")
        return jsonify(result)
        
    except Exception as e:
        print(f"API错误: {str(e)}")
        return jsonify({
            'error': '服务器内部错误',
            'message': str(e)
        }), 500

@app.route('/')
def index():
    """主页"""
    return '''
    <h1>通达信股票数据API</h1>
    <p>使用 /api/stocks 获取股票数据</p>
    <p>当前连接服务器: {}</p>
    '''.format(tdx_api.current_server if tdx_api.current_server else '未连接')

if __name__ == '__main__':
    print("启动通达信股票数据服务器...")
    print(f"当前连接服务器: {tdx_api.current_server}")
    app.run(host='0.0.0.0', port=5000, debug=False)
