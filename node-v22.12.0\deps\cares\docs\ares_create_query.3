.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\" SPDX-License-Identifier: MIT
.\"
.TH ARES_CREATE_QUERY 3 "17 Aug 2012"
.SH NAME
ares_create_query \- Compose a single-question DNS query buffer
.SH SYNOPSIS
.nf
#include <ares.h>

int ares_create_query(const char *\fIname\fP,
                      int \fIdnsclass\fP,
                      int \fItype\fP,
                      unsigned short \fIid\fP,
                      int \fIrd\fP,
                      unsigned char **\fIbuf\fP,
                      int *\fIbuflen\fP,
                      int \fImax_udp_size\fP)
.fi
.SH DESCRIPTION
The \fIares_create_query(3)\fP function composes a DNS query with a single
question.  The parameter \fIname\fP gives the query name as a NUL-terminated C
string of period-separated labels optionally ending with a period; periods and
backslashes within a label must be escaped with a backlash.

The parameters \fIdnsclass\fP and \fItype\fP give the class and type of the
query using the values defined in \fB<arpa/nameser.h>\fP.

The parameter \fIid\fP gives a 16-bit identifier for the query.

The parameter \fIrd\fP should be nonzero if recursion is desired, zero if not.

The query will be placed in an allocated buffer, a pointer to which will be
stored in the variable pointed to by \fIbuf\fP, and the length of which will
be stored in the variable pointed to by \fIbuflen\fP.

It is the caller's responsibility to free this buffer using
\fIares_free_string(3)\fP when it is no longer needed.  The parameter
\fImax_udp_size\fP should be nonzero to activate EDNS. Usage of
\fIares_create_query(3)\fP\ with \fImax_udp_size\fP set to zero is equivalent
to using \fIares_mkquery(3)\fP.
.SH RETURN VALUES
.B ares_create_query
can return any of the following values:
.TP 15
.B ARES_SUCCESS
Construction of the DNS query succeeded.
.TP 15
.B ARES_ENOTFOUND
The query name
.I name
refers to a
.I .onion
domain name. See RFC 7686.
.TP 15
.B ARES_EBADNAME
The query name
.I name
could not be encoded as a domain name, either because it contained a
zero-length label or because it contained a label of more than 63
characters.
.TP 15
.B ARES_ENOMEM
Memory was exhausted.
.SH AVAILABILITY
Added in c-ares 1.10.0
.SH SEE ALSO
.BR ares_dns_record (3),
.BR ares_expand_name (3),
.BR ares_free_string (3),
.BR ares_mkquery (3)
