<!DOCTYPE html>
<meta charset="utf-8">
<title>Encoding: ISO-2022-JP unencodable replacement in form submission</title>
<link rel="help" href="https://encoding.spec.whatwg.org/#iso-2022-jp-encoder">
<link rel="author" title="Benjamin C<PERSON> Sittler"
      href="mailto:<EMAIL>">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script>
'use strict';

promise_test(async testCase => {
  const target = Object.assign(document.createElement('iframe'), {
    name: 'target',
  });
  if (document.readyState !== 'complete') {
    await new Promise(resolve => addEventListener('load', resolve));
  }
  document.body.insertBefore(target, document.body.firstChild);
  const form = Object.assign(document.createElement('form'), {
    acceptCharset: 'iso-2022-jp',
    // NOTE: This uses escape and unescape rather than
    // encodeURI/encodeURIComponent and decodeURI/decodeURIComponent
    // intentionally, because:
    //
    // - escape() is required because encodeURI{,Component} encodes
    //   using UTF-8, and would falsely imply that non-ASCII
    //   characters are expected to work here -- which they won't, as
    //   the data URI has ISO-2022-JP charset; likewise
    //
    // - unescape() is required because the encoded byte sequence
    //   we're trying to decode and verify represents ISO-2022-JP data
    //   rather than the UTF-8 expected by decodeURI{,Component}
    //
    // The resulting document inside the IFRAME will look like:
    // <body onload="..."><plaintext>?utf16=...
    action: 'data:text/html;charset=iso-2022-jp,' + escape(
            '<body onload="(' +
            (() => parent.postMessage({
              utf16: document.body.innerText.split('=').pop(),
              iso2022jp: unescape(location.href.split('=').pop()),
            }, '*')) +
            ')()"><plaintext>'),
    target: target.name,
  });
  form.appendChild(Object.assign(document.createElement('input'), {
    name: 'utf16',
    value: 'ABC~¤•★星🌟星★•¤~XYZ',
  }));
  document.body.insertBefore(form, document.body.firstChild);
  const {iso2022jp, utf16} = await new Promise(resolve => {
    addEventListener('message', ({data}) => resolve(data));
    form.submit();
  });
  assert_equals(utf16, 'ABC~&#164;&#8226;★星&#127775;星★&#8226;&#164;~XYZ');
  assert_equals(
      iso2022jp,
      'ABC~&#164;&#8226;\x1b$B!z@1\x1b(B&#127775;\x1b$B@1!z\x1b(B&#8226;&#164;~XYZ');
}, 'Form submission using ISO-2022-JP correctly replaces unencodables');

</script>
