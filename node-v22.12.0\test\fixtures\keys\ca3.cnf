[ req ]
default_bits           = 2048
days                   = 9999
distinguished_name     = req_distinguished_name
attributes             = req_attributes
prompt                 = no
output_password        = password
x509_extensions        = v3_ca

[ req_distinguished_name ]
C                      = US
ST                     = CA
L                      = SF
O                      = Joyent
OU                     = Node.js
CN                     = ca3
emailAddress           = <EMAIL>

[ req_attributes ]
challengePassword              = A challenge password

[ v3_ca ]
basicConstraints = CA:TRUE
