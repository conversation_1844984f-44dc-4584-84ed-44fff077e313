// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

bitfield struct InterceptorInfoFlags extends uint31 {
  can_intercept_symbols: bool: 1 bit;
  non_masking: bool: 1 bit;
  named: bool: 1 bit;
  has_no_side_effect: bool: 1 bit;
  has_new_callbacks_signature: bool: 1 bit;
}

extern class InterceptorInfo extends Struct {
  getter: NonNullForeign|Zero|Undefined;
  setter: NonNullForeign|Zero|Undefined;
  query: NonNullForeign|Zero|Undefined;
  descriptor: NonNullForeign|Zero|Undefined;
  deleter: NonNullForeign|Zero|Undefined;
  enumerator: NonNullForeign|Zero|Undefined;
  definer: NonNullForeign|Zero|Undefined;
  data: Object;
  flags: SmiTagged<InterceptorInfoFlags>;
}

extern class AccessCheckInfo extends Struct {
  callback: Foreign|Zero|Undefined;
  named_interceptor: InterceptorInfo|Zero|Undefined;
  indexed_interceptor: InterceptorInfo|Zero|Undefined;
  data: Object;
}

type SideEffectType extends int32 constexpr 'SideEffectType';

bitfield struct AccessorInfoFlags extends uint32 {
  is_special_data_property: bool: 1 bit;
  is_sloppy: bool: 1 bit;
  replace_on_access: bool: 1 bit;
  getter_side_effect_type: SideEffectType: 2 bit;
  setter_side_effect_type: SideEffectType: 2 bit;
  initial_attributes: PropertyAttributes: 3 bit;
}

extern class AccessorInfo extends HeapObject {
  name: Name;
  data: Object;
  // For simulator builds this field contains the address of the trampoline
  // callable from generated code and for native builds - the address of
  // the getter C function.
  maybe_redirected_getter: ExternalPointer;
  setter: ExternalPointer;
  flags: AccessorInfoFlags;
  @if(TAGGED_SIZE_8_BYTES) optional_padding: uint32;
  @ifnot(TAGGED_SIZE_8_BYTES) optional_padding: void;
}
