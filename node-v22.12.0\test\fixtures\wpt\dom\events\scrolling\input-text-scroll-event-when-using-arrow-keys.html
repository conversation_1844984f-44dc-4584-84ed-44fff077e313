<!doctype html>
<html>
<head>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-actions.js"></script>
<script src="/resources/testdriver-vendor.js"></script>

</head>
<body onload=runTest()>
    <p>Moving the cursor using the arrow keys into an
        input element fires scroll events when text has to scroll into view.
        Uses arrow keys to move forward and backwards in the input
        element.</p>
    <input type="text" style='width: 50px'
    value="Fooooooooooooooooooooooooooooooooooooooooooooooooo"/>
    <textarea rows="4" cols="4">
        Fooooooooooooooooooooooooooooooooooooooooooooooooo
    </textarea>

    <script>
      async function moveCursorRightInsideElement(element, value){
          var arrowRight = '\uE014';
          for(var i=0;i<value;i++){
            await test_driver.send_keys(element, arrowRight);
          }
      }

      function runTest(){
          promise_test(async(t) => { return new Promise(async (resolve, reject) => {
            var input = document.getElementsByTagName('input')[0];
            function handleScroll(){
              resolve("Scroll Event successfully fired!");
            }
            input.addEventListener('scroll', handleScroll, false);
            // move cursor to the right until the text scrolls
            while(input.scrollLeft === 0){
              await moveCursorRightInsideElement(input, 1);
            }
            // if there is no scroll event fired then test will fail by timeout
          })},
             /*
               Moving the cursor using the arrow keys into an input element
               fires scroll events when text has to scroll into view.
               Uses arrow keys to move right in the input element.
             */
           "Scroll event fired for <input> element.");

          promise_test(async(t) => { return new Promise(async (resolve, reject) => {
            var textarea = document.getElementsByTagName('textarea')[0];
            function handleScroll(){
              resolve("Scroll Event successfully fired!");
            }
            textarea.addEventListener('scroll', handleScroll, false);
            // move cursor to the right until the text scrolls
            while(textarea.scrollLeft === 0){
              await moveCursorRightInsideElement(textarea, 1);
            }
            // if there is no scroll event fired then test will fail by timeout
          })},
             /*
              Moving the cursor using the arrow keys into a textarea element
              fires scroll events when text has to scroll into view.
              Uses arrow keys to move right in the textarea element.
            */
              "Scroll event fired for <textarea> element.");
      }
    </script>
</body>
</html>
