include_rules = [
  "-src",
  "-include/v8-debug.h",
  "+src/base/atomicops.h",
  "+src/base/compiler-specific.h",
  "+src/base/logging.h",
  "+src/base/macros.h",
  "+src/base/memory.h",
  "+src/base/optional.h",
  "+src/base/platform/mutex.h",
  "+src/base/platform/platform.h",
  "+src/base/platform/time.h",
  "+src/base/safe_conversions.h",
  "+src/base/template-utils.h",
  "+src/numbers/conversions.h",
  "+src/inspector",
  "+src/tracing",
  "+src/debug/debug-interface.h",
  "+src/debug/interface-types.h",
  "+src/base/vector.h",
  "+src/base/enum-set.h",
  "+src/utils/sha-256.h",
  "+third_party/inspector_protocol/crdtp",
  "+../../third_party/inspector_protocol/crdtp",
]
