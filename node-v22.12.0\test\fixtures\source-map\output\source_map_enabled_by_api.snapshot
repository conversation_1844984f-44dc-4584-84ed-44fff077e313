Error: an error!
    at functionD (*enclosing-call-site.js:16:17)
    at functionC (*enclosing-call-site.js:10:3)
    at functionB (*enclosing-call-site.js:6:3)
    at functionA (*enclosing-call-site.js:2:3)
    at Object.<anonymous> (*enclosing-call-site.js:24:3)
Error: an error!
    at functionD (*enclosing-call-site-min.js:1:156)
    at functionC (*enclosing-call-site-min.js:1:97)
    at functionB (*enclosing-call-site-min.js:1:60)
    at functionA (*enclosing-call-site-min.js:1:26)
    at Object.<anonymous> (*enclosing-call-site-min.js:1:199)
