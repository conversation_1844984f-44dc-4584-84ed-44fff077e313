<!DOCTYPE html>
<meta charset=utf-8>
<title>EventTarget.dispatchEvent: return value</title>
<link rel="help" href="https://dom.spec.whatwg.org/#concept-event-dispatch">
<link rel="help" href="https://dom.spec.whatwg.org/#dom-event-preventdefault">
<link rel="help" href="https://dom.spec.whatwg.org/#dom-event-returnvalue">
<link rel="help" href="https://dom.spec.whatwg.org/#dom-event-defaultprevented">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<div id=log></div>
<table id="table" border="1" style="display: none">
    <tbody id="table-body">
    <tr id="table-row">
        <td id="table-cell">Shady Grove</td>
        <td><PERSON>eolian</td>
    </tr>
    <tr id="parent">
        <td id="target">Over the river, Charlie</td>
        <td>Dorian</td>
    </tr>
    </tbody>
</table>
<script>
test(function() {
    var event_type = "foo";
    var target = document.getElementById("target");
    var parent = document.getElementById("parent");
    var default_prevented;
    var return_value;

    parent.addEventListener(event_type, function(e) {}, true);
    target.addEventListener(event_type, function(e) {
        evt.preventDefault();
        default_prevented = evt.defaultPrevented;
        return_value = evt.returnValue;
    }, true);
    target.addEventListener(event_type, function(e) {}, true);

    var evt = document.createEvent("Event");
    evt.initEvent(event_type, true, true);

    assert_true(parent.dispatchEvent(evt));
    assert_false(target.dispatchEvent(evt));
    assert_true(default_prevented);
    assert_false(return_value);
}, "Return value of EventTarget.dispatchEvent() affected by preventDefault().");

test(function() {
    var event_type = "foo";
    var target = document.getElementById("target");
    var parent = document.getElementById("parent");
    var default_prevented;
    var return_value;

    parent.addEventListener(event_type, function(e) {}, true);
    target.addEventListener(event_type, function(e) {
        evt.returnValue = false;
        default_prevented = evt.defaultPrevented;
        return_value = evt.returnValue;
    }, true);
    target.addEventListener(event_type, function(e) {}, true);

    var evt = document.createEvent("Event");
    evt.initEvent(event_type, true, true);

    assert_true(parent.dispatchEvent(evt));
    assert_false(target.dispatchEvent(evt));
    assert_true(default_prevented);
    assert_false(return_value);
}, "Return value of EventTarget.dispatchEvent() affected by returnValue.");
</script>
