// Copyright 2022 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// AlwaysSharedSpaceJSObject are JSObjects that must always be allocated in the
// shared space. Its instance type range is used to fast path the shared value
// barrier.
@abstract
extern class AlwaysSharedSpaceJSObject extends J<PERSON><PERSON> {}

extern class JSSharedStruct extends AlwaysSharedSpaceJSObject {}
