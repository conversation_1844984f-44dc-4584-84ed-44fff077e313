TAP version 13
# Subtest: sync pass todo
ok 1 - sync pass todo # TODO
  ---
  duration_ms: *
  ...
# Subtest: sync pass todo with message
ok 2 - sync pass todo with message # TODO this is a passing todo
  ---
  duration_ms: *
  ...
# Subtest: sync todo
not ok 3 - sync todo # TODO
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):4'
  failureType: 'testCodeFailure'
  error: 'should not count as a failure'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: sync todo with message
not ok 4 - sync todo with message # TODO this is a failing todo
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'should not count as a failure'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: sync skip pass
ok 5 - sync skip pass # SKIP
  ---
  duration_ms: *
  ...
# Subtest: sync skip pass with message
ok 6 - sync skip pass with message # SKIP this is skipped
  ---
  duration_ms: *
  ...
# Subtest: sync pass
ok 7 - sync pass
  ---
  duration_ms: *
  ...
# Subtest: sync throw fail
not ok 8 - sync throw fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'thrown from sync throw fail'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: async skip pass
ok 9 - async skip pass # SKIP
  ---
  duration_ms: *
  ...
# Subtest: async pass
ok 10 - async pass
  ---
  duration_ms: *
  ...
# Subtest: mixing describe/it and test should work
ok 11 - mixing describe/it and test should work
  ---
  duration_ms: *
  ...
# Subtest: async throw fail
not ok 12 - async throw fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'thrown from async throw fail'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: async skip fail
not ok 13 - async skip fail # SKIP
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'callbackAndPromisePresent'
  error: 'passed a callback but also returned a Promise'
  code: 'ERR_TEST_FAILURE'
  ...
# Subtest: async assertion fail
not ok 14 - async assertion fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: |-
    Expected values to be strictly equal:
    
    true !== false
    
  code: 'ERR_ASSERTION'
  name: 'AssertionError'
  expected: false
  actual: true
  operator: 'strictEqual'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: resolve pass
ok 15 - resolve pass
  ---
  duration_ms: *
  ...
# Subtest: reject fail
not ok 16 - reject fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'rejected from reject fail'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: unhandled rejection - passes but warns
ok 17 - unhandled rejection - passes but warns
  ---
  duration_ms: *
  ...
# Subtest: async unhandled rejection - passes but warns
ok 18 - async unhandled rejection - passes but warns
  ---
  duration_ms: *
  ...
# Subtest: immediate throw - passes but warns
ok 19 - immediate throw - passes but warns
  ---
  duration_ms: *
  ...
# Subtest: immediate reject - passes but warns
ok 20 - immediate reject - passes but warns
  ---
  duration_ms: *
  ...
# Subtest: immediate resolve pass
ok 21 - immediate resolve pass
  ---
  duration_ms: *
  ...
# Subtest: subtest sync throw fail
    # Subtest: +sync throw fail
    not ok 1 - +sync throw fail
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):3'
      failureType: 'testCodeFailure'
      error: 'thrown from subtest sync throw fail'
      code: 'ERR_TEST_FAILURE'
      stack: |-
        *
        *
        *
        *
        *
        *
        new Promise (<anonymous>)
        *
        *
        Array.map (<anonymous>)
      ...
    # Subtest: mixing describe/it and test should work
    ok 2 - mixing describe/it and test should work
      ---
      duration_ms: *
      ...
    1..2
not ok 22 - subtest sync throw fail
  ---
  duration_ms: *
  type: 'suite'
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'subtestsFailed'
  error: '1 subtest failed'
  code: 'ERR_TEST_FAILURE'
  ...
# Subtest: sync throw non-error fail
not ok 23 - sync throw non-error fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'Symbol(thrown symbol from sync throw non-error fail)'
  code: 'ERR_TEST_FAILURE'
  ...
# Subtest: level 0a
    # Subtest: level 1a
    ok 1 - level 1a
      ---
      duration_ms: *
      ...
    # Subtest: level 1b
    ok 2 - level 1b
      ---
      duration_ms: *
      ...
    # Subtest: level 1c
    ok 3 - level 1c
      ---
      duration_ms: *
      ...
    # Subtest: level 1d
    ok 4 - level 1d
      ---
      duration_ms: *
      ...
    1..4
ok 24 - level 0a
  ---
  duration_ms: *
  type: 'suite'
  ...
# Subtest: invalid subtest - pass but subtest fails
ok 25 - invalid subtest - pass but subtest fails
  ---
  duration_ms: *
  type: 'suite'
  ...
# Subtest: sync skip option
ok 26 - sync skip option # SKIP
  ---
  duration_ms: *
  ...
# Subtest: sync skip option with message
ok 27 - sync skip option with message # SKIP this is skipped
  ---
  duration_ms: *
  ...
# Subtest: sync skip option is false fail
not ok 28 - sync skip option is false fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'this should be executed'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: <anonymous>
ok 29 - <anonymous>
  ---
  duration_ms: *
  ...
# Subtest: functionOnly
ok 30 - functionOnly
  ---
  duration_ms: *
  ...
# Subtest: <anonymous>
ok 31 - <anonymous>
  ---
  duration_ms: *
  ...
# Subtest: test with only a name provided
ok 32 - test with only a name provided
  ---
  duration_ms: *
  ...
# Subtest: <anonymous>
ok 33 - <anonymous>
  ---
  duration_ms: *
  ...
# Subtest: <anonymous>
ok 34 - <anonymous> # SKIP
  ---
  duration_ms: *
  ...
# Subtest: test with a name and options provided
ok 35 - test with a name and options provided # SKIP
  ---
  duration_ms: *
  ...
# Subtest: functionAndOptions
ok 36 - functionAndOptions # SKIP
  ---
  duration_ms: *
  ...
# Subtest: callback pass
ok 37 - callback pass
  ---
  duration_ms: *
  ...
# Subtest: callback fail
not ok 38 - callback fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'callback failure'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
  ...
# Subtest: sync t is this in test
ok 39 - sync t is this in test
  ---
  duration_ms: *
  ...
# Subtest: async t is this in test
ok 40 - async t is this in test
  ---
  duration_ms: *
  ...
# Subtest: callback t is this in test
ok 41 - callback t is this in test
  ---
  duration_ms: *
  ...
# Subtest: callback also returns a Promise
not ok 42 - callback also returns a Promise
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'callbackAndPromisePresent'
  error: 'passed a callback but also returned a Promise'
  code: 'ERR_TEST_FAILURE'
  ...
# Subtest: callback throw
not ok 43 - callback throw
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'thrown from callback throw'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: callback called twice
not ok 44 - callback called twice
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'multipleCallbackInvocations'
  error: 'callback invoked multiple times'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
  ...
# Subtest: callback called twice in different ticks
ok 45 - callback called twice in different ticks
  ---
  duration_ms: *
  ...
# Subtest: callback called twice in future tick
not ok 46 - callback called twice in future tick
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'uncaughtException'
  error: 'callback invoked multiple times'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
  ...
# Subtest: callback async throw
not ok 47 - callback async throw
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'uncaughtException'
  error: 'thrown from callback async throw'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
  ...
# Subtest: callback async throw after done
ok 48 - callback async throw after done
  ---
  duration_ms: *
  ...
# Subtest: custom inspect symbol fail
not ok 49 - custom inspect symbol fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'customized'
  code: 'ERR_TEST_FAILURE'
  ...
# Subtest: custom inspect symbol that throws fail
not ok 50 - custom inspect symbol that throws fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: |-
    {
      foo: 1,
      [Symbol(nodejs.util.inspect.custom)]: [Function: [nodejs.util.inspect.custom]]
    }
  code: 'ERR_TEST_FAILURE'
  ...
# Subtest: subtest sync throw fails
    # Subtest: sync throw fails at first
    not ok 1 - sync throw fails at first
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):3'
      failureType: 'testCodeFailure'
      error: 'thrown from subtest sync throw fails at first'
      code: 'ERR_TEST_FAILURE'
      stack: |-
        *
        *
        *
        *
        *
        *
        new Promise (<anonymous>)
        *
        *
        Array.map (<anonymous>)
      ...
    # Subtest: sync throw fails at second
    not ok 2 - sync throw fails at second
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):3'
      failureType: 'testCodeFailure'
      error: 'thrown from subtest sync throw fails at second'
      code: 'ERR_TEST_FAILURE'
      stack: |-
        *
        *
        *
        *
        *
        *
        async Promise.all (index 0)
        *
        *
      ...
    1..2
not ok 51 - subtest sync throw fails
  ---
  duration_ms: *
  type: 'suite'
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'subtestsFailed'
  error: '2 subtests failed'
  code: 'ERR_TEST_FAILURE'
  ...
# Subtest: describe sync throw fails
    # Subtest: should not run
    not ok 1 - should not run
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):3'
      failureType: 'cancelledByParent'
      error: 'test did not finish before its parent and was cancelled'
      code: 'ERR_TEST_FAILURE'
      ...
    1..1
not ok 52 - describe sync throw fails
  ---
  duration_ms: *
  type: 'suite'
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'thrown from describe'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: describe async throw fails
    # Subtest: should not run
    not ok 1 - should not run
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):3'
      failureType: 'cancelledByParent'
      error: 'test did not finish before its parent and was cancelled'
      code: 'ERR_TEST_FAILURE'
      ...
    1..1
not ok 53 - describe async throw fails
  ---
  duration_ms: *
  type: 'suite'
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'thrown from describe'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: timeouts
    # Subtest: timed out async test
    not ok 1 - timed out async test
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):3'
      failureType: 'testTimeoutFailure'
      error: 'test timed out after 5ms'
      code: 'ERR_TEST_FAILURE'
      stack: |-
        async Promise.all (index 0)
      ...
    # Subtest: timed out callback test
    not ok 2 - timed out callback test
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):3'
      failureType: 'testTimeoutFailure'
      error: 'test timed out after 5ms'
      code: 'ERR_TEST_FAILURE'
      ...
    # Subtest: large timeout async test is ok
    ok 3 - large timeout async test is ok
      ---
      duration_ms: *
      ...
    # Subtest: large timeout callback test is ok
    ok 4 - large timeout callback test is ok
      ---
      duration_ms: *
      ...
    1..4
not ok 54 - timeouts
  ---
  duration_ms: *
  type: 'suite'
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'subtestsFailed'
  error: '2 subtests failed'
  code: 'ERR_TEST_FAILURE'
  ...
# Subtest: successful thenable
    # Subtest: successful thenable
    ok 1 - successful thenable
      ---
      duration_ms: *
      ...
    # Subtest: rejected thenable
    not ok 2 - rejected thenable
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):3'
      failureType: 'testCodeFailure'
      error: 'custom error'
      code: 'ERR_TEST_FAILURE'
      stack: |-
        *
      ...
    1..2
not ok 55 - successful thenable
  ---
  duration_ms: *
  type: 'suite'
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'subtestsFailed'
  error: '1 subtest failed'
  code: 'ERR_TEST_FAILURE'
  ...
# Subtest: rejected thenable
not ok 56 - rejected thenable
  ---
  duration_ms: *
  type: 'suite'
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):1'
  failureType: 'testCodeFailure'
  error: 'custom error'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
  ...
# Subtest: async describe function
    # Subtest: it inside describe 1
    ok 1 - it inside describe 1
      ---
      duration_ms: *
      ...
    # Subtest: it inside describe 2
    ok 2 - it inside describe 2
      ---
      duration_ms: *
      ...
    # Subtest: inner describe
        # Subtest: it inside inner describe
        ok 1 - it inside inner describe
          ---
          duration_ms: *
          ...
        1..1
    ok 3 - inner describe
      ---
      duration_ms: *
      type: 'suite'
      ...
    1..3
ok 57 - async describe function
  ---
  duration_ms: *
  type: 'suite'
  ...
# Subtest: invalid subtest fail
not ok 58 - invalid subtest fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/describe_it.js:(LINE):5'
  failureType: 'parentAlreadyFinished'
  error: 'test could not be started because its parent finished'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
  ...
1..58
# Error: Test "unhandled rejection - passes but warns" at test/fixtures/test-runner/output/describe_it.js:(LINE):1 generated asynchronous activity after the test ended. This activity created the error "Error: rejected from unhandled rejection fail" and would have caused the test to fail, but instead triggered an unhandledRejection event.
# Error: Test "async unhandled rejection - passes but warns" at test/fixtures/test-runner/output/describe_it.js:(LINE):1 generated asynchronous activity after the test ended. This activity created the error "Error: rejected from async unhandled rejection fail" and would have caused the test to fail, but instead triggered an unhandledRejection event.
# Error: Test "immediate throw - passes but warns" at test/fixtures/test-runner/output/describe_it.js:(LINE):1 generated asynchronous activity after the test ended. This activity created the error "Error: thrown from immediate throw fail" and would have caused the test to fail, but instead triggered an uncaughtException event.
# Error: Test "immediate reject - passes but warns" at test/fixtures/test-runner/output/describe_it.js:(LINE):1 generated asynchronous activity after the test ended. This activity created the error "Error: rejected from immediate reject fail" and would have caused the test to fail, but instead triggered an unhandledRejection event.
# Error: Test "callback called twice in different ticks" at test/fixtures/test-runner/output/describe_it.js:(LINE):1 generated asynchronous activity after the test ended. This activity created the error "Error [ERR_TEST_FAILURE]: callback invoked multiple times" and would have caused the test to fail, but instead triggered an uncaughtException event.
# Error: Test "callback async throw after done" at test/fixtures/test-runner/output/describe_it.js:(LINE):1 generated asynchronous activity after the test ended. This activity created the error "Error: thrown from callback async throw after done" and would have caused the test to fail, but instead triggered an uncaughtException event.
# tests 67
# suites 11
# pass 31
# fail 19
# cancelled 4
# skipped 9
# todo 4
# duration_ms *
