// Copyright 2022 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/objects/js-duration-format.h'

type JSDurationFormatStyle extends int32
    constexpr 'JSDurationFormat::Style';
type JSDurationFormatSeparator extends int32
    constexpr 'JSDurationFormat::Separator';
type JSDurationFormatFieldStyle extends int32
    constexpr 'JSDurationFormat::FieldStyle';
type JSDurationFormatDisplay extends int32
    constexpr 'JSDurationFormat::Display';
bitfield struct JSDurationFormatStyleFlags extends uint31 {
  style: JSDurationFormatStyle: 2 bit;
  years_style: JSDurationFormatFieldStyle: 2 bit;
  months_style: JSDurationFormatFieldStyle: 2 bit;
  weeks_style: JSDurationFormatFieldStyle: 2 bit;
  days_style: JSDurationFormatFieldStyle: 2 bit;
  hours_style: JSDurationFormatFieldStyle: 3 bit;
  minutes_style: JSDurationFormatFieldStyle: 3 bit;
  seconds_style: JSDurationFormatFieldStyle: 3 bit;
  milliseconds_style: JSDurationFormatFieldStyle: 2 bit;
  microseconds_style: JSDurationFormatFieldStyle: 2 bit;
  nanoseconds_style: JSDurationFormatFieldStyle: 2 bit;
  separator: JSDurationFormatSeparator: 2 bit;
}
bitfield struct JSDurationFormatDisplayFlags extends uint31 {
  years_display: JSDurationFormatDisplay: 1 bit;
  months_display: JSDurationFormatDisplay: 1 bit;
  weeks_display: JSDurationFormatDisplay: 1 bit;
  days_display: JSDurationFormatDisplay: 1 bit;
  hours_display: JSDurationFormatDisplay: 1 bit;
  minutes_display: JSDurationFormatDisplay: 1 bit;
  seconds_display: JSDurationFormatDisplay: 1 bit;
  milliseconds_display: JSDurationFormatDisplay: 1 bit;
  microseconds_display: JSDurationFormatDisplay: 1 bit;
  nanoseconds_display: JSDurationFormatDisplay: 1 bit;
  fractional_digits: int32: 4 bit;
}

extern class JSDurationFormat extends JSObject {
  style_flags: SmiTagged<JSDurationFormatStyleFlags>;
  display_flags: SmiTagged<JSDurationFormatDisplayFlags>;
  icu_locale: Foreign;  // Managed<icu::Locale>
  icu_number_formatter:
      Foreign;  // Managed<icu::number::LocalizedNumberFormatter>
}
