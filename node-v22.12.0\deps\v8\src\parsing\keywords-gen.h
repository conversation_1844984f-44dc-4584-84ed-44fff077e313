// Copyright 2018 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file is automatically generated by gen-keywords-gen-h.py and should not
// be modified manually.

#ifndef V8_PARSING_KEYWORDS_GEN_H_
#define V8_PARSING_KEYWORDS_GEN_H_

#include "src/parsing/token.h"

namespace v8 {
namespace internal {

/* C++ code produced by gperf version 3.1 */
/* Command-line: gperf -m100 src/parsing/keywords.txt  */
/* Computed positions: -k'1-2' */

#if !(                                                                         \
    (' ' == 32) && ('!' == 33) && ('"' == 34) && ('#' == 35) && ('%' == 37) && \
    ('&' == 38) && ('\'' == 39) && ('(' == 40) && (')' == 41) &&               \
    ('*' == 42) && ('+' == 43) && (',' == 44) && ('-' == 45) && ('.' == 46) && \
    ('/' == 47) && ('0' == 48) && ('1' == 49) && ('2' == 50) && ('3' == 51) && \
    ('4' == 52) && ('5' == 53) && ('6' == 54) && ('7' == 55) && ('8' == 56) && \
    ('9' == 57) && (':' == 58) && (';' == 59) && ('<' == 60) && ('=' == 61) && \
    ('>' == 62) && ('?' == 63) && ('A' == 65) && ('B' == 66) && ('C' == 67) && \
    ('D' == 68) && ('E' == 69) && ('F' == 70) && ('G' == 71) && ('H' == 72) && \
    ('I' == 73) && ('J' == 74) && ('K' == 75) && ('L' == 76) && ('M' == 77) && \
    ('N' == 78) && ('O' == 79) && ('P' == 80) && ('Q' == 81) && ('R' == 82) && \
    ('S' == 83) && ('T' == 84) && ('U' == 85) && ('V' == 86) && ('W' == 87) && \
    ('X' == 88) && ('Y' == 89) && ('Z' == 90) && ('[' == 91) &&                \
    ('\\' == 92) && (']' == 93) && ('^' == 94) && ('_' == 95) &&               \
    ('a' == 97) && ('b' == 98) && ('c' == 99) && ('d' == 100) &&               \
    ('e' == 101) && ('f' == 102) && ('g' == 103) && ('h' == 104) &&            \
    ('i' == 105) && ('j' == 106) && ('k' == 107) && ('l' == 108) &&            \
    ('m' == 109) && ('n' == 110) && ('o' == 111) && ('p' == 112) &&            \
    ('q' == 113) && ('r' == 114) && ('s' == 115) && ('t' == 116) &&            \
    ('u' == 117) && ('v' == 118) && ('w' == 119) && ('x' == 120) &&            \
    ('y' == 121) && ('z' == 122) && ('{' == 123) && ('|' == 124) &&            \
    ('}' == 125) && ('~' == 126))
/* The character set is not based on ISO-646.  */
#error "gperf generated tables don't work with this execution character set."
// If you see this error, please report a bug to <<EMAIL>>.
#endif

struct PerfectKeywordHashTableEntry {
  const char* name;
  Token::Value value;
};
enum {
  TOTAL_KEYWORDS = 51,
  MIN_WORD_LENGTH = 2,
  MAX_WORD_LENGTH = 10,
  MIN_HASH_VALUE = 3,
  MAX_HASH_VALUE = 75
};

/* maximum key range = 73, duplicates = 0 */

class PerfectKeywordHash {
 private:
  static inline unsigned int Hash(const char* str, int len);

 public:
  static inline Token::Value GetToken(const char* str, int len);
};

inline unsigned int PerfectKeywordHash::Hash(const char* str, int len) {
  DCHECK_LT(str[0], 128);
  static const unsigned char asso_values[128] = {
      76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76,
      76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76,
      76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76,
      76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76,
      76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76,
      76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76,
      76, 43, 0,  28, 23, 1,  0,  33, 76, 14, 20, 76, 0,  42, 20, 19,
      1,  76, 0,  10, 3,  37, 4,  22, 9,  31, 1,  76, 76, 76, 76, 76};
  return len + asso_values[static_cast<unsigned char>(str[1] + 1)] +
         asso_values[static_cast<unsigned char>(str[0])];
}

static const unsigned char kPerfectKeywordLengthTable[128] = {
    0,  0, 0, 3, 3, 5, 6, 3, 7, 4, 6, 6, 8, 3, 0, 5, 3, 4, 7, 5, 9, 4,
    5,  3, 4, 6, 2, 7, 4, 6, 7, 8, 4, 5, 5, 2, 3, 8, 6, 7, 6, 5, 9, 10,
    10, 5, 4, 4, 0, 2, 0, 5, 0, 6, 2, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0,
    0,  0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0,  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0,  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

static const struct PerfectKeywordHashTableEntry kPerfectKeywordHashTable[128] =
    {{"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"let", Token::kLet},
     {"for", Token::kFor},
     {"false", Token::kFalseLiteral},
     {"return", Token::kReturn},
     {"var", Token::kVar},
     {"package", Token::kFutureStrictReservedWord},
     {"void", Token::kVoid},
     {"typeof", Token::kTypeOf},
     {"public", Token::kFutureStrictReservedWord},
     {"function", Token::kFunction},
     {"set", Token::kSet},
     {"", Token::kIdentifier},
     {"break", Token::kBreak},
     {"try", Token::kTry},
     {"true", Token::kTrueLiteral},
     {"private", Token::kFutureStrictReservedWord},
     {"super", Token::kSuper},
     {"protected", Token::kFutureStrictReservedWord},
     {"this", Token::kThis},
     {"throw", Token::kThrow},
     {"new", Token::kNew},
     {"enum", Token::kEnum},
     {"switch", Token::kSwitch},
     {"do", Token::kDo},
     {"finally", Token::kFinally},
     {"null", Token::kNullLiteral},
     {"delete", Token::kDelete},
     {"default", Token::kDefault},
     {"debugger", Token::kDebugger},
     {"case", Token::kCase},
     {"catch", Token::kCatch},
     {"const", Token::kConst},
     {"in", Token::kIn},
     {"get", Token::kGet},
     {"continue", Token::kContinue},
     {"export", Token::kExport},
     {"extends", Token::kExtends},
     {"import", Token::kImport},
     {"while", Token::kWhile},
     {"interface", Token::kFutureStrictReservedWord},
     {"instanceof", Token::kInstanceOf},
     {"implements", Token::kFutureStrictReservedWord},
     {"using", Token::kUsing},
     {"with", Token::kWith},
     {"else", Token::kElse},
     {"", Token::kIdentifier},
     {"if", Token::kIf},
     {"", Token::kIdentifier},
     {"async", Token::kAsync},
     {"", Token::kIdentifier},
     {"static", Token::kStatic},
     {"of", Token::kOf},
     {"", Token::kIdentifier},
     {"yield", Token::kYield},
     {"await", Token::kAwait},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"class", Token::kClass},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier},
     {"", Token::kIdentifier}};

inline Token::Value PerfectKeywordHash::GetToken(const char* str, int len) {
  if (base::IsInRange(len, MIN_WORD_LENGTH, MAX_WORD_LENGTH)) {
    unsigned int key = Hash(str, len) & 0x7f;

    DCHECK_LT(key, arraysize(kPerfectKeywordLengthTable));
    DCHECK_LT(key, arraysize(kPerfectKeywordHashTable));
    if (len == kPerfectKeywordLengthTable[key]) {
      const char* s = kPerfectKeywordHashTable[key].name;

      while (*s != 0) {
        if (*s++ != *str++) return Token::kIdentifier;
      }
      return kPerfectKeywordHashTable[key].value;
    }
  }
  return Token::kIdentifier;
}

}  // namespace internal
}  // namespace v8

#endif  // V8_PARSING_KEYWORDS_GEN_H_
