<!DOCTYPE html>
<meta charset="utf-8">
<link rel="author" title="Joey Arhar" href="mailto:<EMAIL>">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<body>
<script>
test(t => {
  const element = document.createElement('div');

  element.addEventListener('click', () => {
    event.stopPropagation();
  }, { capture: true });

  element.addEventListener('click',
    t.unreached_func('stopPropagation in the capture handler should have canceled this bubble handler.'));

  element.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));
});
</script>
