// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/objects/ordered-hash-table.h'

// Using int as a dummy type-parameter to get access to these constants which
// don't actually depend on the derived class. This avoids accidentially
// depending on something from a concrete derived class.
const kSmallOrderedHashTableMaxCapacity: constexpr int31
    generates 'SmallOrderedHashTable<int>::kMaxCapacity';
const kSmallOrderedHashTableNotFound: constexpr int31
    generates 'SmallOrderedHashTable<int>::kNotFound';
const kSmallOrderedHashTableLoadFactor: constexpr int31
    generates 'SmallOrderedHashTable<int>::kLoadFactor';

@abstract
@doNotGenerateCppClass
extern class SmallOrderedHashTable extends HeapObject
    generates 'TNode<HeapObject>' {}

extern macro SmallOrderedHashSetMapConstant(): Map;
const kSmallOrderedHashSetMap: Map = SmallOrderedHashSetMapConstant();

@doNotGenerateCppClass
extern class SmallOrderedHashSet extends SmallOrderedHashTable {
  number_of_elements: uint8;
  number_of_deleted_elements: uint8;
  const number_of_buckets: uint8;
  @if(TAGGED_SIZE_8_BYTES) padding[5]: uint8;
  @ifnot(TAGGED_SIZE_8_BYTES) padding[1]: uint8;
  data_table[Convert<intptr>(number_of_buckets) * kSmallOrderedHashTableLoadFactor]:
      JSAny|TheHole;
  hash_table[number_of_buckets]: uint8;
  chain_table[Convert<intptr>(number_of_buckets) * kSmallOrderedHashTableLoadFactor]:
      uint8;
}

@export
macro AllocateSmallOrderedHashSet(capacity: intptr): SmallOrderedHashSet {
  const hashTableSize = capacity / kSmallOrderedHashTableLoadFactor;
  dcheck(
      0 <= hashTableSize && hashTableSize <= kSmallOrderedHashTableMaxCapacity);
  return new SmallOrderedHashSet{
    map: kSmallOrderedHashSetMap,
    number_of_elements: 0,
    number_of_deleted_elements: 0,
    number_of_buckets: (Convert<uint8>(hashTableSize)),
    padding: ...ConstantIterator<uint8>(0),
    data_table: ...ConstantIterator(TheHole),
    hash_table: ...ConstantIterator<uint8>(kSmallOrderedHashTableNotFound),
    chain_table: ...ConstantIterator<uint8>(kSmallOrderedHashTableNotFound)
  };
}

struct HashMapEntry {
  key: JSAny|TheHole;
  value: JSAny|TheHole;
}

extern macro SmallOrderedHashMapMapConstant(): Map;
const kSmallOrderedHashMapMap: Map = SmallOrderedHashMapMapConstant();

@doNotGenerateCppClass
extern class SmallOrderedHashMap extends SmallOrderedHashTable {
  number_of_elements: uint8;
  number_of_deleted_elements: uint8;
  const number_of_buckets: uint8;
  @if(TAGGED_SIZE_8_BYTES) padding[5]: uint8;
  @ifnot(TAGGED_SIZE_8_BYTES) padding[1]: uint8;
  data_table[Convert<intptr>(number_of_buckets) * kSmallOrderedHashTableLoadFactor]:
      HashMapEntry;
  hash_table[number_of_buckets]: uint8;
  chain_table[Convert<intptr>(number_of_buckets) * kSmallOrderedHashTableLoadFactor]:
      uint8;
}

@export
macro AllocateSmallOrderedHashMap(capacity: intptr): SmallOrderedHashMap {
  const hashTableSize = capacity / kSmallOrderedHashTableLoadFactor;
  dcheck(
      0 <= hashTableSize && hashTableSize <= kSmallOrderedHashTableMaxCapacity);
  return new SmallOrderedHashMap{
    map: kSmallOrderedHashMapMap,
    number_of_elements: 0,
    number_of_deleted_elements: 0,
    number_of_buckets: (Convert<uint8>(hashTableSize)),
    padding: ...ConstantIterator<uint8>(0),
    data_table: ...ConstantIterator(HashMapEntry{key: TheHole, value: TheHole}),
    hash_table: ...ConstantIterator<uint8>(kSmallOrderedHashTableNotFound),
    chain_table: ...ConstantIterator<uint8>(kSmallOrderedHashTableNotFound)
  };
}

struct NameDictionaryEntry {
  key: JSAny|TheHole;
  value: JSAny|TheHole;
  property_details: Smi|TheHole;
}

@doNotGenerateCppClass
extern class SmallOrderedNameDictionary extends SmallOrderedHashTable {
  hash: int32;
  @if(TAGGED_SIZE_8_BYTES) padding_0: int32;
  @ifnot(TAGGED_SIZE_8_BYTES) padding_0: void;
  number_of_elements: uint8;
  number_of_deleted_elements: uint8;
  const number_of_buckets: uint8;
  @if(TAGGED_SIZE_8_BYTES) padding_1[5]: uint8;
  @ifnot(TAGGED_SIZE_8_BYTES) padding_1[1]: uint8;
  data_table[Convert<intptr>(number_of_buckets) * kSmallOrderedHashTableLoadFactor]:
      NameDictionaryEntry;
  hash_table[number_of_buckets]: uint8;
  chain_table[number_of_buckets]: uint8;
}
