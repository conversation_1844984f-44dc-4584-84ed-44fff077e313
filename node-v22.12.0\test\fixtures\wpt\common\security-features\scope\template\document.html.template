<!DOCTYPE html>
<html>
  <head>
    %(meta)s
    <script src="/common/security-features/resources/common.sub.js"></script>
    <script>
    // Receive a message from the parent and start the test.
    function onMessageFromParent(event) {
      // Because this window might receive messages from child iframe during
      // tests, we first remove the listener here before staring the test.
      window.removeEventListener('message', onMessageFromParent);

      const configurationError = "%(error)s";
      if (configurationError.length > 0) {
        parent.postMessage({error: configurationError}, "*");
        return;
      }

      invokeRequest(event.data.subresource,
                    event.data.sourceContextList)
        .then(result => parent.postMessage(result, "*"))
        .catch(e => {
            const message = (e.error && e.error.stack) || e.message || "Error";
            parent.postMessage({error: message}, "*");
          });
    }
    window.addEventListener('message', onMessageFromParent);
    </script>
  </head>
</html>
