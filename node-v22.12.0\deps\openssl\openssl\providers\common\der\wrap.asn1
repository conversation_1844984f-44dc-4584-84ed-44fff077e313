-- Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
--
-- Licensed under the Apache License 2.0 (the "License").  You may not use
-- this file except in compliance with the License.  You can obtain a copy
-- in the file LICENSE in the source distribution or at
-- https://www.openssl.org/source/license.html

-- -------------------------------------------------------------------
-- Taken from RFC 3370, Section 4.3.1 Triple-DES Key Wrap
-- (https://tools.ietf.org/html/rfc3370)

id-alg-CMS3DESwrap OBJECT IDENTIFIER ::= {
    iso(1) member-body(2) us(840) rsadsi(113549) pkcs(1) pkcs-9(9) smime(16) alg(3) 6
}

-- -------------------------------------------------------------------
-- Taken from RFC 3394, Section 3. Object Identifiers
-- (https://tools.ietf.org/html/rfc3565)

aes  OBJECT IDENTIFIER  ::=  {
    joint-iso-itu-t(2) country(16) us(840) organization(1) gov(101) csor(3) nistAlgorithm(4) 1
}

id-aes128-wrap OBJECT IDENTIFIER ::= { aes 5 }
id-aes192-wrap OBJECT IDENTIFIER ::= { aes 25 }
id-aes256-wrap OBJECT IDENTIFIER ::= { aes 45 }
