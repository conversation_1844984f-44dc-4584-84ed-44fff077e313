<!DOCTYPE html>
<html>
<head>
<meta charset="windows-31j"> <!-- test breaks if the server overrides this -->
<title>windows-31j encoding (form)</title>
<meta name="timeout" content="long">
<meta name="variant" content="?1-1000">
<meta name="variant" content="?1001-2000">
<meta name="variant" content="?2001-3000">
<meta name="variant" content="?3001-4000">
<meta name="variant" content="?4001-5000">
<meta name="variant" content="?5001-6000">
<meta name="variant" content="?6001-7000">
<meta name="variant" content="?7001-last">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/common/subset-tests.js"></script>
<script src="jis0208_index.js"></script>
<script src="sjis-encoder.js"></script>
<link rel="author" title="<PERSON> Is<PERSON>" href="mailto:<EMAIL>">
<link rel="help" href="https://encoding.spec.whatwg.org/#shift_jis">
<meta name="assert" content="The browser produces the same encoding behavior for a document labeled 'windows-31j' as for a document labeled 'shift_jis'.">
<style>
 iframe { display:none }
 form { display:none }
</style>
</head>
<body>
<div id="log"></div>
<script src="../../resources/ranges.js"></script>
<script>
var errors = false;
var encoder = sjisEncoder;
var ranges = rangesAll;
var separator = ",";
function expect(result, codepoint) {
  return "%" + result.replace(/ /g, "%");
}
</script>
<script src="../../resources/encode-form-common.js"></script>
</body>
</html>
