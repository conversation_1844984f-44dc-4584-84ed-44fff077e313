<!doctype html>
<meta charset=utf-8>
<title></title>
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<iframe src="{{location[scheme]}}://{{domains[www1]}}:{{ports[http][0]}}{{location[path]}}/../EventListener-incumbent-global-subframe-1.sub.html"></iframe>
<script>

var t = async_test("Check the incumbent global EventListeners  are called with");

onload = t.step_func(function() {
  onmessage = t.step_func_done(function(e) {
    var d = e.data;
    assert_equals(d.actual, d.expected, d.reason);
  });

  frames[0].postMessage("start", "*");
});

</script>
