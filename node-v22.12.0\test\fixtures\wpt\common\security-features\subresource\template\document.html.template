<!DOCTYPE html>
<html>
  <head>
    <title>This page reports back it's request details to the parent frame</title>
  </head>
  <body>
    <script>
      var result = {
        location: document.location.toString(),
        referrer: document.referrer.length > 0 ? document.referrer : undefined,
        headers: %(headers)s
      };
      parent.postMessage(result, "*");
    </script>
  </body>
</html>
