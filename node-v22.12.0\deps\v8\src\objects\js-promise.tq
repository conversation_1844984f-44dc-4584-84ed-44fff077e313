// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

bitfield struct JSPromiseFlags extends uint31 {
  status: PromiseState: 2 bit;
  has_handler: bool: 1 bit;
  handled_hint: bool: 1 bit;
  is_silent: bool: 1 bit;
  async_task_id: int32: 22 bit;
}

extern class JSPromise extends JSObjectWithEmbedderSlots {
  macro Status(): PromiseState {
    return this.flags.status;
  }

  macro SetStatus(status: constexpr PromiseState): void {
    dcheck(this.Status() == PromiseState::kPending);
    dcheck(status != PromiseState::kPending);

    this.flags.status = status;
  }

  macro HasHandler(): bool {
    return this.flags.has_handler;
  }

  macro SetHasHandler(): void {
    this.flags.has_handler = true;
  }

  // Smi 0 terminated list of PromiseReaction objects in case the JSPromise was
  // not settled yet, otherwise the result.
  reactions_or_result: Zero|PromiseReaction|JSAny;
  flags: SmiTagged<JSPromiseFlags>;
}

@doNotGenerateCast
extern class J<PERSON>romiseConstructor extends JSFunction
    generates 'TNode<JSFunction>';
