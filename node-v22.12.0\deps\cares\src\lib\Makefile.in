# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

# aminclude_static.am generated automatically by Autoconf
# from AX_AM_MACROS_STATIC on Sat Nov  9 17:40:37 UTC 2024

# Copyright (C) The c-ares project and its contributors
# SPDX-License-Identifier: MIT

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
@CARES_USE_NO_UNDEFINED_TRUE@am__append_1 = -no-undefined
@CARES_SYMBOL_HIDING_TRUE@am__append_2 = @CARES_SYMBOL_HIDING_CFLAG@
@CARES_SYMBOL_HIDING_TRUE@am__append_3 = -DCARES_SYMBOL_HIDING
subdir = src/lib
SUBDIRS =
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_ac_append_to_file.m4 \
	$(top_srcdir)/m4/ax_ac_print_to_file.m4 \
	$(top_srcdir)/m4/ax_add_am_macro_static.m4 \
	$(top_srcdir)/m4/ax_am_macros_static.m4 \
	$(top_srcdir)/m4/ax_append_compile_flags.m4 \
	$(top_srcdir)/m4/ax_append_flag.m4 \
	$(top_srcdir)/m4/ax_append_link_flags.m4 \
	$(top_srcdir)/m4/ax_check_compile_flag.m4 \
	$(top_srcdir)/m4/ax_check_gnu_make.m4 \
	$(top_srcdir)/m4/ax_check_link_flag.m4 \
	$(top_srcdir)/m4/ax_check_user_namespace.m4 \
	$(top_srcdir)/m4/ax_check_uts_namespace.m4 \
	$(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/ax_compiler_vendor.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx_14.m4 \
	$(top_srcdir)/m4/ax_file_escapes.m4 \
	$(top_srcdir)/m4/ax_pthread.m4 \
	$(top_srcdir)/m4/ax_require_defined.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/m4/pkg.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = ares_config.h $(top_builddir)/include/ares_build.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)"
LTLIBRARIES = $(lib_LTLIBRARIES)
libcares_la_LIBADD =
am__dirstamp = $(am__leading_dot)dirstamp
am__objects_1 = libcares_la-ares_addrinfo2hostent.lo \
	libcares_la-ares_addrinfo_localhost.lo \
	libcares_la-ares_android.lo libcares_la-ares_cancel.lo \
	libcares_la-ares_close_sockets.lo libcares_la-ares_conn.lo \
	libcares_la-ares_cookie.lo libcares_la-ares_data.lo \
	libcares_la-ares_destroy.lo libcares_la-ares_free_hostent.lo \
	libcares_la-ares_free_string.lo \
	libcares_la-ares_freeaddrinfo.lo \
	libcares_la-ares_getaddrinfo.lo libcares_la-ares_getenv.lo \
	libcares_la-ares_gethostbyaddr.lo \
	libcares_la-ares_gethostbyname.lo \
	libcares_la-ares_getnameinfo.lo libcares_la-ares_hosts_file.lo \
	libcares_la-ares_init.lo libcares_la-ares_library_init.lo \
	libcares_la-ares_metrics.lo libcares_la-ares_options.lo \
	libcares_la-ares_parse_into_addrinfo.lo \
	libcares_la-ares_process.lo libcares_la-ares_qcache.lo \
	libcares_la-ares_query.lo libcares_la-ares_search.lo \
	libcares_la-ares_send.lo \
	libcares_la-ares_set_socket_functions.lo \
	libcares_la-ares_socket.lo libcares_la-ares_sortaddrinfo.lo \
	libcares_la-ares_strerror.lo libcares_la-ares_sysconfig.lo \
	libcares_la-ares_sysconfig_files.lo \
	libcares_la-ares_sysconfig_mac.lo \
	libcares_la-ares_sysconfig_win.lo libcares_la-ares_timeout.lo \
	libcares_la-ares_update_servers.lo libcares_la-ares_version.lo \
	libcares_la-inet_net_pton.lo libcares_la-inet_ntop.lo \
	libcares_la-windows_port.lo dsa/libcares_la-ares_array.lo \
	dsa/libcares_la-ares_htable.lo \
	dsa/libcares_la-ares_htable_asvp.lo \
	dsa/libcares_la-ares_htable_dict.lo \
	dsa/libcares_la-ares_htable_strvp.lo \
	dsa/libcares_la-ares_htable_szvp.lo \
	dsa/libcares_la-ares_htable_vpstr.lo \
	dsa/libcares_la-ares_htable_vpvp.lo \
	dsa/libcares_la-ares_llist.lo dsa/libcares_la-ares_slist.lo \
	event/libcares_la-ares_event_configchg.lo \
	event/libcares_la-ares_event_epoll.lo \
	event/libcares_la-ares_event_kqueue.lo \
	event/libcares_la-ares_event_poll.lo \
	event/libcares_la-ares_event_select.lo \
	event/libcares_la-ares_event_thread.lo \
	event/libcares_la-ares_event_wake_pipe.lo \
	event/libcares_la-ares_event_win32.lo \
	legacy/libcares_la-ares_create_query.lo \
	legacy/libcares_la-ares_expand_name.lo \
	legacy/libcares_la-ares_expand_string.lo \
	legacy/libcares_la-ares_fds.lo \
	legacy/libcares_la-ares_getsock.lo \
	legacy/libcares_la-ares_parse_a_reply.lo \
	legacy/libcares_la-ares_parse_aaaa_reply.lo \
	legacy/libcares_la-ares_parse_caa_reply.lo \
	legacy/libcares_la-ares_parse_mx_reply.lo \
	legacy/libcares_la-ares_parse_naptr_reply.lo \
	legacy/libcares_la-ares_parse_ns_reply.lo \
	legacy/libcares_la-ares_parse_ptr_reply.lo \
	legacy/libcares_la-ares_parse_soa_reply.lo \
	legacy/libcares_la-ares_parse_srv_reply.lo \
	legacy/libcares_la-ares_parse_txt_reply.lo \
	legacy/libcares_la-ares_parse_uri_reply.lo \
	record/libcares_la-ares_dns_mapping.lo \
	record/libcares_la-ares_dns_multistring.lo \
	record/libcares_la-ares_dns_name.lo \
	record/libcares_la-ares_dns_parse.lo \
	record/libcares_la-ares_dns_record.lo \
	record/libcares_la-ares_dns_write.lo \
	str/libcares_la-ares_buf.lo str/libcares_la-ares_str.lo \
	str/libcares_la-ares_strsplit.lo \
	util/libcares_la-ares_iface_ips.lo \
	util/libcares_la-ares_threads.lo \
	util/libcares_la-ares_timeval.lo util/libcares_la-ares_math.lo \
	util/libcares_la-ares_rand.lo util/libcares_la-ares_uri.lo
am__objects_2 =
am_libcares_la_OBJECTS = $(am__objects_1) $(am__objects_2)
libcares_la_OBJECTS = $(am_libcares_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
libcares_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(libcares_la_CFLAGS) \
	$(CFLAGS) $(libcares_la_LDFLAGS) $(LDFLAGS) -o $@
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = 
depcomp = $(SHELL) $(top_srcdir)/config/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade =  \
	./$(DEPDIR)/libcares_la-ares_addrinfo2hostent.Plo \
	./$(DEPDIR)/libcares_la-ares_addrinfo_localhost.Plo \
	./$(DEPDIR)/libcares_la-ares_android.Plo \
	./$(DEPDIR)/libcares_la-ares_cancel.Plo \
	./$(DEPDIR)/libcares_la-ares_close_sockets.Plo \
	./$(DEPDIR)/libcares_la-ares_conn.Plo \
	./$(DEPDIR)/libcares_la-ares_cookie.Plo \
	./$(DEPDIR)/libcares_la-ares_data.Plo \
	./$(DEPDIR)/libcares_la-ares_destroy.Plo \
	./$(DEPDIR)/libcares_la-ares_free_hostent.Plo \
	./$(DEPDIR)/libcares_la-ares_free_string.Plo \
	./$(DEPDIR)/libcares_la-ares_freeaddrinfo.Plo \
	./$(DEPDIR)/libcares_la-ares_getaddrinfo.Plo \
	./$(DEPDIR)/libcares_la-ares_getenv.Plo \
	./$(DEPDIR)/libcares_la-ares_gethostbyaddr.Plo \
	./$(DEPDIR)/libcares_la-ares_gethostbyname.Plo \
	./$(DEPDIR)/libcares_la-ares_getnameinfo.Plo \
	./$(DEPDIR)/libcares_la-ares_hosts_file.Plo \
	./$(DEPDIR)/libcares_la-ares_init.Plo \
	./$(DEPDIR)/libcares_la-ares_library_init.Plo \
	./$(DEPDIR)/libcares_la-ares_metrics.Plo \
	./$(DEPDIR)/libcares_la-ares_options.Plo \
	./$(DEPDIR)/libcares_la-ares_parse_into_addrinfo.Plo \
	./$(DEPDIR)/libcares_la-ares_process.Plo \
	./$(DEPDIR)/libcares_la-ares_qcache.Plo \
	./$(DEPDIR)/libcares_la-ares_query.Plo \
	./$(DEPDIR)/libcares_la-ares_search.Plo \
	./$(DEPDIR)/libcares_la-ares_send.Plo \
	./$(DEPDIR)/libcares_la-ares_set_socket_functions.Plo \
	./$(DEPDIR)/libcares_la-ares_socket.Plo \
	./$(DEPDIR)/libcares_la-ares_sortaddrinfo.Plo \
	./$(DEPDIR)/libcares_la-ares_strerror.Plo \
	./$(DEPDIR)/libcares_la-ares_sysconfig.Plo \
	./$(DEPDIR)/libcares_la-ares_sysconfig_files.Plo \
	./$(DEPDIR)/libcares_la-ares_sysconfig_mac.Plo \
	./$(DEPDIR)/libcares_la-ares_sysconfig_win.Plo \
	./$(DEPDIR)/libcares_la-ares_timeout.Plo \
	./$(DEPDIR)/libcares_la-ares_update_servers.Plo \
	./$(DEPDIR)/libcares_la-ares_version.Plo \
	./$(DEPDIR)/libcares_la-inet_net_pton.Plo \
	./$(DEPDIR)/libcares_la-inet_ntop.Plo \
	./$(DEPDIR)/libcares_la-windows_port.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_array.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_htable.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_htable_asvp.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_htable_dict.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_htable_strvp.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_htable_szvp.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_htable_vpstr.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_htable_vpvp.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_llist.Plo \
	dsa/$(DEPDIR)/libcares_la-ares_slist.Plo \
	event/$(DEPDIR)/libcares_la-ares_event_configchg.Plo \
	event/$(DEPDIR)/libcares_la-ares_event_epoll.Plo \
	event/$(DEPDIR)/libcares_la-ares_event_kqueue.Plo \
	event/$(DEPDIR)/libcares_la-ares_event_poll.Plo \
	event/$(DEPDIR)/libcares_la-ares_event_select.Plo \
	event/$(DEPDIR)/libcares_la-ares_event_thread.Plo \
	event/$(DEPDIR)/libcares_la-ares_event_wake_pipe.Plo \
	event/$(DEPDIR)/libcares_la-ares_event_win32.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_create_query.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_expand_name.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_expand_string.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_fds.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_getsock.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_a_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_aaaa_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_caa_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_mx_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_naptr_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_ns_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_ptr_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_soa_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_srv_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_txt_reply.Plo \
	legacy/$(DEPDIR)/libcares_la-ares_parse_uri_reply.Plo \
	record/$(DEPDIR)/libcares_la-ares_dns_mapping.Plo \
	record/$(DEPDIR)/libcares_la-ares_dns_multistring.Plo \
	record/$(DEPDIR)/libcares_la-ares_dns_name.Plo \
	record/$(DEPDIR)/libcares_la-ares_dns_parse.Plo \
	record/$(DEPDIR)/libcares_la-ares_dns_record.Plo \
	record/$(DEPDIR)/libcares_la-ares_dns_write.Plo \
	str/$(DEPDIR)/libcares_la-ares_buf.Plo \
	str/$(DEPDIR)/libcares_la-ares_str.Plo \
	str/$(DEPDIR)/libcares_la-ares_strsplit.Plo \
	util/$(DEPDIR)/libcares_la-ares_iface_ips.Plo \
	util/$(DEPDIR)/libcares_la-ares_math.Plo \
	util/$(DEPDIR)/libcares_la-ares_rand.Plo \
	util/$(DEPDIR)/libcares_la-ares_threads.Plo \
	util/$(DEPDIR)/libcares_la-ares_timeval.Plo \
	util/$(DEPDIR)/libcares_la-ares_uri.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libcares_la_SOURCES)
DIST_SOURCES = $(libcares_la_SOURCES)
RECURSIVE_TARGETS = all-recursive check-recursive cscopelist-recursive \
	ctags-recursive dvi-recursive html-recursive info-recursive \
	install-data-recursive install-dvi-recursive \
	install-exec-recursive install-html-recursive \
	install-info-recursive install-pdf-recursive \
	install-ps-recursive install-recursive installcheck-recursive \
	installdirs-recursive pdf-recursive ps-recursive \
	tags-recursive uninstall-recursive
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
am__recursive_targets = \
  $(RECURSIVE_TARGETS) \
  $(RECURSIVE_CLEAN_TARGETS) \
  $(am__extra_recursive_targets)
AM_RECURSIVE_TARGETS = $(am__recursive_targets:-recursive=) TAGS CTAGS \
	distdir distdir-am
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP) \
	ares_config.h.in
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/Makefile.inc \
	$(srcdir)/ares_config.h.in $(top_srcdir)/aminclude_static.am \
	$(top_srcdir)/config/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_CFLAGS = @AM_CFLAGS@

# Specify our include paths here, and do it relative to $(top_srcdir) and
# $(top_builddir), to ensure that these paths which belong to the library
# being currently built and tested are searched before the library which
# might possibly already be installed in the system.
AM_CPPFLAGS = @AM_CPPFLAGS@ -I$(top_builddir)/include \
	-I$(top_builddir)/src/lib -I$(top_srcdir)/include \
	-I$(top_srcdir)/src/lib -I$(top_srcdir)/src/lib/include
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AS = @AS@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BUILD_SUBDIRS = @BUILD_SUBDIRS@
CARES_PRIVATE_LIBS = @CARES_PRIVATE_LIBS@
CARES_RANDOM_FILE = @CARES_RANDOM_FILE@
CARES_SYMBOL_HIDING_CFLAG = @CARES_SYMBOL_HIDING_CFLAG@
CARES_VERSION_INFO = @CARES_VERSION_INFO@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CODE_COVERAGE_CFLAGS = @CODE_COVERAGE_CFLAGS@
CODE_COVERAGE_CPPFLAGS = @CODE_COVERAGE_CPPFLAGS@
CODE_COVERAGE_CXXFLAGS = @CODE_COVERAGE_CXXFLAGS@
CODE_COVERAGE_ENABLED = @CODE_COVERAGE_ENABLED@
CODE_COVERAGE_LIBS = @CODE_COVERAGE_LIBS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GCOV = @GCOV@
GENHTML = @GENHTML@
GMOCK112_CFLAGS = @GMOCK112_CFLAGS@
GMOCK112_LIBS = @GMOCK112_LIBS@
GMOCK_CFLAGS = @GMOCK_CFLAGS@
GMOCK_LIBS = @GMOCK_LIBS@
GREP = @GREP@
HAVE_CXX14 = @HAVE_CXX14@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LCOV = @LCOV@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PKGCONFIG_CFLAGS = @PKGCONFIG_CFLAGS@
PKG_CONFIG = @PKG_CONFIG@
PKG_CONFIG_LIBDIR = @PKG_CONFIG_LIBDIR@
PKG_CONFIG_PATH = @PKG_CONFIG_PATH@
PTHREAD_CC = @PTHREAD_CC@
PTHREAD_CFLAGS = @PTHREAD_CFLAGS@
PTHREAD_CXX = @PTHREAD_CXX@
PTHREAD_LIBS = @PTHREAD_LIBS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
ax_pthread_config = @ax_pthread_config@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
ifGNUmake = @ifGNUmake@
ifnGNUmake = @ifnGNUmake@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@

# Copyright (C) The c-ares project and its contributors
# SPDX-License-Identifier: MIT
AUTOMAKE_OPTIONS = foreign subdir-objects nostdinc 1.9.6
ACLOCAL_AMFLAGS = -I m4 --install
lib_LTLIBRARIES = libcares.la
man_MANS = $(MANPAGES)

# adig and ahost are just sample programs and thus not mentioned with the
# regular sources and headers
EXTRA_DIST = Makefile.inc config-win32.h CMakeLists.txt \
  ares_config.h.in ares_config.h.cmake cares.rc \
  $(CSOURCES) $(HHEADERS) config-dos.h

DISTCLEANFILES = ares_config.h
DIST_SUBDIRS = 
libcares_la_LDFLAGS = -version-info @CARES_VERSION_INFO@ \
	$(am__append_1)
libcares_la_CFLAGS_EXTRA = $(am__append_2) $(CODE_COVERAGE_CFLAGS)
libcares_la_CPPFLAGS_EXTRA = -DCARES_BUILDING_LIBRARY $(am__append_3) \
	$(CODE_COVERAGE_CPPFLAGS)
@CODE_COVERAGE_ENABLED_TRUE@GITIGNOREFILES := $(GITIGNOREFILES) $(CODE_COVERAGE_OUTPUT_FILE) $(CODE_COVERAGE_OUTPUT_DIRECTORY)
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_v_lcov_cap = $(code_coverage_v_lcov_cap_$(V))
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_v_lcov_cap_ = $(code_coverage_v_lcov_cap_$(AM_DEFAULT_VERBOSITY))
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_v_lcov_cap_0 = @echo "  LCOV   --capture" $(CODE_COVERAGE_OUTPUT_FILE);
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_v_lcov_ign = $(code_coverage_v_lcov_ign_$(V))
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_v_lcov_ign_ = $(code_coverage_v_lcov_ign_$(AM_DEFAULT_VERBOSITY))
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_v_lcov_ign_0 = @echo "  LCOV   --remove /tmp/*" $(CODE_COVERAGE_IGNORE_PATTERN);
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_v_genhtml = $(code_coverage_v_genhtml_$(V))
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_v_genhtml_ = $(code_coverage_v_genhtml_$(AM_DEFAULT_VERBOSITY))
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_v_genhtml_0 = @echo "  GEN   " "$(CODE_COVERAGE_OUTPUT_DIRECTORY)";
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_quiet = $(code_coverage_quiet_$(V))
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_quiet_ = $(code_coverage_quiet_$(AM_DEFAULT_VERBOSITY))
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_quiet_0 = --quiet

# sanitizes the test-name: replaces with underscores: dashes and dots
@CODE_COVERAGE_ENABLED_TRUE@code_coverage_sanitize = $(subst -,_,$(subst .,_,$(1)))
@CODE_COVERAGE_ENABLED_TRUE@AM_DISTCHECK_CONFIGURE_FLAGS := $(AM_DISTCHECK_CONFIGURE_FLAGS) --disable-code-coverage
libcares_la_LIBS = $(CODE_COVERAGE_LIBS)
libcares_la_CFLAGS = $(AM_CFLAGS) $(libcares_la_CFLAGS_EXTRA)
libcares_la_CPPFLAGS = $(AM_CPPFLAGS) $(libcares_la_CPPFLAGS_EXTRA)
CSOURCES = ares_addrinfo2hostent.c	\
  ares_addrinfo_localhost.c		\
  ares_android.c			\
  ares_cancel.c				\
  ares_close_sockets.c			\
  ares_conn.c				\
  ares_cookie.c				\
  ares_data.c				\
  ares_destroy.c			\
  ares_free_hostent.c			\
  ares_free_string.c			\
  ares_freeaddrinfo.c			\
  ares_getaddrinfo.c			\
  ares_getenv.c				\
  ares_gethostbyaddr.c			\
  ares_gethostbyname.c			\
  ares_getnameinfo.c			\
  ares_hosts_file.c			\
  ares_init.c				\
  ares_library_init.c			\
  ares_metrics.c			\
  ares_options.c			\
  ares_parse_into_addrinfo.c		\
  ares_process.c			\
  ares_qcache.c				\
  ares_query.c				\
  ares_search.c				\
  ares_send.c				\
  ares_set_socket_functions.c		\
  ares_socket.c				\
  ares_sortaddrinfo.c			\
  ares_strerror.c			\
  ares_sysconfig.c			\
  ares_sysconfig_files.c		\
  ares_sysconfig_mac.c			\
  ares_sysconfig_win.c			\
  ares_timeout.c			\
  ares_update_servers.c			\
  ares_version.c			\
  inet_net_pton.c			\
  inet_ntop.c				\
  windows_port.c			\
  dsa/ares_array.c			\
  dsa/ares_htable.c			\
  dsa/ares_htable_asvp.c		\
  dsa/ares_htable_dict.c		\
  dsa/ares_htable_strvp.c		\
  dsa/ares_htable_szvp.c		\
  dsa/ares_htable_vpstr.c		\
  dsa/ares_htable_vpvp.c		\
  dsa/ares_llist.c			\
  dsa/ares_slist.c			\
  event/ares_event_configchg.c		\
  event/ares_event_epoll.c		\
  event/ares_event_kqueue.c		\
  event/ares_event_poll.c		\
  event/ares_event_select.c		\
  event/ares_event_thread.c		\
  event/ares_event_wake_pipe.c		\
  event/ares_event_win32.c		\
  legacy/ares_create_query.c		\
  legacy/ares_expand_name.c		\
  legacy/ares_expand_string.c		\
  legacy/ares_fds.c			\
  legacy/ares_getsock.c			\
  legacy/ares_parse_a_reply.c		\
  legacy/ares_parse_aaaa_reply.c	\
  legacy/ares_parse_caa_reply.c		\
  legacy/ares_parse_mx_reply.c		\
  legacy/ares_parse_naptr_reply.c	\
  legacy/ares_parse_ns_reply.c		\
  legacy/ares_parse_ptr_reply.c		\
  legacy/ares_parse_soa_reply.c		\
  legacy/ares_parse_srv_reply.c		\
  legacy/ares_parse_txt_reply.c		\
  legacy/ares_parse_uri_reply.c		\
  record/ares_dns_mapping.c		\
  record/ares_dns_multistring.c		\
  record/ares_dns_name.c		\
  record/ares_dns_parse.c		\
  record/ares_dns_record.c		\
  record/ares_dns_write.c		\
  str/ares_buf.c			\
  str/ares_str.c			\
  str/ares_strsplit.c			\
  util/ares_iface_ips.c			\
  util/ares_threads.c			\
  util/ares_timeval.c			\
  util/ares_math.c			\
  util/ares_rand.c			\
  util/ares_uri.c

HHEADERS = ares_android.h			\
  ares_conn.h				\
  ares_data.h				\
  ares_getenv.h				\
  ares_inet_net_pton.h			\
  ares_ipv6.h				\
  ares_private.h			\
  ares_setup.h				\
  ares_socket.h				\
  dsa/ares_htable.h			\
  dsa/ares_slist.h			\
  event/ares_event.h			\
  event/ares_event_win32.h		\
  include/ares_array.h			\
  include/ares_buf.h			\
  include/ares_htable_asvp.h		\
  include/ares_htable_dict.h		\
  include/ares_htable_strvp.h		\
  include/ares_htable_szvp.h		\
  include/ares_htable_vpstr.h		\
  include/ares_htable_vpvp.h		\
  include/ares_llist.h			\
  include/ares_mem.h			\
  include/ares_str.h			\
  record/ares_dns_multistring.h		\
  record/ares_dns_private.h		\
  str/ares_strsplit.h			\
  util/ares_iface_ips.h			\
  util/ares_math.h			\
  util/ares_rand.h			\
  util/ares_time.h			\
  util/ares_threads.h			\
  util/ares_uri.h			\
  thirdparty/apple/dnsinfo.h


# Makefile.inc provides the CSOURCES and HHEADERS defines
libcares_la_SOURCES = $(CSOURCES) $(HHEADERS)
all: ares_config.h
	$(MAKE) $(AM_MAKEFLAGS) all-recursive

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am $(top_srcdir)/aminclude_static.am $(srcdir)/Makefile.inc $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign src/lib/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign src/lib/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;
$(top_srcdir)/aminclude_static.am $(srcdir)/Makefile.inc $(am__empty):

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

ares_config.h: stamp-h1
	@test -f $@ || rm -f stamp-h1
	@test -f $@ || $(MAKE) $(AM_MAKEFLAGS) stamp-h1

stamp-h1: $(srcdir)/ares_config.h.in $(top_builddir)/config.status
	@rm -f stamp-h1
	cd $(top_builddir) && $(SHELL) ./config.status src/lib/ares_config.h
$(srcdir)/ares_config.h.in: @MAINTAINER_MODE_TRUE@ $(am__configure_deps) 
	($(am__cd) $(top_srcdir) && $(AUTOHEADER))
	rm -f stamp-h1
	touch $@

distclean-hdr:
	-rm -f ares_config.h stamp-h1

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
dsa/$(am__dirstamp):
	@$(MKDIR_P) dsa
	@: > dsa/$(am__dirstamp)
dsa/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) dsa/$(DEPDIR)
	@: > dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_array.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_htable.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_htable_asvp.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_htable_dict.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_htable_strvp.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_htable_szvp.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_htable_vpstr.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_htable_vpvp.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_llist.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
dsa/libcares_la-ares_slist.lo: dsa/$(am__dirstamp) \
	dsa/$(DEPDIR)/$(am__dirstamp)
event/$(am__dirstamp):
	@$(MKDIR_P) event
	@: > event/$(am__dirstamp)
event/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) event/$(DEPDIR)
	@: > event/$(DEPDIR)/$(am__dirstamp)
event/libcares_la-ares_event_configchg.lo: event/$(am__dirstamp) \
	event/$(DEPDIR)/$(am__dirstamp)
event/libcares_la-ares_event_epoll.lo: event/$(am__dirstamp) \
	event/$(DEPDIR)/$(am__dirstamp)
event/libcares_la-ares_event_kqueue.lo: event/$(am__dirstamp) \
	event/$(DEPDIR)/$(am__dirstamp)
event/libcares_la-ares_event_poll.lo: event/$(am__dirstamp) \
	event/$(DEPDIR)/$(am__dirstamp)
event/libcares_la-ares_event_select.lo: event/$(am__dirstamp) \
	event/$(DEPDIR)/$(am__dirstamp)
event/libcares_la-ares_event_thread.lo: event/$(am__dirstamp) \
	event/$(DEPDIR)/$(am__dirstamp)
event/libcares_la-ares_event_wake_pipe.lo: event/$(am__dirstamp) \
	event/$(DEPDIR)/$(am__dirstamp)
event/libcares_la-ares_event_win32.lo: event/$(am__dirstamp) \
	event/$(DEPDIR)/$(am__dirstamp)
legacy/$(am__dirstamp):
	@$(MKDIR_P) legacy
	@: > legacy/$(am__dirstamp)
legacy/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) legacy/$(DEPDIR)
	@: > legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_create_query.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_expand_name.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_expand_string.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_fds.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_getsock.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_a_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_aaaa_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_caa_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_mx_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_naptr_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_ns_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_ptr_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_soa_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_srv_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_txt_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
legacy/libcares_la-ares_parse_uri_reply.lo: legacy/$(am__dirstamp) \
	legacy/$(DEPDIR)/$(am__dirstamp)
record/$(am__dirstamp):
	@$(MKDIR_P) record
	@: > record/$(am__dirstamp)
record/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) record/$(DEPDIR)
	@: > record/$(DEPDIR)/$(am__dirstamp)
record/libcares_la-ares_dns_mapping.lo: record/$(am__dirstamp) \
	record/$(DEPDIR)/$(am__dirstamp)
record/libcares_la-ares_dns_multistring.lo: record/$(am__dirstamp) \
	record/$(DEPDIR)/$(am__dirstamp)
record/libcares_la-ares_dns_name.lo: record/$(am__dirstamp) \
	record/$(DEPDIR)/$(am__dirstamp)
record/libcares_la-ares_dns_parse.lo: record/$(am__dirstamp) \
	record/$(DEPDIR)/$(am__dirstamp)
record/libcares_la-ares_dns_record.lo: record/$(am__dirstamp) \
	record/$(DEPDIR)/$(am__dirstamp)
record/libcares_la-ares_dns_write.lo: record/$(am__dirstamp) \
	record/$(DEPDIR)/$(am__dirstamp)
str/$(am__dirstamp):
	@$(MKDIR_P) str
	@: > str/$(am__dirstamp)
str/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) str/$(DEPDIR)
	@: > str/$(DEPDIR)/$(am__dirstamp)
str/libcares_la-ares_buf.lo: str/$(am__dirstamp) \
	str/$(DEPDIR)/$(am__dirstamp)
str/libcares_la-ares_str.lo: str/$(am__dirstamp) \
	str/$(DEPDIR)/$(am__dirstamp)
str/libcares_la-ares_strsplit.lo: str/$(am__dirstamp) \
	str/$(DEPDIR)/$(am__dirstamp)
util/$(am__dirstamp):
	@$(MKDIR_P) util
	@: > util/$(am__dirstamp)
util/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) util/$(DEPDIR)
	@: > util/$(DEPDIR)/$(am__dirstamp)
util/libcares_la-ares_iface_ips.lo: util/$(am__dirstamp) \
	util/$(DEPDIR)/$(am__dirstamp)
util/libcares_la-ares_threads.lo: util/$(am__dirstamp) \
	util/$(DEPDIR)/$(am__dirstamp)
util/libcares_la-ares_timeval.lo: util/$(am__dirstamp) \
	util/$(DEPDIR)/$(am__dirstamp)
util/libcares_la-ares_math.lo: util/$(am__dirstamp) \
	util/$(DEPDIR)/$(am__dirstamp)
util/libcares_la-ares_rand.lo: util/$(am__dirstamp) \
	util/$(DEPDIR)/$(am__dirstamp)
util/libcares_la-ares_uri.lo: util/$(am__dirstamp) \
	util/$(DEPDIR)/$(am__dirstamp)

libcares.la: $(libcares_la_OBJECTS) $(libcares_la_DEPENDENCIES) $(EXTRA_libcares_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libcares_la_LINK) -rpath $(libdir) $(libcares_la_OBJECTS) $(libcares_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f dsa/*.$(OBJEXT)
	-rm -f dsa/*.lo
	-rm -f event/*.$(OBJEXT)
	-rm -f event/*.lo
	-rm -f legacy/*.$(OBJEXT)
	-rm -f legacy/*.lo
	-rm -f record/*.$(OBJEXT)
	-rm -f record/*.lo
	-rm -f str/*.$(OBJEXT)
	-rm -f str/*.lo
	-rm -f util/*.$(OBJEXT)
	-rm -f util/*.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_addrinfo2hostent.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_addrinfo_localhost.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_android.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_cancel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_close_sockets.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_conn.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_cookie.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_data.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_destroy.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_free_hostent.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_free_string.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_freeaddrinfo.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_getaddrinfo.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_getenv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_gethostbyaddr.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_gethostbyname.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_getnameinfo.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_hosts_file.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_init.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_library_init.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_metrics.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_options.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_parse_into_addrinfo.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_process.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_qcache.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_query.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_search.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_send.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_set_socket_functions.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_socket.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_sortaddrinfo.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_strerror.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_sysconfig.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_sysconfig_files.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_sysconfig_mac.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_sysconfig_win.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_timeout.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_update_servers.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-ares_version.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-inet_net_pton.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-inet_ntop.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libcares_la-windows_port.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_array.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_htable.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_htable_asvp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_htable_dict.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_htable_strvp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_htable_szvp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_htable_vpstr.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_htable_vpvp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_llist.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dsa/$(DEPDIR)/libcares_la-ares_slist.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@event/$(DEPDIR)/libcares_la-ares_event_configchg.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@event/$(DEPDIR)/libcares_la-ares_event_epoll.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@event/$(DEPDIR)/libcares_la-ares_event_kqueue.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@event/$(DEPDIR)/libcares_la-ares_event_poll.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@event/$(DEPDIR)/libcares_la-ares_event_select.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@event/$(DEPDIR)/libcares_la-ares_event_thread.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@event/$(DEPDIR)/libcares_la-ares_event_wake_pipe.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@event/$(DEPDIR)/libcares_la-ares_event_win32.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_create_query.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_expand_name.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_expand_string.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_fds.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_getsock.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_a_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_aaaa_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_caa_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_mx_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_naptr_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_ns_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_ptr_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_soa_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_srv_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_txt_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@legacy/$(DEPDIR)/libcares_la-ares_parse_uri_reply.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@record/$(DEPDIR)/libcares_la-ares_dns_mapping.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@record/$(DEPDIR)/libcares_la-ares_dns_multistring.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@record/$(DEPDIR)/libcares_la-ares_dns_name.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@record/$(DEPDIR)/libcares_la-ares_dns_parse.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@record/$(DEPDIR)/libcares_la-ares_dns_record.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@record/$(DEPDIR)/libcares_la-ares_dns_write.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@str/$(DEPDIR)/libcares_la-ares_buf.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@str/$(DEPDIR)/libcares_la-ares_str.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@str/$(DEPDIR)/libcares_la-ares_strsplit.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@util/$(DEPDIR)/libcares_la-ares_iface_ips.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@util/$(DEPDIR)/libcares_la-ares_math.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@util/$(DEPDIR)/libcares_la-ares_rand.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@util/$(DEPDIR)/libcares_la-ares_threads.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@util/$(DEPDIR)/libcares_la-ares_timeval.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@util/$(DEPDIR)/libcares_la-ares_uri.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

libcares_la-ares_addrinfo2hostent.lo: ares_addrinfo2hostent.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_addrinfo2hostent.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_addrinfo2hostent.Tpo -c -o libcares_la-ares_addrinfo2hostent.lo `test -f 'ares_addrinfo2hostent.c' || echo '$(srcdir)/'`ares_addrinfo2hostent.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_addrinfo2hostent.Tpo $(DEPDIR)/libcares_la-ares_addrinfo2hostent.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_addrinfo2hostent.c' object='libcares_la-ares_addrinfo2hostent.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_addrinfo2hostent.lo `test -f 'ares_addrinfo2hostent.c' || echo '$(srcdir)/'`ares_addrinfo2hostent.c

libcares_la-ares_addrinfo_localhost.lo: ares_addrinfo_localhost.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_addrinfo_localhost.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_addrinfo_localhost.Tpo -c -o libcares_la-ares_addrinfo_localhost.lo `test -f 'ares_addrinfo_localhost.c' || echo '$(srcdir)/'`ares_addrinfo_localhost.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_addrinfo_localhost.Tpo $(DEPDIR)/libcares_la-ares_addrinfo_localhost.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_addrinfo_localhost.c' object='libcares_la-ares_addrinfo_localhost.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_addrinfo_localhost.lo `test -f 'ares_addrinfo_localhost.c' || echo '$(srcdir)/'`ares_addrinfo_localhost.c

libcares_la-ares_android.lo: ares_android.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_android.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_android.Tpo -c -o libcares_la-ares_android.lo `test -f 'ares_android.c' || echo '$(srcdir)/'`ares_android.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_android.Tpo $(DEPDIR)/libcares_la-ares_android.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_android.c' object='libcares_la-ares_android.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_android.lo `test -f 'ares_android.c' || echo '$(srcdir)/'`ares_android.c

libcares_la-ares_cancel.lo: ares_cancel.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_cancel.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_cancel.Tpo -c -o libcares_la-ares_cancel.lo `test -f 'ares_cancel.c' || echo '$(srcdir)/'`ares_cancel.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_cancel.Tpo $(DEPDIR)/libcares_la-ares_cancel.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_cancel.c' object='libcares_la-ares_cancel.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_cancel.lo `test -f 'ares_cancel.c' || echo '$(srcdir)/'`ares_cancel.c

libcares_la-ares_close_sockets.lo: ares_close_sockets.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_close_sockets.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_close_sockets.Tpo -c -o libcares_la-ares_close_sockets.lo `test -f 'ares_close_sockets.c' || echo '$(srcdir)/'`ares_close_sockets.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_close_sockets.Tpo $(DEPDIR)/libcares_la-ares_close_sockets.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_close_sockets.c' object='libcares_la-ares_close_sockets.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_close_sockets.lo `test -f 'ares_close_sockets.c' || echo '$(srcdir)/'`ares_close_sockets.c

libcares_la-ares_conn.lo: ares_conn.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_conn.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_conn.Tpo -c -o libcares_la-ares_conn.lo `test -f 'ares_conn.c' || echo '$(srcdir)/'`ares_conn.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_conn.Tpo $(DEPDIR)/libcares_la-ares_conn.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_conn.c' object='libcares_la-ares_conn.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_conn.lo `test -f 'ares_conn.c' || echo '$(srcdir)/'`ares_conn.c

libcares_la-ares_cookie.lo: ares_cookie.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_cookie.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_cookie.Tpo -c -o libcares_la-ares_cookie.lo `test -f 'ares_cookie.c' || echo '$(srcdir)/'`ares_cookie.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_cookie.Tpo $(DEPDIR)/libcares_la-ares_cookie.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_cookie.c' object='libcares_la-ares_cookie.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_cookie.lo `test -f 'ares_cookie.c' || echo '$(srcdir)/'`ares_cookie.c

libcares_la-ares_data.lo: ares_data.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_data.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_data.Tpo -c -o libcares_la-ares_data.lo `test -f 'ares_data.c' || echo '$(srcdir)/'`ares_data.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_data.Tpo $(DEPDIR)/libcares_la-ares_data.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_data.c' object='libcares_la-ares_data.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_data.lo `test -f 'ares_data.c' || echo '$(srcdir)/'`ares_data.c

libcares_la-ares_destroy.lo: ares_destroy.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_destroy.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_destroy.Tpo -c -o libcares_la-ares_destroy.lo `test -f 'ares_destroy.c' || echo '$(srcdir)/'`ares_destroy.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_destroy.Tpo $(DEPDIR)/libcares_la-ares_destroy.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_destroy.c' object='libcares_la-ares_destroy.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_destroy.lo `test -f 'ares_destroy.c' || echo '$(srcdir)/'`ares_destroy.c

libcares_la-ares_free_hostent.lo: ares_free_hostent.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_free_hostent.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_free_hostent.Tpo -c -o libcares_la-ares_free_hostent.lo `test -f 'ares_free_hostent.c' || echo '$(srcdir)/'`ares_free_hostent.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_free_hostent.Tpo $(DEPDIR)/libcares_la-ares_free_hostent.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_free_hostent.c' object='libcares_la-ares_free_hostent.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_free_hostent.lo `test -f 'ares_free_hostent.c' || echo '$(srcdir)/'`ares_free_hostent.c

libcares_la-ares_free_string.lo: ares_free_string.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_free_string.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_free_string.Tpo -c -o libcares_la-ares_free_string.lo `test -f 'ares_free_string.c' || echo '$(srcdir)/'`ares_free_string.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_free_string.Tpo $(DEPDIR)/libcares_la-ares_free_string.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_free_string.c' object='libcares_la-ares_free_string.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_free_string.lo `test -f 'ares_free_string.c' || echo '$(srcdir)/'`ares_free_string.c

libcares_la-ares_freeaddrinfo.lo: ares_freeaddrinfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_freeaddrinfo.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_freeaddrinfo.Tpo -c -o libcares_la-ares_freeaddrinfo.lo `test -f 'ares_freeaddrinfo.c' || echo '$(srcdir)/'`ares_freeaddrinfo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_freeaddrinfo.Tpo $(DEPDIR)/libcares_la-ares_freeaddrinfo.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_freeaddrinfo.c' object='libcares_la-ares_freeaddrinfo.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_freeaddrinfo.lo `test -f 'ares_freeaddrinfo.c' || echo '$(srcdir)/'`ares_freeaddrinfo.c

libcares_la-ares_getaddrinfo.lo: ares_getaddrinfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_getaddrinfo.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_getaddrinfo.Tpo -c -o libcares_la-ares_getaddrinfo.lo `test -f 'ares_getaddrinfo.c' || echo '$(srcdir)/'`ares_getaddrinfo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_getaddrinfo.Tpo $(DEPDIR)/libcares_la-ares_getaddrinfo.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_getaddrinfo.c' object='libcares_la-ares_getaddrinfo.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_getaddrinfo.lo `test -f 'ares_getaddrinfo.c' || echo '$(srcdir)/'`ares_getaddrinfo.c

libcares_la-ares_getenv.lo: ares_getenv.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_getenv.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_getenv.Tpo -c -o libcares_la-ares_getenv.lo `test -f 'ares_getenv.c' || echo '$(srcdir)/'`ares_getenv.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_getenv.Tpo $(DEPDIR)/libcares_la-ares_getenv.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_getenv.c' object='libcares_la-ares_getenv.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_getenv.lo `test -f 'ares_getenv.c' || echo '$(srcdir)/'`ares_getenv.c

libcares_la-ares_gethostbyaddr.lo: ares_gethostbyaddr.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_gethostbyaddr.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_gethostbyaddr.Tpo -c -o libcares_la-ares_gethostbyaddr.lo `test -f 'ares_gethostbyaddr.c' || echo '$(srcdir)/'`ares_gethostbyaddr.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_gethostbyaddr.Tpo $(DEPDIR)/libcares_la-ares_gethostbyaddr.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_gethostbyaddr.c' object='libcares_la-ares_gethostbyaddr.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_gethostbyaddr.lo `test -f 'ares_gethostbyaddr.c' || echo '$(srcdir)/'`ares_gethostbyaddr.c

libcares_la-ares_gethostbyname.lo: ares_gethostbyname.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_gethostbyname.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_gethostbyname.Tpo -c -o libcares_la-ares_gethostbyname.lo `test -f 'ares_gethostbyname.c' || echo '$(srcdir)/'`ares_gethostbyname.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_gethostbyname.Tpo $(DEPDIR)/libcares_la-ares_gethostbyname.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_gethostbyname.c' object='libcares_la-ares_gethostbyname.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_gethostbyname.lo `test -f 'ares_gethostbyname.c' || echo '$(srcdir)/'`ares_gethostbyname.c

libcares_la-ares_getnameinfo.lo: ares_getnameinfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_getnameinfo.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_getnameinfo.Tpo -c -o libcares_la-ares_getnameinfo.lo `test -f 'ares_getnameinfo.c' || echo '$(srcdir)/'`ares_getnameinfo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_getnameinfo.Tpo $(DEPDIR)/libcares_la-ares_getnameinfo.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_getnameinfo.c' object='libcares_la-ares_getnameinfo.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_getnameinfo.lo `test -f 'ares_getnameinfo.c' || echo '$(srcdir)/'`ares_getnameinfo.c

libcares_la-ares_hosts_file.lo: ares_hosts_file.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_hosts_file.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_hosts_file.Tpo -c -o libcares_la-ares_hosts_file.lo `test -f 'ares_hosts_file.c' || echo '$(srcdir)/'`ares_hosts_file.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_hosts_file.Tpo $(DEPDIR)/libcares_la-ares_hosts_file.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_hosts_file.c' object='libcares_la-ares_hosts_file.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_hosts_file.lo `test -f 'ares_hosts_file.c' || echo '$(srcdir)/'`ares_hosts_file.c

libcares_la-ares_init.lo: ares_init.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_init.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_init.Tpo -c -o libcares_la-ares_init.lo `test -f 'ares_init.c' || echo '$(srcdir)/'`ares_init.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_init.Tpo $(DEPDIR)/libcares_la-ares_init.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_init.c' object='libcares_la-ares_init.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_init.lo `test -f 'ares_init.c' || echo '$(srcdir)/'`ares_init.c

libcares_la-ares_library_init.lo: ares_library_init.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_library_init.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_library_init.Tpo -c -o libcares_la-ares_library_init.lo `test -f 'ares_library_init.c' || echo '$(srcdir)/'`ares_library_init.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_library_init.Tpo $(DEPDIR)/libcares_la-ares_library_init.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_library_init.c' object='libcares_la-ares_library_init.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_library_init.lo `test -f 'ares_library_init.c' || echo '$(srcdir)/'`ares_library_init.c

libcares_la-ares_metrics.lo: ares_metrics.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_metrics.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_metrics.Tpo -c -o libcares_la-ares_metrics.lo `test -f 'ares_metrics.c' || echo '$(srcdir)/'`ares_metrics.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_metrics.Tpo $(DEPDIR)/libcares_la-ares_metrics.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_metrics.c' object='libcares_la-ares_metrics.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_metrics.lo `test -f 'ares_metrics.c' || echo '$(srcdir)/'`ares_metrics.c

libcares_la-ares_options.lo: ares_options.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_options.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_options.Tpo -c -o libcares_la-ares_options.lo `test -f 'ares_options.c' || echo '$(srcdir)/'`ares_options.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_options.Tpo $(DEPDIR)/libcares_la-ares_options.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_options.c' object='libcares_la-ares_options.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_options.lo `test -f 'ares_options.c' || echo '$(srcdir)/'`ares_options.c

libcares_la-ares_parse_into_addrinfo.lo: ares_parse_into_addrinfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_parse_into_addrinfo.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_parse_into_addrinfo.Tpo -c -o libcares_la-ares_parse_into_addrinfo.lo `test -f 'ares_parse_into_addrinfo.c' || echo '$(srcdir)/'`ares_parse_into_addrinfo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_parse_into_addrinfo.Tpo $(DEPDIR)/libcares_la-ares_parse_into_addrinfo.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_parse_into_addrinfo.c' object='libcares_la-ares_parse_into_addrinfo.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_parse_into_addrinfo.lo `test -f 'ares_parse_into_addrinfo.c' || echo '$(srcdir)/'`ares_parse_into_addrinfo.c

libcares_la-ares_process.lo: ares_process.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_process.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_process.Tpo -c -o libcares_la-ares_process.lo `test -f 'ares_process.c' || echo '$(srcdir)/'`ares_process.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_process.Tpo $(DEPDIR)/libcares_la-ares_process.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_process.c' object='libcares_la-ares_process.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_process.lo `test -f 'ares_process.c' || echo '$(srcdir)/'`ares_process.c

libcares_la-ares_qcache.lo: ares_qcache.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_qcache.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_qcache.Tpo -c -o libcares_la-ares_qcache.lo `test -f 'ares_qcache.c' || echo '$(srcdir)/'`ares_qcache.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_qcache.Tpo $(DEPDIR)/libcares_la-ares_qcache.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_qcache.c' object='libcares_la-ares_qcache.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_qcache.lo `test -f 'ares_qcache.c' || echo '$(srcdir)/'`ares_qcache.c

libcares_la-ares_query.lo: ares_query.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_query.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_query.Tpo -c -o libcares_la-ares_query.lo `test -f 'ares_query.c' || echo '$(srcdir)/'`ares_query.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_query.Tpo $(DEPDIR)/libcares_la-ares_query.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_query.c' object='libcares_la-ares_query.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_query.lo `test -f 'ares_query.c' || echo '$(srcdir)/'`ares_query.c

libcares_la-ares_search.lo: ares_search.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_search.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_search.Tpo -c -o libcares_la-ares_search.lo `test -f 'ares_search.c' || echo '$(srcdir)/'`ares_search.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_search.Tpo $(DEPDIR)/libcares_la-ares_search.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_search.c' object='libcares_la-ares_search.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_search.lo `test -f 'ares_search.c' || echo '$(srcdir)/'`ares_search.c

libcares_la-ares_send.lo: ares_send.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_send.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_send.Tpo -c -o libcares_la-ares_send.lo `test -f 'ares_send.c' || echo '$(srcdir)/'`ares_send.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_send.Tpo $(DEPDIR)/libcares_la-ares_send.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_send.c' object='libcares_la-ares_send.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_send.lo `test -f 'ares_send.c' || echo '$(srcdir)/'`ares_send.c

libcares_la-ares_set_socket_functions.lo: ares_set_socket_functions.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_set_socket_functions.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_set_socket_functions.Tpo -c -o libcares_la-ares_set_socket_functions.lo `test -f 'ares_set_socket_functions.c' || echo '$(srcdir)/'`ares_set_socket_functions.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_set_socket_functions.Tpo $(DEPDIR)/libcares_la-ares_set_socket_functions.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_set_socket_functions.c' object='libcares_la-ares_set_socket_functions.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_set_socket_functions.lo `test -f 'ares_set_socket_functions.c' || echo '$(srcdir)/'`ares_set_socket_functions.c

libcares_la-ares_socket.lo: ares_socket.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_socket.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_socket.Tpo -c -o libcares_la-ares_socket.lo `test -f 'ares_socket.c' || echo '$(srcdir)/'`ares_socket.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_socket.Tpo $(DEPDIR)/libcares_la-ares_socket.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_socket.c' object='libcares_la-ares_socket.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_socket.lo `test -f 'ares_socket.c' || echo '$(srcdir)/'`ares_socket.c

libcares_la-ares_sortaddrinfo.lo: ares_sortaddrinfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_sortaddrinfo.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_sortaddrinfo.Tpo -c -o libcares_la-ares_sortaddrinfo.lo `test -f 'ares_sortaddrinfo.c' || echo '$(srcdir)/'`ares_sortaddrinfo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_sortaddrinfo.Tpo $(DEPDIR)/libcares_la-ares_sortaddrinfo.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_sortaddrinfo.c' object='libcares_la-ares_sortaddrinfo.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_sortaddrinfo.lo `test -f 'ares_sortaddrinfo.c' || echo '$(srcdir)/'`ares_sortaddrinfo.c

libcares_la-ares_strerror.lo: ares_strerror.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_strerror.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_strerror.Tpo -c -o libcares_la-ares_strerror.lo `test -f 'ares_strerror.c' || echo '$(srcdir)/'`ares_strerror.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_strerror.Tpo $(DEPDIR)/libcares_la-ares_strerror.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_strerror.c' object='libcares_la-ares_strerror.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_strerror.lo `test -f 'ares_strerror.c' || echo '$(srcdir)/'`ares_strerror.c

libcares_la-ares_sysconfig.lo: ares_sysconfig.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_sysconfig.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_sysconfig.Tpo -c -o libcares_la-ares_sysconfig.lo `test -f 'ares_sysconfig.c' || echo '$(srcdir)/'`ares_sysconfig.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_sysconfig.Tpo $(DEPDIR)/libcares_la-ares_sysconfig.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_sysconfig.c' object='libcares_la-ares_sysconfig.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_sysconfig.lo `test -f 'ares_sysconfig.c' || echo '$(srcdir)/'`ares_sysconfig.c

libcares_la-ares_sysconfig_files.lo: ares_sysconfig_files.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_sysconfig_files.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_sysconfig_files.Tpo -c -o libcares_la-ares_sysconfig_files.lo `test -f 'ares_sysconfig_files.c' || echo '$(srcdir)/'`ares_sysconfig_files.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_sysconfig_files.Tpo $(DEPDIR)/libcares_la-ares_sysconfig_files.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_sysconfig_files.c' object='libcares_la-ares_sysconfig_files.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_sysconfig_files.lo `test -f 'ares_sysconfig_files.c' || echo '$(srcdir)/'`ares_sysconfig_files.c

libcares_la-ares_sysconfig_mac.lo: ares_sysconfig_mac.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_sysconfig_mac.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_sysconfig_mac.Tpo -c -o libcares_la-ares_sysconfig_mac.lo `test -f 'ares_sysconfig_mac.c' || echo '$(srcdir)/'`ares_sysconfig_mac.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_sysconfig_mac.Tpo $(DEPDIR)/libcares_la-ares_sysconfig_mac.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_sysconfig_mac.c' object='libcares_la-ares_sysconfig_mac.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_sysconfig_mac.lo `test -f 'ares_sysconfig_mac.c' || echo '$(srcdir)/'`ares_sysconfig_mac.c

libcares_la-ares_sysconfig_win.lo: ares_sysconfig_win.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_sysconfig_win.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_sysconfig_win.Tpo -c -o libcares_la-ares_sysconfig_win.lo `test -f 'ares_sysconfig_win.c' || echo '$(srcdir)/'`ares_sysconfig_win.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_sysconfig_win.Tpo $(DEPDIR)/libcares_la-ares_sysconfig_win.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_sysconfig_win.c' object='libcares_la-ares_sysconfig_win.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_sysconfig_win.lo `test -f 'ares_sysconfig_win.c' || echo '$(srcdir)/'`ares_sysconfig_win.c

libcares_la-ares_timeout.lo: ares_timeout.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_timeout.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_timeout.Tpo -c -o libcares_la-ares_timeout.lo `test -f 'ares_timeout.c' || echo '$(srcdir)/'`ares_timeout.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_timeout.Tpo $(DEPDIR)/libcares_la-ares_timeout.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_timeout.c' object='libcares_la-ares_timeout.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_timeout.lo `test -f 'ares_timeout.c' || echo '$(srcdir)/'`ares_timeout.c

libcares_la-ares_update_servers.lo: ares_update_servers.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_update_servers.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_update_servers.Tpo -c -o libcares_la-ares_update_servers.lo `test -f 'ares_update_servers.c' || echo '$(srcdir)/'`ares_update_servers.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_update_servers.Tpo $(DEPDIR)/libcares_la-ares_update_servers.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_update_servers.c' object='libcares_la-ares_update_servers.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_update_servers.lo `test -f 'ares_update_servers.c' || echo '$(srcdir)/'`ares_update_servers.c

libcares_la-ares_version.lo: ares_version.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-ares_version.lo -MD -MP -MF $(DEPDIR)/libcares_la-ares_version.Tpo -c -o libcares_la-ares_version.lo `test -f 'ares_version.c' || echo '$(srcdir)/'`ares_version.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-ares_version.Tpo $(DEPDIR)/libcares_la-ares_version.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ares_version.c' object='libcares_la-ares_version.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-ares_version.lo `test -f 'ares_version.c' || echo '$(srcdir)/'`ares_version.c

libcares_la-inet_net_pton.lo: inet_net_pton.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-inet_net_pton.lo -MD -MP -MF $(DEPDIR)/libcares_la-inet_net_pton.Tpo -c -o libcares_la-inet_net_pton.lo `test -f 'inet_net_pton.c' || echo '$(srcdir)/'`inet_net_pton.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-inet_net_pton.Tpo $(DEPDIR)/libcares_la-inet_net_pton.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='inet_net_pton.c' object='libcares_la-inet_net_pton.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-inet_net_pton.lo `test -f 'inet_net_pton.c' || echo '$(srcdir)/'`inet_net_pton.c

libcares_la-inet_ntop.lo: inet_ntop.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-inet_ntop.lo -MD -MP -MF $(DEPDIR)/libcares_la-inet_ntop.Tpo -c -o libcares_la-inet_ntop.lo `test -f 'inet_ntop.c' || echo '$(srcdir)/'`inet_ntop.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-inet_ntop.Tpo $(DEPDIR)/libcares_la-inet_ntop.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='inet_ntop.c' object='libcares_la-inet_ntop.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-inet_ntop.lo `test -f 'inet_ntop.c' || echo '$(srcdir)/'`inet_ntop.c

libcares_la-windows_port.lo: windows_port.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT libcares_la-windows_port.lo -MD -MP -MF $(DEPDIR)/libcares_la-windows_port.Tpo -c -o libcares_la-windows_port.lo `test -f 'windows_port.c' || echo '$(srcdir)/'`windows_port.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libcares_la-windows_port.Tpo $(DEPDIR)/libcares_la-windows_port.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='windows_port.c' object='libcares_la-windows_port.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o libcares_la-windows_port.lo `test -f 'windows_port.c' || echo '$(srcdir)/'`windows_port.c

dsa/libcares_la-ares_array.lo: dsa/ares_array.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_array.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_array.Tpo -c -o dsa/libcares_la-ares_array.lo `test -f 'dsa/ares_array.c' || echo '$(srcdir)/'`dsa/ares_array.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_array.Tpo dsa/$(DEPDIR)/libcares_la-ares_array.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_array.c' object='dsa/libcares_la-ares_array.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_array.lo `test -f 'dsa/ares_array.c' || echo '$(srcdir)/'`dsa/ares_array.c

dsa/libcares_la-ares_htable.lo: dsa/ares_htable.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_htable.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_htable.Tpo -c -o dsa/libcares_la-ares_htable.lo `test -f 'dsa/ares_htable.c' || echo '$(srcdir)/'`dsa/ares_htable.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_htable.Tpo dsa/$(DEPDIR)/libcares_la-ares_htable.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_htable.c' object='dsa/libcares_la-ares_htable.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_htable.lo `test -f 'dsa/ares_htable.c' || echo '$(srcdir)/'`dsa/ares_htable.c

dsa/libcares_la-ares_htable_asvp.lo: dsa/ares_htable_asvp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_htable_asvp.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_htable_asvp.Tpo -c -o dsa/libcares_la-ares_htable_asvp.lo `test -f 'dsa/ares_htable_asvp.c' || echo '$(srcdir)/'`dsa/ares_htable_asvp.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_htable_asvp.Tpo dsa/$(DEPDIR)/libcares_la-ares_htable_asvp.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_htable_asvp.c' object='dsa/libcares_la-ares_htable_asvp.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_htable_asvp.lo `test -f 'dsa/ares_htable_asvp.c' || echo '$(srcdir)/'`dsa/ares_htable_asvp.c

dsa/libcares_la-ares_htable_dict.lo: dsa/ares_htable_dict.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_htable_dict.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_htable_dict.Tpo -c -o dsa/libcares_la-ares_htable_dict.lo `test -f 'dsa/ares_htable_dict.c' || echo '$(srcdir)/'`dsa/ares_htable_dict.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_htable_dict.Tpo dsa/$(DEPDIR)/libcares_la-ares_htable_dict.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_htable_dict.c' object='dsa/libcares_la-ares_htable_dict.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_htable_dict.lo `test -f 'dsa/ares_htable_dict.c' || echo '$(srcdir)/'`dsa/ares_htable_dict.c

dsa/libcares_la-ares_htable_strvp.lo: dsa/ares_htable_strvp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_htable_strvp.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_htable_strvp.Tpo -c -o dsa/libcares_la-ares_htable_strvp.lo `test -f 'dsa/ares_htable_strvp.c' || echo '$(srcdir)/'`dsa/ares_htable_strvp.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_htable_strvp.Tpo dsa/$(DEPDIR)/libcares_la-ares_htable_strvp.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_htable_strvp.c' object='dsa/libcares_la-ares_htable_strvp.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_htable_strvp.lo `test -f 'dsa/ares_htable_strvp.c' || echo '$(srcdir)/'`dsa/ares_htable_strvp.c

dsa/libcares_la-ares_htable_szvp.lo: dsa/ares_htable_szvp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_htable_szvp.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_htable_szvp.Tpo -c -o dsa/libcares_la-ares_htable_szvp.lo `test -f 'dsa/ares_htable_szvp.c' || echo '$(srcdir)/'`dsa/ares_htable_szvp.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_htable_szvp.Tpo dsa/$(DEPDIR)/libcares_la-ares_htable_szvp.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_htable_szvp.c' object='dsa/libcares_la-ares_htable_szvp.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_htable_szvp.lo `test -f 'dsa/ares_htable_szvp.c' || echo '$(srcdir)/'`dsa/ares_htable_szvp.c

dsa/libcares_la-ares_htable_vpstr.lo: dsa/ares_htable_vpstr.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_htable_vpstr.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_htable_vpstr.Tpo -c -o dsa/libcares_la-ares_htable_vpstr.lo `test -f 'dsa/ares_htable_vpstr.c' || echo '$(srcdir)/'`dsa/ares_htable_vpstr.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_htable_vpstr.Tpo dsa/$(DEPDIR)/libcares_la-ares_htable_vpstr.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_htable_vpstr.c' object='dsa/libcares_la-ares_htable_vpstr.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_htable_vpstr.lo `test -f 'dsa/ares_htable_vpstr.c' || echo '$(srcdir)/'`dsa/ares_htable_vpstr.c

dsa/libcares_la-ares_htable_vpvp.lo: dsa/ares_htable_vpvp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_htable_vpvp.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_htable_vpvp.Tpo -c -o dsa/libcares_la-ares_htable_vpvp.lo `test -f 'dsa/ares_htable_vpvp.c' || echo '$(srcdir)/'`dsa/ares_htable_vpvp.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_htable_vpvp.Tpo dsa/$(DEPDIR)/libcares_la-ares_htable_vpvp.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_htable_vpvp.c' object='dsa/libcares_la-ares_htable_vpvp.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_htable_vpvp.lo `test -f 'dsa/ares_htable_vpvp.c' || echo '$(srcdir)/'`dsa/ares_htable_vpvp.c

dsa/libcares_la-ares_llist.lo: dsa/ares_llist.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_llist.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_llist.Tpo -c -o dsa/libcares_la-ares_llist.lo `test -f 'dsa/ares_llist.c' || echo '$(srcdir)/'`dsa/ares_llist.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_llist.Tpo dsa/$(DEPDIR)/libcares_la-ares_llist.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_llist.c' object='dsa/libcares_la-ares_llist.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_llist.lo `test -f 'dsa/ares_llist.c' || echo '$(srcdir)/'`dsa/ares_llist.c

dsa/libcares_la-ares_slist.lo: dsa/ares_slist.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT dsa/libcares_la-ares_slist.lo -MD -MP -MF dsa/$(DEPDIR)/libcares_la-ares_slist.Tpo -c -o dsa/libcares_la-ares_slist.lo `test -f 'dsa/ares_slist.c' || echo '$(srcdir)/'`dsa/ares_slist.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dsa/$(DEPDIR)/libcares_la-ares_slist.Tpo dsa/$(DEPDIR)/libcares_la-ares_slist.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dsa/ares_slist.c' object='dsa/libcares_la-ares_slist.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o dsa/libcares_la-ares_slist.lo `test -f 'dsa/ares_slist.c' || echo '$(srcdir)/'`dsa/ares_slist.c

event/libcares_la-ares_event_configchg.lo: event/ares_event_configchg.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT event/libcares_la-ares_event_configchg.lo -MD -MP -MF event/$(DEPDIR)/libcares_la-ares_event_configchg.Tpo -c -o event/libcares_la-ares_event_configchg.lo `test -f 'event/ares_event_configchg.c' || echo '$(srcdir)/'`event/ares_event_configchg.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) event/$(DEPDIR)/libcares_la-ares_event_configchg.Tpo event/$(DEPDIR)/libcares_la-ares_event_configchg.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='event/ares_event_configchg.c' object='event/libcares_la-ares_event_configchg.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o event/libcares_la-ares_event_configchg.lo `test -f 'event/ares_event_configchg.c' || echo '$(srcdir)/'`event/ares_event_configchg.c

event/libcares_la-ares_event_epoll.lo: event/ares_event_epoll.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT event/libcares_la-ares_event_epoll.lo -MD -MP -MF event/$(DEPDIR)/libcares_la-ares_event_epoll.Tpo -c -o event/libcares_la-ares_event_epoll.lo `test -f 'event/ares_event_epoll.c' || echo '$(srcdir)/'`event/ares_event_epoll.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) event/$(DEPDIR)/libcares_la-ares_event_epoll.Tpo event/$(DEPDIR)/libcares_la-ares_event_epoll.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='event/ares_event_epoll.c' object='event/libcares_la-ares_event_epoll.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o event/libcares_la-ares_event_epoll.lo `test -f 'event/ares_event_epoll.c' || echo '$(srcdir)/'`event/ares_event_epoll.c

event/libcares_la-ares_event_kqueue.lo: event/ares_event_kqueue.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT event/libcares_la-ares_event_kqueue.lo -MD -MP -MF event/$(DEPDIR)/libcares_la-ares_event_kqueue.Tpo -c -o event/libcares_la-ares_event_kqueue.lo `test -f 'event/ares_event_kqueue.c' || echo '$(srcdir)/'`event/ares_event_kqueue.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) event/$(DEPDIR)/libcares_la-ares_event_kqueue.Tpo event/$(DEPDIR)/libcares_la-ares_event_kqueue.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='event/ares_event_kqueue.c' object='event/libcares_la-ares_event_kqueue.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o event/libcares_la-ares_event_kqueue.lo `test -f 'event/ares_event_kqueue.c' || echo '$(srcdir)/'`event/ares_event_kqueue.c

event/libcares_la-ares_event_poll.lo: event/ares_event_poll.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT event/libcares_la-ares_event_poll.lo -MD -MP -MF event/$(DEPDIR)/libcares_la-ares_event_poll.Tpo -c -o event/libcares_la-ares_event_poll.lo `test -f 'event/ares_event_poll.c' || echo '$(srcdir)/'`event/ares_event_poll.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) event/$(DEPDIR)/libcares_la-ares_event_poll.Tpo event/$(DEPDIR)/libcares_la-ares_event_poll.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='event/ares_event_poll.c' object='event/libcares_la-ares_event_poll.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o event/libcares_la-ares_event_poll.lo `test -f 'event/ares_event_poll.c' || echo '$(srcdir)/'`event/ares_event_poll.c

event/libcares_la-ares_event_select.lo: event/ares_event_select.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT event/libcares_la-ares_event_select.lo -MD -MP -MF event/$(DEPDIR)/libcares_la-ares_event_select.Tpo -c -o event/libcares_la-ares_event_select.lo `test -f 'event/ares_event_select.c' || echo '$(srcdir)/'`event/ares_event_select.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) event/$(DEPDIR)/libcares_la-ares_event_select.Tpo event/$(DEPDIR)/libcares_la-ares_event_select.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='event/ares_event_select.c' object='event/libcares_la-ares_event_select.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o event/libcares_la-ares_event_select.lo `test -f 'event/ares_event_select.c' || echo '$(srcdir)/'`event/ares_event_select.c

event/libcares_la-ares_event_thread.lo: event/ares_event_thread.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT event/libcares_la-ares_event_thread.lo -MD -MP -MF event/$(DEPDIR)/libcares_la-ares_event_thread.Tpo -c -o event/libcares_la-ares_event_thread.lo `test -f 'event/ares_event_thread.c' || echo '$(srcdir)/'`event/ares_event_thread.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) event/$(DEPDIR)/libcares_la-ares_event_thread.Tpo event/$(DEPDIR)/libcares_la-ares_event_thread.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='event/ares_event_thread.c' object='event/libcares_la-ares_event_thread.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o event/libcares_la-ares_event_thread.lo `test -f 'event/ares_event_thread.c' || echo '$(srcdir)/'`event/ares_event_thread.c

event/libcares_la-ares_event_wake_pipe.lo: event/ares_event_wake_pipe.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT event/libcares_la-ares_event_wake_pipe.lo -MD -MP -MF event/$(DEPDIR)/libcares_la-ares_event_wake_pipe.Tpo -c -o event/libcares_la-ares_event_wake_pipe.lo `test -f 'event/ares_event_wake_pipe.c' || echo '$(srcdir)/'`event/ares_event_wake_pipe.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) event/$(DEPDIR)/libcares_la-ares_event_wake_pipe.Tpo event/$(DEPDIR)/libcares_la-ares_event_wake_pipe.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='event/ares_event_wake_pipe.c' object='event/libcares_la-ares_event_wake_pipe.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o event/libcares_la-ares_event_wake_pipe.lo `test -f 'event/ares_event_wake_pipe.c' || echo '$(srcdir)/'`event/ares_event_wake_pipe.c

event/libcares_la-ares_event_win32.lo: event/ares_event_win32.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT event/libcares_la-ares_event_win32.lo -MD -MP -MF event/$(DEPDIR)/libcares_la-ares_event_win32.Tpo -c -o event/libcares_la-ares_event_win32.lo `test -f 'event/ares_event_win32.c' || echo '$(srcdir)/'`event/ares_event_win32.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) event/$(DEPDIR)/libcares_la-ares_event_win32.Tpo event/$(DEPDIR)/libcares_la-ares_event_win32.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='event/ares_event_win32.c' object='event/libcares_la-ares_event_win32.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o event/libcares_la-ares_event_win32.lo `test -f 'event/ares_event_win32.c' || echo '$(srcdir)/'`event/ares_event_win32.c

legacy/libcares_la-ares_create_query.lo: legacy/ares_create_query.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_create_query.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_create_query.Tpo -c -o legacy/libcares_la-ares_create_query.lo `test -f 'legacy/ares_create_query.c' || echo '$(srcdir)/'`legacy/ares_create_query.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_create_query.Tpo legacy/$(DEPDIR)/libcares_la-ares_create_query.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_create_query.c' object='legacy/libcares_la-ares_create_query.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_create_query.lo `test -f 'legacy/ares_create_query.c' || echo '$(srcdir)/'`legacy/ares_create_query.c

legacy/libcares_la-ares_expand_name.lo: legacy/ares_expand_name.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_expand_name.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_expand_name.Tpo -c -o legacy/libcares_la-ares_expand_name.lo `test -f 'legacy/ares_expand_name.c' || echo '$(srcdir)/'`legacy/ares_expand_name.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_expand_name.Tpo legacy/$(DEPDIR)/libcares_la-ares_expand_name.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_expand_name.c' object='legacy/libcares_la-ares_expand_name.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_expand_name.lo `test -f 'legacy/ares_expand_name.c' || echo '$(srcdir)/'`legacy/ares_expand_name.c

legacy/libcares_la-ares_expand_string.lo: legacy/ares_expand_string.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_expand_string.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_expand_string.Tpo -c -o legacy/libcares_la-ares_expand_string.lo `test -f 'legacy/ares_expand_string.c' || echo '$(srcdir)/'`legacy/ares_expand_string.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_expand_string.Tpo legacy/$(DEPDIR)/libcares_la-ares_expand_string.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_expand_string.c' object='legacy/libcares_la-ares_expand_string.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_expand_string.lo `test -f 'legacy/ares_expand_string.c' || echo '$(srcdir)/'`legacy/ares_expand_string.c

legacy/libcares_la-ares_fds.lo: legacy/ares_fds.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_fds.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_fds.Tpo -c -o legacy/libcares_la-ares_fds.lo `test -f 'legacy/ares_fds.c' || echo '$(srcdir)/'`legacy/ares_fds.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_fds.Tpo legacy/$(DEPDIR)/libcares_la-ares_fds.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_fds.c' object='legacy/libcares_la-ares_fds.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_fds.lo `test -f 'legacy/ares_fds.c' || echo '$(srcdir)/'`legacy/ares_fds.c

legacy/libcares_la-ares_getsock.lo: legacy/ares_getsock.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_getsock.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_getsock.Tpo -c -o legacy/libcares_la-ares_getsock.lo `test -f 'legacy/ares_getsock.c' || echo '$(srcdir)/'`legacy/ares_getsock.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_getsock.Tpo legacy/$(DEPDIR)/libcares_la-ares_getsock.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_getsock.c' object='legacy/libcares_la-ares_getsock.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_getsock.lo `test -f 'legacy/ares_getsock.c' || echo '$(srcdir)/'`legacy/ares_getsock.c

legacy/libcares_la-ares_parse_a_reply.lo: legacy/ares_parse_a_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_a_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_a_reply.Tpo -c -o legacy/libcares_la-ares_parse_a_reply.lo `test -f 'legacy/ares_parse_a_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_a_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_a_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_a_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_a_reply.c' object='legacy/libcares_la-ares_parse_a_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_a_reply.lo `test -f 'legacy/ares_parse_a_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_a_reply.c

legacy/libcares_la-ares_parse_aaaa_reply.lo: legacy/ares_parse_aaaa_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_aaaa_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_aaaa_reply.Tpo -c -o legacy/libcares_la-ares_parse_aaaa_reply.lo `test -f 'legacy/ares_parse_aaaa_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_aaaa_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_aaaa_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_aaaa_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_aaaa_reply.c' object='legacy/libcares_la-ares_parse_aaaa_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_aaaa_reply.lo `test -f 'legacy/ares_parse_aaaa_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_aaaa_reply.c

legacy/libcares_la-ares_parse_caa_reply.lo: legacy/ares_parse_caa_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_caa_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_caa_reply.Tpo -c -o legacy/libcares_la-ares_parse_caa_reply.lo `test -f 'legacy/ares_parse_caa_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_caa_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_caa_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_caa_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_caa_reply.c' object='legacy/libcares_la-ares_parse_caa_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_caa_reply.lo `test -f 'legacy/ares_parse_caa_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_caa_reply.c

legacy/libcares_la-ares_parse_mx_reply.lo: legacy/ares_parse_mx_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_mx_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_mx_reply.Tpo -c -o legacy/libcares_la-ares_parse_mx_reply.lo `test -f 'legacy/ares_parse_mx_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_mx_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_mx_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_mx_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_mx_reply.c' object='legacy/libcares_la-ares_parse_mx_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_mx_reply.lo `test -f 'legacy/ares_parse_mx_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_mx_reply.c

legacy/libcares_la-ares_parse_naptr_reply.lo: legacy/ares_parse_naptr_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_naptr_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_naptr_reply.Tpo -c -o legacy/libcares_la-ares_parse_naptr_reply.lo `test -f 'legacy/ares_parse_naptr_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_naptr_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_naptr_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_naptr_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_naptr_reply.c' object='legacy/libcares_la-ares_parse_naptr_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_naptr_reply.lo `test -f 'legacy/ares_parse_naptr_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_naptr_reply.c

legacy/libcares_la-ares_parse_ns_reply.lo: legacy/ares_parse_ns_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_ns_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_ns_reply.Tpo -c -o legacy/libcares_la-ares_parse_ns_reply.lo `test -f 'legacy/ares_parse_ns_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_ns_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_ns_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_ns_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_ns_reply.c' object='legacy/libcares_la-ares_parse_ns_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_ns_reply.lo `test -f 'legacy/ares_parse_ns_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_ns_reply.c

legacy/libcares_la-ares_parse_ptr_reply.lo: legacy/ares_parse_ptr_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_ptr_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_ptr_reply.Tpo -c -o legacy/libcares_la-ares_parse_ptr_reply.lo `test -f 'legacy/ares_parse_ptr_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_ptr_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_ptr_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_ptr_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_ptr_reply.c' object='legacy/libcares_la-ares_parse_ptr_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_ptr_reply.lo `test -f 'legacy/ares_parse_ptr_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_ptr_reply.c

legacy/libcares_la-ares_parse_soa_reply.lo: legacy/ares_parse_soa_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_soa_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_soa_reply.Tpo -c -o legacy/libcares_la-ares_parse_soa_reply.lo `test -f 'legacy/ares_parse_soa_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_soa_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_soa_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_soa_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_soa_reply.c' object='legacy/libcares_la-ares_parse_soa_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_soa_reply.lo `test -f 'legacy/ares_parse_soa_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_soa_reply.c

legacy/libcares_la-ares_parse_srv_reply.lo: legacy/ares_parse_srv_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_srv_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_srv_reply.Tpo -c -o legacy/libcares_la-ares_parse_srv_reply.lo `test -f 'legacy/ares_parse_srv_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_srv_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_srv_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_srv_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_srv_reply.c' object='legacy/libcares_la-ares_parse_srv_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_srv_reply.lo `test -f 'legacy/ares_parse_srv_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_srv_reply.c

legacy/libcares_la-ares_parse_txt_reply.lo: legacy/ares_parse_txt_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_txt_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_txt_reply.Tpo -c -o legacy/libcares_la-ares_parse_txt_reply.lo `test -f 'legacy/ares_parse_txt_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_txt_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_txt_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_txt_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_txt_reply.c' object='legacy/libcares_la-ares_parse_txt_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_txt_reply.lo `test -f 'legacy/ares_parse_txt_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_txt_reply.c

legacy/libcares_la-ares_parse_uri_reply.lo: legacy/ares_parse_uri_reply.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT legacy/libcares_la-ares_parse_uri_reply.lo -MD -MP -MF legacy/$(DEPDIR)/libcares_la-ares_parse_uri_reply.Tpo -c -o legacy/libcares_la-ares_parse_uri_reply.lo `test -f 'legacy/ares_parse_uri_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_uri_reply.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) legacy/$(DEPDIR)/libcares_la-ares_parse_uri_reply.Tpo legacy/$(DEPDIR)/libcares_la-ares_parse_uri_reply.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='legacy/ares_parse_uri_reply.c' object='legacy/libcares_la-ares_parse_uri_reply.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o legacy/libcares_la-ares_parse_uri_reply.lo `test -f 'legacy/ares_parse_uri_reply.c' || echo '$(srcdir)/'`legacy/ares_parse_uri_reply.c

record/libcares_la-ares_dns_mapping.lo: record/ares_dns_mapping.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT record/libcares_la-ares_dns_mapping.lo -MD -MP -MF record/$(DEPDIR)/libcares_la-ares_dns_mapping.Tpo -c -o record/libcares_la-ares_dns_mapping.lo `test -f 'record/ares_dns_mapping.c' || echo '$(srcdir)/'`record/ares_dns_mapping.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) record/$(DEPDIR)/libcares_la-ares_dns_mapping.Tpo record/$(DEPDIR)/libcares_la-ares_dns_mapping.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='record/ares_dns_mapping.c' object='record/libcares_la-ares_dns_mapping.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o record/libcares_la-ares_dns_mapping.lo `test -f 'record/ares_dns_mapping.c' || echo '$(srcdir)/'`record/ares_dns_mapping.c

record/libcares_la-ares_dns_multistring.lo: record/ares_dns_multistring.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT record/libcares_la-ares_dns_multistring.lo -MD -MP -MF record/$(DEPDIR)/libcares_la-ares_dns_multistring.Tpo -c -o record/libcares_la-ares_dns_multistring.lo `test -f 'record/ares_dns_multistring.c' || echo '$(srcdir)/'`record/ares_dns_multistring.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) record/$(DEPDIR)/libcares_la-ares_dns_multistring.Tpo record/$(DEPDIR)/libcares_la-ares_dns_multistring.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='record/ares_dns_multistring.c' object='record/libcares_la-ares_dns_multistring.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o record/libcares_la-ares_dns_multistring.lo `test -f 'record/ares_dns_multistring.c' || echo '$(srcdir)/'`record/ares_dns_multistring.c

record/libcares_la-ares_dns_name.lo: record/ares_dns_name.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT record/libcares_la-ares_dns_name.lo -MD -MP -MF record/$(DEPDIR)/libcares_la-ares_dns_name.Tpo -c -o record/libcares_la-ares_dns_name.lo `test -f 'record/ares_dns_name.c' || echo '$(srcdir)/'`record/ares_dns_name.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) record/$(DEPDIR)/libcares_la-ares_dns_name.Tpo record/$(DEPDIR)/libcares_la-ares_dns_name.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='record/ares_dns_name.c' object='record/libcares_la-ares_dns_name.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o record/libcares_la-ares_dns_name.lo `test -f 'record/ares_dns_name.c' || echo '$(srcdir)/'`record/ares_dns_name.c

record/libcares_la-ares_dns_parse.lo: record/ares_dns_parse.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT record/libcares_la-ares_dns_parse.lo -MD -MP -MF record/$(DEPDIR)/libcares_la-ares_dns_parse.Tpo -c -o record/libcares_la-ares_dns_parse.lo `test -f 'record/ares_dns_parse.c' || echo '$(srcdir)/'`record/ares_dns_parse.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) record/$(DEPDIR)/libcares_la-ares_dns_parse.Tpo record/$(DEPDIR)/libcares_la-ares_dns_parse.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='record/ares_dns_parse.c' object='record/libcares_la-ares_dns_parse.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o record/libcares_la-ares_dns_parse.lo `test -f 'record/ares_dns_parse.c' || echo '$(srcdir)/'`record/ares_dns_parse.c

record/libcares_la-ares_dns_record.lo: record/ares_dns_record.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT record/libcares_la-ares_dns_record.lo -MD -MP -MF record/$(DEPDIR)/libcares_la-ares_dns_record.Tpo -c -o record/libcares_la-ares_dns_record.lo `test -f 'record/ares_dns_record.c' || echo '$(srcdir)/'`record/ares_dns_record.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) record/$(DEPDIR)/libcares_la-ares_dns_record.Tpo record/$(DEPDIR)/libcares_la-ares_dns_record.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='record/ares_dns_record.c' object='record/libcares_la-ares_dns_record.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o record/libcares_la-ares_dns_record.lo `test -f 'record/ares_dns_record.c' || echo '$(srcdir)/'`record/ares_dns_record.c

record/libcares_la-ares_dns_write.lo: record/ares_dns_write.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT record/libcares_la-ares_dns_write.lo -MD -MP -MF record/$(DEPDIR)/libcares_la-ares_dns_write.Tpo -c -o record/libcares_la-ares_dns_write.lo `test -f 'record/ares_dns_write.c' || echo '$(srcdir)/'`record/ares_dns_write.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) record/$(DEPDIR)/libcares_la-ares_dns_write.Tpo record/$(DEPDIR)/libcares_la-ares_dns_write.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='record/ares_dns_write.c' object='record/libcares_la-ares_dns_write.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o record/libcares_la-ares_dns_write.lo `test -f 'record/ares_dns_write.c' || echo '$(srcdir)/'`record/ares_dns_write.c

str/libcares_la-ares_buf.lo: str/ares_buf.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT str/libcares_la-ares_buf.lo -MD -MP -MF str/$(DEPDIR)/libcares_la-ares_buf.Tpo -c -o str/libcares_la-ares_buf.lo `test -f 'str/ares_buf.c' || echo '$(srcdir)/'`str/ares_buf.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) str/$(DEPDIR)/libcares_la-ares_buf.Tpo str/$(DEPDIR)/libcares_la-ares_buf.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='str/ares_buf.c' object='str/libcares_la-ares_buf.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o str/libcares_la-ares_buf.lo `test -f 'str/ares_buf.c' || echo '$(srcdir)/'`str/ares_buf.c

str/libcares_la-ares_str.lo: str/ares_str.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT str/libcares_la-ares_str.lo -MD -MP -MF str/$(DEPDIR)/libcares_la-ares_str.Tpo -c -o str/libcares_la-ares_str.lo `test -f 'str/ares_str.c' || echo '$(srcdir)/'`str/ares_str.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) str/$(DEPDIR)/libcares_la-ares_str.Tpo str/$(DEPDIR)/libcares_la-ares_str.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='str/ares_str.c' object='str/libcares_la-ares_str.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o str/libcares_la-ares_str.lo `test -f 'str/ares_str.c' || echo '$(srcdir)/'`str/ares_str.c

str/libcares_la-ares_strsplit.lo: str/ares_strsplit.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT str/libcares_la-ares_strsplit.lo -MD -MP -MF str/$(DEPDIR)/libcares_la-ares_strsplit.Tpo -c -o str/libcares_la-ares_strsplit.lo `test -f 'str/ares_strsplit.c' || echo '$(srcdir)/'`str/ares_strsplit.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) str/$(DEPDIR)/libcares_la-ares_strsplit.Tpo str/$(DEPDIR)/libcares_la-ares_strsplit.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='str/ares_strsplit.c' object='str/libcares_la-ares_strsplit.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o str/libcares_la-ares_strsplit.lo `test -f 'str/ares_strsplit.c' || echo '$(srcdir)/'`str/ares_strsplit.c

util/libcares_la-ares_iface_ips.lo: util/ares_iface_ips.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT util/libcares_la-ares_iface_ips.lo -MD -MP -MF util/$(DEPDIR)/libcares_la-ares_iface_ips.Tpo -c -o util/libcares_la-ares_iface_ips.lo `test -f 'util/ares_iface_ips.c' || echo '$(srcdir)/'`util/ares_iface_ips.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) util/$(DEPDIR)/libcares_la-ares_iface_ips.Tpo util/$(DEPDIR)/libcares_la-ares_iface_ips.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='util/ares_iface_ips.c' object='util/libcares_la-ares_iface_ips.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o util/libcares_la-ares_iface_ips.lo `test -f 'util/ares_iface_ips.c' || echo '$(srcdir)/'`util/ares_iface_ips.c

util/libcares_la-ares_threads.lo: util/ares_threads.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT util/libcares_la-ares_threads.lo -MD -MP -MF util/$(DEPDIR)/libcares_la-ares_threads.Tpo -c -o util/libcares_la-ares_threads.lo `test -f 'util/ares_threads.c' || echo '$(srcdir)/'`util/ares_threads.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) util/$(DEPDIR)/libcares_la-ares_threads.Tpo util/$(DEPDIR)/libcares_la-ares_threads.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='util/ares_threads.c' object='util/libcares_la-ares_threads.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o util/libcares_la-ares_threads.lo `test -f 'util/ares_threads.c' || echo '$(srcdir)/'`util/ares_threads.c

util/libcares_la-ares_timeval.lo: util/ares_timeval.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT util/libcares_la-ares_timeval.lo -MD -MP -MF util/$(DEPDIR)/libcares_la-ares_timeval.Tpo -c -o util/libcares_la-ares_timeval.lo `test -f 'util/ares_timeval.c' || echo '$(srcdir)/'`util/ares_timeval.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) util/$(DEPDIR)/libcares_la-ares_timeval.Tpo util/$(DEPDIR)/libcares_la-ares_timeval.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='util/ares_timeval.c' object='util/libcares_la-ares_timeval.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o util/libcares_la-ares_timeval.lo `test -f 'util/ares_timeval.c' || echo '$(srcdir)/'`util/ares_timeval.c

util/libcares_la-ares_math.lo: util/ares_math.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT util/libcares_la-ares_math.lo -MD -MP -MF util/$(DEPDIR)/libcares_la-ares_math.Tpo -c -o util/libcares_la-ares_math.lo `test -f 'util/ares_math.c' || echo '$(srcdir)/'`util/ares_math.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) util/$(DEPDIR)/libcares_la-ares_math.Tpo util/$(DEPDIR)/libcares_la-ares_math.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='util/ares_math.c' object='util/libcares_la-ares_math.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o util/libcares_la-ares_math.lo `test -f 'util/ares_math.c' || echo '$(srcdir)/'`util/ares_math.c

util/libcares_la-ares_rand.lo: util/ares_rand.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT util/libcares_la-ares_rand.lo -MD -MP -MF util/$(DEPDIR)/libcares_la-ares_rand.Tpo -c -o util/libcares_la-ares_rand.lo `test -f 'util/ares_rand.c' || echo '$(srcdir)/'`util/ares_rand.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) util/$(DEPDIR)/libcares_la-ares_rand.Tpo util/$(DEPDIR)/libcares_la-ares_rand.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='util/ares_rand.c' object='util/libcares_la-ares_rand.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o util/libcares_la-ares_rand.lo `test -f 'util/ares_rand.c' || echo '$(srcdir)/'`util/ares_rand.c

util/libcares_la-ares_uri.lo: util/ares_uri.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -MT util/libcares_la-ares_uri.lo -MD -MP -MF util/$(DEPDIR)/libcares_la-ares_uri.Tpo -c -o util/libcares_la-ares_uri.lo `test -f 'util/ares_uri.c' || echo '$(srcdir)/'`util/ares_uri.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) util/$(DEPDIR)/libcares_la-ares_uri.Tpo util/$(DEPDIR)/libcares_la-ares_uri.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='util/ares_uri.c' object='util/libcares_la-ares_uri.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libcares_la_CPPFLAGS) $(CPPFLAGS) $(libcares_la_CFLAGS) $(CFLAGS) -c -o util/libcares_la-ares_uri.lo `test -f 'util/ares_uri.c' || echo '$(srcdir)/'`util/ares_uri.c

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf dsa/.libs dsa/_libs
	-rm -rf event/.libs event/_libs
	-rm -rf legacy/.libs legacy/_libs
	-rm -rf record/.libs record/_libs
	-rm -rf str/.libs str/_libs
	-rm -rf util/.libs util/_libs

# This directory's subdirectories are mostly independent; you can cd
# into them and run 'make' without going through this Makefile.
# To change the values of 'make' variables: instead of editing Makefiles,
# (1) if the variable is set in 'config.status', edit 'config.status'
#     (which will cause the Makefiles to be regenerated when you run 'make');
# (2) otherwise, pass the desired values on the 'make' command line.
$(am__recursive_targets):
	@fail=; \
	if $(am__make_keepgoing); then \
	  failcom='fail=yes'; \
	else \
	  failcom='exit 1'; \
	fi; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-recursive
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-recursive

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-recursive

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    $(am__make_dryrun) \
	      || test -d "$(distdir)/$$subdir" \
	      || $(MKDIR_P) "$(distdir)/$$subdir" \
	      || exit 1; \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-recursive
all-am: Makefile $(LTLIBRARIES) ares_config.h
installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(libdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f dsa/$(DEPDIR)/$(am__dirstamp)
	-rm -f dsa/$(am__dirstamp)
	-rm -f event/$(DEPDIR)/$(am__dirstamp)
	-rm -f event/$(am__dirstamp)
	-rm -f legacy/$(DEPDIR)/$(am__dirstamp)
	-rm -f legacy/$(am__dirstamp)
	-rm -f record/$(DEPDIR)/$(am__dirstamp)
	-rm -f record/$(am__dirstamp)
	-rm -f str/$(DEPDIR)/$(am__dirstamp)
	-rm -f str/$(am__dirstamp)
	-rm -f util/$(DEPDIR)/$(am__dirstamp)
	-rm -f util/$(am__dirstamp)
	-test -z "$(DISTCLEANFILES)" || rm -f $(DISTCLEANFILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-recursive

clean-am: clean-generic clean-libLTLIBRARIES clean-libtool \
	mostlyclean-am

distclean: distclean-recursive
		-rm -f ./$(DEPDIR)/libcares_la-ares_addrinfo2hostent.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_addrinfo_localhost.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_android.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_cancel.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_close_sockets.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_conn.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_cookie.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_data.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_destroy.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_free_hostent.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_free_string.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_freeaddrinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_getaddrinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_getenv.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_gethostbyaddr.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_gethostbyname.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_getnameinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_hosts_file.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_init.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_library_init.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_metrics.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_options.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_parse_into_addrinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_process.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_qcache.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_query.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_search.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_send.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_set_socket_functions.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_socket.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sortaddrinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_strerror.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sysconfig.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sysconfig_files.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sysconfig_mac.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sysconfig_win.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_timeout.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_update_servers.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_version.Plo
	-rm -f ./$(DEPDIR)/libcares_la-inet_net_pton.Plo
	-rm -f ./$(DEPDIR)/libcares_la-inet_ntop.Plo
	-rm -f ./$(DEPDIR)/libcares_la-windows_port.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_array.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_asvp.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_dict.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_strvp.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_szvp.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_vpstr.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_vpvp.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_llist.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_slist.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_configchg.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_epoll.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_kqueue.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_poll.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_select.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_thread.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_wake_pipe.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_win32.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_create_query.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_expand_name.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_expand_string.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_fds.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_getsock.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_a_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_aaaa_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_caa_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_mx_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_naptr_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_ns_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_ptr_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_soa_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_srv_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_txt_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_uri_reply.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_mapping.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_multistring.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_name.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_parse.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_record.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_write.Plo
	-rm -f str/$(DEPDIR)/libcares_la-ares_buf.Plo
	-rm -f str/$(DEPDIR)/libcares_la-ares_str.Plo
	-rm -f str/$(DEPDIR)/libcares_la-ares_strsplit.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_iface_ips.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_math.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_rand.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_threads.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_timeval.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_uri.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-hdr distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

html-am:

info: info-recursive

info-am:

install-data-am:

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am: install-libLTLIBRARIES

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man:

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
		-rm -f ./$(DEPDIR)/libcares_la-ares_addrinfo2hostent.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_addrinfo_localhost.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_android.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_cancel.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_close_sockets.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_conn.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_cookie.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_data.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_destroy.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_free_hostent.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_free_string.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_freeaddrinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_getaddrinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_getenv.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_gethostbyaddr.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_gethostbyname.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_getnameinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_hosts_file.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_init.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_library_init.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_metrics.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_options.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_parse_into_addrinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_process.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_qcache.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_query.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_search.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_send.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_set_socket_functions.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_socket.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sortaddrinfo.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_strerror.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sysconfig.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sysconfig_files.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sysconfig_mac.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_sysconfig_win.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_timeout.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_update_servers.Plo
	-rm -f ./$(DEPDIR)/libcares_la-ares_version.Plo
	-rm -f ./$(DEPDIR)/libcares_la-inet_net_pton.Plo
	-rm -f ./$(DEPDIR)/libcares_la-inet_ntop.Plo
	-rm -f ./$(DEPDIR)/libcares_la-windows_port.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_array.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_asvp.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_dict.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_strvp.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_szvp.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_vpstr.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_htable_vpvp.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_llist.Plo
	-rm -f dsa/$(DEPDIR)/libcares_la-ares_slist.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_configchg.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_epoll.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_kqueue.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_poll.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_select.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_thread.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_wake_pipe.Plo
	-rm -f event/$(DEPDIR)/libcares_la-ares_event_win32.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_create_query.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_expand_name.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_expand_string.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_fds.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_getsock.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_a_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_aaaa_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_caa_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_mx_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_naptr_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_ns_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_ptr_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_soa_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_srv_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_txt_reply.Plo
	-rm -f legacy/$(DEPDIR)/libcares_la-ares_parse_uri_reply.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_mapping.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_multistring.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_name.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_parse.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_record.Plo
	-rm -f record/$(DEPDIR)/libcares_la-ares_dns_write.Plo
	-rm -f str/$(DEPDIR)/libcares_la-ares_buf.Plo
	-rm -f str/$(DEPDIR)/libcares_la-ares_str.Plo
	-rm -f str/$(DEPDIR)/libcares_la-ares_strsplit.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_iface_ips.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_math.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_rand.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_threads.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_timeval.Plo
	-rm -f util/$(DEPDIR)/libcares_la-ares_uri.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-libLTLIBRARIES

.MAKE: $(am__recursive_targets) all install-am install-strip

.PHONY: $(am__recursive_targets) CTAGS GTAGS TAGS all all-am \
	am--depfiles check check-am clean clean-generic \
	clean-libLTLIBRARIES clean-libtool cscopelist-am ctags \
	ctags-am distclean distclean-compile distclean-generic \
	distclean-hdr distclean-libtool distclean-tags distdir dvi \
	dvi-am html html-am info info-am install install-am \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-libLTLIBRARIES \
	install-man install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs installdirs-am maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am uninstall-libLTLIBRARIES

.PRECIOUS: Makefile


# Code coverage
#
# Optional:
#  - CODE_COVERAGE_DIRECTORY: Top-level directory for code coverage reporting.
#    Multiple directories may be specified, separated by whitespace.
#    (Default: $(top_builddir))
#  - CODE_COVERAGE_OUTPUT_FILE: Filename and path for the .info file generated
#    by lcov for code coverage. (Default:
#    $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage.info)
#  - CODE_COVERAGE_OUTPUT_DIRECTORY: Directory for generated code coverage
#    reports to be created. (Default:
#    $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage)
#  - CODE_COVERAGE_BRANCH_COVERAGE: Set to 1 to enforce branch coverage,
#    set to 0 to disable it and leave empty to stay with the default.
#    (Default: empty)
#  - CODE_COVERAGE_LCOV_SHOPTS_DEFAULT: Extra options shared between both lcov
#    instances. (Default: based on )
#  - CODE_COVERAGE_LCOV_SHOPTS: Extra options to shared between both lcov
#    instances. (Default: )
#  - CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH: --gcov-tool pathtogcov
#  - CODE_COVERAGE_LCOV_OPTIONS_DEFAULT: Extra options to pass to the
#    collecting lcov instance. (Default: )
#  - CODE_COVERAGE_LCOV_OPTIONS: Extra options to pass to the collecting lcov
#    instance. (Default: )
#  - CODE_COVERAGE_LCOV_RMOPTS_DEFAULT: Extra options to pass to the filtering
#    lcov instance. (Default: empty)
#  - CODE_COVERAGE_LCOV_RMOPTS: Extra options to pass to the filtering lcov
#    instance. (Default: )
#  - CODE_COVERAGE_GENHTML_OPTIONS_DEFAULT: Extra options to pass to the
#    genhtml instance. (Default: based on )
#  - CODE_COVERAGE_GENHTML_OPTIONS: Extra options to pass to the genhtml
#    instance. (Default: )
#  - CODE_COVERAGE_IGNORE_PATTERN: Extra glob pattern of files to ignore
#
# The generated report will be titled using the $(PACKAGE_NAME) and
# $(PACKAGE_VERSION). In order to add the current git hash to the title,
# use the git-version-gen script, available online.
# Optional variables
# run only on top dir
@CODE_COVERAGE_ENABLED_TRUE@ ifeq ($(abs_builddir), $(abs_top_builddir))
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_DIRECTORY ?= $(top_builddir)
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_OUTPUT_FILE ?= $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage.info
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_OUTPUT_DIRECTORY ?= $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage

@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_BRANCH_COVERAGE ?=
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_LCOV_SHOPTS_DEFAULT ?= $(if $(CODE_COVERAGE_BRANCH_COVERAGE),--rc lcov_branch_coverage=$(CODE_COVERAGE_BRANCH_COVERAGE))
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_LCOV_SHOPTS ?= $(CODE_COVERAGE_LCOV_SHOPTS_DEFAULT)
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH ?= --gcov-tool "$(GCOV)"
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_LCOV_OPTIONS_DEFAULT ?= $(CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH)
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_LCOV_OPTIONS ?= $(CODE_COVERAGE_LCOV_OPTIONS_DEFAULT)
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_LCOV_RMOPTS_DEFAULT ?=
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_LCOV_RMOPTS ?= $(CODE_COVERAGE_LCOV_RMOPTS_DEFAULT)
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_GENHTML_OPTIONS_DEFAULT ?=$(if $(CODE_COVERAGE_BRANCH_COVERAGE),--rc genhtml_branch_coverage=$(CODE_COVERAGE_BRANCH_COVERAGE))
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_GENHTML_OPTIONS ?= $(CODE_COVERAGE_GENHTML_OPTIONS_DEFAULT)
@CODE_COVERAGE_ENABLED_TRUE@CODE_COVERAGE_IGNORE_PATTERN ?=

# Use recursive makes in order to ignore errors during check
@CODE_COVERAGE_ENABLED_TRUE@check-code-coverage:
@CODE_COVERAGE_ENABLED_TRUE@	-$(AM_V_at)$(MAKE) $(AM_MAKEFLAGS) -k check
@CODE_COVERAGE_ENABLED_TRUE@	$(AM_V_at)$(MAKE) $(AM_MAKEFLAGS) code-coverage-capture

# Capture code coverage data
@CODE_COVERAGE_ENABLED_TRUE@code-coverage-capture: code-coverage-capture-hook
@CODE_COVERAGE_ENABLED_TRUE@	$(code_coverage_v_lcov_cap)$(LCOV) $(code_coverage_quiet) $(addprefix --directory ,$(CODE_COVERAGE_DIRECTORY)) --capture --output-file "$(CODE_COVERAGE_OUTPUT_FILE).tmp" --test-name "$(call code_coverage_sanitize,$(PACKAGE_NAME)-$(PACKAGE_VERSION))" --no-checksum --compat-libtool $(CODE_COVERAGE_LCOV_SHOPTS) $(CODE_COVERAGE_LCOV_OPTIONS)
@CODE_COVERAGE_ENABLED_TRUE@	$(code_coverage_v_lcov_ign)$(LCOV) $(code_coverage_quiet) $(addprefix --directory ,$(CODE_COVERAGE_DIRECTORY)) --remove "$(CODE_COVERAGE_OUTPUT_FILE).tmp" "/tmp/*" $(CODE_COVERAGE_IGNORE_PATTERN) --output-file "$(CODE_COVERAGE_OUTPUT_FILE)" $(CODE_COVERAGE_LCOV_SHOPTS) $(CODE_COVERAGE_LCOV_RMOPTS)
@CODE_COVERAGE_ENABLED_TRUE@	-@rm -f "$(CODE_COVERAGE_OUTPUT_FILE).tmp"
@CODE_COVERAGE_ENABLED_TRUE@	$(code_coverage_v_genhtml)LANG=C $(GENHTML) $(code_coverage_quiet) $(addprefix --prefix ,$(CODE_COVERAGE_DIRECTORY)) --output-directory "$(CODE_COVERAGE_OUTPUT_DIRECTORY)" --title "$(PACKAGE_NAME)-$(PACKAGE_VERSION) Code Coverage" --legend --show-details "$(CODE_COVERAGE_OUTPUT_FILE)" $(CODE_COVERAGE_GENHTML_OPTIONS)
@CODE_COVERAGE_ENABLED_TRUE@	@echo "file://$(abs_builddir)/$(CODE_COVERAGE_OUTPUT_DIRECTORY)/index.html"

@CODE_COVERAGE_ENABLED_TRUE@code-coverage-clean:
@CODE_COVERAGE_ENABLED_TRUE@	-$(LCOV) --directory $(top_builddir) -z
@CODE_COVERAGE_ENABLED_TRUE@	-rm -rf "$(CODE_COVERAGE_OUTPUT_FILE)" "$(CODE_COVERAGE_OUTPUT_FILE).tmp" "$(CODE_COVERAGE_OUTPUT_DIRECTORY)"
@CODE_COVERAGE_ENABLED_TRUE@	-find . \( -name "*.gcda" -o -name "*.gcno" -o -name "*.gcov" \) -delete

@CODE_COVERAGE_ENABLED_TRUE@code-coverage-dist-clean:
@CODE_COVERAGE_ENABLED_TRUE@ else # ifneq ($(abs_builddir), $(abs_top_builddir))
@CODE_COVERAGE_ENABLED_TRUE@check-code-coverage:

@CODE_COVERAGE_ENABLED_TRUE@code-coverage-capture: code-coverage-capture-hook

@CODE_COVERAGE_ENABLED_TRUE@code-coverage-clean:

@CODE_COVERAGE_ENABLED_TRUE@code-coverage-dist-clean:
@CODE_COVERAGE_ENABLED_TRUE@ endif # ifeq ($(abs_builddir), $(abs_top_builddir))
# Use recursive makes in order to ignore errors during check
@CODE_COVERAGE_ENABLED_FALSE@check-code-coverage:
@CODE_COVERAGE_ENABLED_FALSE@	@echo "Need to reconfigure with --enable-code-coverage"
# Capture code coverage data
@CODE_COVERAGE_ENABLED_FALSE@code-coverage-capture: code-coverage-capture-hook
@CODE_COVERAGE_ENABLED_FALSE@	@echo "Need to reconfigure with --enable-code-coverage"

@CODE_COVERAGE_ENABLED_FALSE@code-coverage-clean:

@CODE_COVERAGE_ENABLED_FALSE@code-coverage-dist-clean:

# Hook rule executed before code-coverage-capture, overridable by the user
code-coverage-capture-hook:

.PHONY: check-code-coverage code-coverage-capture code-coverage-dist-clean code-coverage-clean code-coverage-capture-hook

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
