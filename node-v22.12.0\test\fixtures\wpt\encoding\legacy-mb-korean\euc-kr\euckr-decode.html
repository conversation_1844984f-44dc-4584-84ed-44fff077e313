<!DOCTYPE html>
<html lang="en-GB">
<head>
<meta charset="utf-8"/>
<title>EUC-KR decoding</title>
<meta name="timeout" content="long">
<meta name="variant" content="?1-1000">
<meta name="variant" content="?1001-2000">
<meta name="variant" content="?2001-3000">
<meta name="variant" content="?3001-4000">
<meta name="variant" content="?4001-5000">
<meta name="variant" content="?5001-6000">
<meta name="variant" content="?6001-7000">
<meta name="variant" content="?7001-8000">
<meta name="variant" content="?8001-9000">
<meta name="variant" content="?9001-10000">
<meta name="variant" content="?10001-11000">
<meta name="variant" content="?11001-12000">
<meta name="variant" content="?12001-13000">
<meta name="variant" content="?13001-14000">
<meta name="variant" content="?14001-15000">
<meta name="variant" content="?15001-16000">
<meta name="variant" content="?16001-17000">
<meta name="variant" content="?17001-last">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/common/subset-tests.js"></script>
<link rel="author" title="Richard Ishida" href="mailto:<EMAIL>">
<link rel="help" href="https://encoding.spec.whatwg.org/#euc-kr">
<meta name="assert" content="The browser decodes all characters as expected from a file generated by encoding all pointers in the euc-kr encoding per the encoder steps in the specification.">
<style>
 iframe { display:none }
</style>
<script src="euckr_index.js"></script>
<script src="euckr-decoder.js"></script>
<script src="../../resources/decode-common.js"></script>
</head>

<body onload="showNodes(euckrDecoder);">

<iframe src="euckr_chars.html" name="scriptWindow" id="scrwin"></iframe>

<div id="log"></div>

</body>
</html>
