// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@cppObjectDefinition
extern class RegExpMatchInfo extends HeapObject {
  macro GetStartOfCapture(i: constexpr int31): Smi {
    return this.objects[i * 2];
  }
  macro GetEndOfCapture(i: constexpr int31): Smi {
    return this.objects[i * 2 + 1];
  }

  const length: Smi;
  number_of_capture_registers: Smi;
  last_subject: String;
  last_input: Object;
  // TODO(jgruber): These could be encoded as raw int32_t values instead.
  objects[length]: Smi;
}
