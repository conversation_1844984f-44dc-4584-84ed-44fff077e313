beginning
foo.vm:1
var 4;
    ^

SyntaxError: Unexpected number
    at new Script (node:vm:*)
    at createScript (node:vm:*)
    at Object.runInThisContext (node:vm:*)
    at Object.<anonymous> (*fixtures*vm*vm_display_syntax_error.js:31:6)
test.vm:1
var 5;
    ^

SyntaxError: Unexpected number
    at new Script (node:vm:*)
    at createScript (node:vm:*)
    at Object.runInThisContext (node:vm:*)
    at Object.<anonymous> (*fixtures*vm*vm_display_syntax_error.js:36:4)

Node.js *
