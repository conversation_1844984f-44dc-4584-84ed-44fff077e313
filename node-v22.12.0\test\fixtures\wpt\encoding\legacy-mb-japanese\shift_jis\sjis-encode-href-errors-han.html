<!DOCTYPE html>
<html>
<head>
<meta charset="shift_jis"> <!-- test breaks if the server overrides this -->
<title>Shift_jis encoding errors (href, han)</title>
<meta name="timeout" content="long">
<meta name="variant" content="?1-1000">
<meta name="variant" content="?1001-2000">
<meta name="variant" content="?2001-3000">
<meta name="variant" content="?3001-4000">
<meta name="variant" content="?4001-5000">
<meta name="variant" content="?5001-6000">
<meta name="variant" content="?6001-7000">
<meta name="variant" content="?7001-8000">
<meta name="variant" content="?8001-9000">
<meta name="variant" content="?9001-10000">
<meta name="variant" content="?10001-11000">
<meta name="variant" content="?11001-12000">
<meta name="variant" content="?12001-13000">
<meta name="variant" content="?13001-14000">
<meta name="variant" content="?14001-15000">
<meta name="variant" content="?15001-16000">
<meta name="variant" content="?16001-17000">
<meta name="variant" content="?17001-18000">
<meta name="variant" content="?18001-19000">
<meta name="variant" content="?19001-20000">
<meta name="variant" content="?20001-21000">
<meta name="variant" content="?21001-last">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/common/subset-tests.js"></script>
<script src="jis0208_index.js"></script>
<script src="sjis-encoder.js"></script>
<link rel="author" title="Richard Ishida" href="mailto:<EMAIL>">
<link rel="help" href="https://encoding.spec.whatwg.org/#shift_jis">
<meta name="assert" content="The browser produces percent-escaped character references when writing characters to an href value and encoding han characters that are not in the shift_jis encoding.">
<style>
 iframe { display:none }
 form { display:none }
</style>
</head>
<body>
<div id="log"></div>
<script src="../../resources/ranges.js"></script>
<script>
var errors = true;
var encoder = sjisEncoder;
var ranges = rangesHan;
var separator = ",";
function expect(result, codepoint) {
  return "%26%23" + codepoint + "%3B";
}
// Overwrite normalizeStr
function normalizeStr(str) {
  return str;
}
</script>
<script src="../../resources/encode-form-common.js"></script>
</body>
</html>
