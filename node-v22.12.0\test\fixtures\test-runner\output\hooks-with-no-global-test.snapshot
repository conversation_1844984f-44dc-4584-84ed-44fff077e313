TAP version 13
# Subtest: describe hooks with no global tests
    # Subtest: 1
    ok 1 - 1
      ---
      duration_ms: *
      ...
    # Subtest: 2
    ok 2 - 2
      ---
      duration_ms: *
      ...
    # Subtest: nested
        # Subtest: nested 1
        ok 1 - nested 1
          ---
          duration_ms: *
          ...
        # Subtest: nested 2
        ok 2 - nested 2
          ---
          duration_ms: *
          ...
        1..2
    ok 3 - nested
      ---
      duration_ms: *
      type: 'suite'
      ...
    1..3
ok 1 - describe hooks with no global tests
  ---
  duration_ms: *
  type: 'suite'
  ...
1..1
# tests 4
# suites 2
# pass 4
# fail 0
# cancelled 0
# skipped 0
# todo 0
# duration_ms *
