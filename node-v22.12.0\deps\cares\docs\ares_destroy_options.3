.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\" SPDX-License-Identifier: MIT
.\"
.TH ARES_DESTROY_OPTIONS 3 "1 June 2007"
.SH NAME
ares_destroy_options \- Destroy options initialized with ares_save_options
.SH SYNOPSIS
.nf
#include <ares.h>

void ares_destroy_options(struct ares_options *\fIoptions\fP)
.fi
.SH DESCRIPTION
The \fBares_destroy_options(3)\fP function destroys the options struct
identified by \Ioptions\fP, freeing all memory allocated by
\fBares_save_options(3)\fP.
.SH SEE ALSO
.BR ares_save_options (3),
.BR ares_init_options (3)
