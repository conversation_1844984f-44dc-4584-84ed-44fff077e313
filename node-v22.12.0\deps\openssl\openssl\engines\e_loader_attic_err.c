/*
 * Generated by util/mkerr.pl DO NOT EDIT
 * Copyright 1995-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#include <openssl/err.h>
#include "e_loader_attic_err.h"

#ifndef OPENSSL_NO_ERR

static ERR_STRING_DATA ATTIC_str_reasons[] = {
    {ERR_PACK(0, 0, ATTIC_R_AMBIGUOUS_CONTENT_TYPE), "ambiguous content type"},
    {ERR_PACK(0, 0, ATTIC_R_BAD_PASSWORD_READ), "bad password read"},
    {ERR_PACK(0, 0, ATTIC_R_ERROR_VERIFYING_PKCS12_MAC),
    "error verifying pkcs12 mac"},
    {ERR_PACK(0, 0, ATTIC_R_INIT_FAILED), "init failed"},
    {ERR_PACK(0, 0, ATTIC_R_PASSPHRASE_CALLBACK_ERROR),
    "passphrase callback error"},
    {ERR_PACK(0, 0, ATTIC_R_PATH_MUST_BE_ABSOLUTE), "path must be absolute"},
    {ERR_PACK(0, 0, ATTIC_R_SEARCH_ONLY_SUPPORTED_FOR_DIRECTORIES),
    "search only supported for directories"},
    {ERR_PACK(0, 0, ATTIC_R_UI_PROCESS_INTERRUPTED_OR_CANCELLED),
    "ui process interrupted or cancelled"},
    {ERR_PACK(0, 0, ATTIC_R_UNSUPPORTED_CONTENT_TYPE),
    "unsupported content type"},
    {ERR_PACK(0, 0, ATTIC_R_UNSUPPORTED_SEARCH_TYPE),
    "unsupported search type"},
    {ERR_PACK(0, 0, ATTIC_R_URI_AUTHORITY_UNSUPPORTED),
    "uri authority unsupported"},
    {0, NULL}
};

#endif

static int lib_code = 0;
static int error_loaded = 0;

static int ERR_load_ATTIC_strings(void)
{
    if (lib_code == 0)
        lib_code = ERR_get_next_error_library();

    if (!error_loaded) {
#ifndef OPENSSL_NO_ERR
        ERR_load_strings(lib_code, ATTIC_str_reasons);
#endif
        error_loaded = 1;
    }
    return 1;
}

static void ERR_unload_ATTIC_strings(void)
{
    if (error_loaded) {
#ifndef OPENSSL_NO_ERR
        ERR_unload_strings(lib_code, ATTIC_str_reasons);
#endif
        error_loaded = 0;
    }
}

static void ERR_ATTIC_error(int function, int reason, const char *file, int line)
{
    if (lib_code == 0)
        lib_code = ERR_get_next_error_library();
    ERR_raise(lib_code, reason);
    ERR_set_debug(file, line, NULL);
}
