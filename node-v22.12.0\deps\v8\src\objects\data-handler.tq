// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This class does not use the generated verifier, so if you change anything
// here, please also update DataHandlerVerify in objects-debug.cc.
@abstract
extern class DataHandler extends Struct {
  // [smi_handler]: A Smi which encodes a handler or Code object
  // (we still use code handlers for accessing lexical environment variables,
  // but soon only smi handlers will remain). See LoadHandler and StoreHandler
  // for details about encoding.
  smi_handler: Smi|Code;

  // [validity_cell]: A validity Cell that guards prototype chain modifications.
  validity_cell: Smi|Cell;

  // Space for the following fields may or may not be allocated.
  data1: MaybeObject;
  data2: MaybeObject;
  data3: MaybeObject;
}
