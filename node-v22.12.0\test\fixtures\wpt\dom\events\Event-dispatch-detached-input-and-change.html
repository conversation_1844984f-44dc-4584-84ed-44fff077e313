<!DOCTYPE html>
<meta charset="utf-8">
<link rel="author" title="Joey Arhar" href="mailto:<EMAIL>">
<title>input and change events for detached checkbox and radio elements</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>

<body>
<script>

test(() => {
  const input = document.createElement('input');
  input.type = 'checkbox';

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.click();
  assert_false(inputEventFired);
  assert_false(changeEventFired);
}, 'detached checkbox should not emit input or change events on click().');

test(() => {
  const input = document.createElement('input');
  input.type = 'radio';

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.click();
  assert_false(inputEventFired);
  assert_false(changeEventFired);
}, 'detached radio should not emit input or change events on click().');

test(() => {
  const input = document.createElement('input');
  input.type = 'checkbox';

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.dispatchEvent(new MouseEvent('click'));
  assert_false(inputEventFired);
  assert_false(changeEventFired);
}, `detached checkbox should not emit input or change events on dispatchEvent(new MouseEvent('click')).`);

test(() => {
  const input = document.createElement('input');
  input.type = 'radio';

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.dispatchEvent(new MouseEvent('click'));
  assert_false(inputEventFired);
  assert_false(changeEventFired);
}, `detached radio should not emit input or change events on dispatchEvent(new MouseEvent('click')).`);


test(() => {
  const input = document.createElement('input');
  input.type = 'checkbox';
  document.body.appendChild(input);

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.click();
  assert_true(inputEventFired);
  assert_true(changeEventFired);
}, 'attached checkbox should emit input and change events on click().');

test(() => {
  const input = document.createElement('input');
  input.type = 'radio';
  document.body.appendChild(input);

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.click();
  assert_true(inputEventFired);
  assert_true(changeEventFired);
}, 'attached radio should emit input and change events on click().');

test(() => {
  const input = document.createElement('input');
  input.type = 'checkbox';
  document.body.appendChild(input);

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.dispatchEvent(new MouseEvent('click'));
  assert_true(inputEventFired);
  assert_true(changeEventFired);
}, `attached checkbox should emit input and change events on dispatchEvent(new MouseEvent('click')).`);

test(() => {
  const input = document.createElement('input');
  input.type = 'radio';
  document.body.appendChild(input);

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.dispatchEvent(new MouseEvent('click'));
  assert_true(inputEventFired);
  assert_true(changeEventFired);
}, `attached radio should emit input and change events on dispatchEvent(new MouseEvent('click')).`);


test(() => {
  const input = document.createElement('input');
  input.type = 'checkbox';
  const shadowHost = document.createElement('div');
  document.body.appendChild(shadowHost);
  const shadowRoot = shadowHost.attachShadow({mode: 'open'});
  shadowRoot.appendChild(input);

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.click();
  assert_true(inputEventFired);
  assert_true(changeEventFired);
}, 'attached to shadow dom checkbox should emit input and change events on click().');

test(() => {
  const input = document.createElement('input');
  input.type = 'radio';
  const shadowHost = document.createElement('div');
  document.body.appendChild(shadowHost);
  const shadowRoot = shadowHost.attachShadow({mode: 'open'});
  shadowRoot.appendChild(input);

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.click();
  assert_true(inputEventFired);
  assert_true(changeEventFired);
}, 'attached to shadow dom radio should emit input and change events on click().');

test(() => {
  const input = document.createElement('input');
  input.type = 'checkbox';
  const shadowHost = document.createElement('div');
  document.body.appendChild(shadowHost);
  const shadowRoot = shadowHost.attachShadow({mode: 'open'});
  shadowRoot.appendChild(input);

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.dispatchEvent(new MouseEvent('click'));
  assert_true(inputEventFired);
  assert_true(changeEventFired);
}, `attached to shadow dom checkbox should emit input and change events on dispatchEvent(new MouseEvent('click')).`);

test(() => {
  const input = document.createElement('input');
  input.type = 'radio';
  const shadowHost = document.createElement('div');
  document.body.appendChild(shadowHost);
  const shadowRoot = shadowHost.attachShadow({mode: 'open'});
  shadowRoot.appendChild(input);

  let inputEventFired = false;
  input.addEventListener('input', () => inputEventFired = true);
  let changeEventFired = false;
  input.addEventListener('change', () => changeEventFired = true);
  input.dispatchEvent(new MouseEvent('click'));
  assert_true(inputEventFired);
  assert_true(changeEventFired);
}, `attached to shadow dom radio should emit input and change events on dispatchEvent(new MouseEvent('click')).`);

</script>
</body>
