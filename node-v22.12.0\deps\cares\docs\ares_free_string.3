.\"
.\" Copyright 2000 by the Massachusetts Institute of Technology.
.\" SPDX-License-Identifier: MIT
.\"
.TH ARES_FREE_STRING 3 "4 February 2004"
.SH NAME
ares_free_string \- Free strings allocated by ares functions
.SH SYNOPSIS
.nf
#include <ares.h>

void ares_free_string(void *\fIstr\fP)
.fi
.SH DESCRIPTION
The \fIares_free_string(3)\fP function frees a string allocated by an ares
function.
.SH SEE ALSO
.BR ares_mkquery (3)
.BR ares_expand_string (3)
