{"name": "ochang_js_fuzzer", "version": "1.0.0", "description": "", "main": "run.js", "scripts": {"test": "APP_NAME=d8 mocha"}, "bin": "run.js", "author": "<EMAIL>", "license": "ISC", "dependencies": {"@babel/generator": "^7.1.3", "@babel/template": "^7.1.2", "@babel/traverse": "^7.1.4", "@babel/types": "^7.1.3", "@babel/parser": "^7.1.3", "commander": "^2.11.0", "globals": "^10.1.0", "tempfile": "^3.0.0", "tempy": "^0.5.0"}, "devDependencies": {"eslint": "^6.8.0", "mocha": "^3.5.3", "pkg": "^4.3.4", "prettier": "2.0.5", "sinon": "^4.0.0"}, "pkg": {"assets": "resources/**/*"}}