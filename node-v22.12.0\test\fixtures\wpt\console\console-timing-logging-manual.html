<!DOCTYPE html>
<html>
<head>
<title>Console Timing Methods - Logging Manual Test</title>
<meta name="author" title="Dominic Farolino" href="mailto:<EMAIL>">
<meta name="assert" content="Console timing methods">
<link rel="help" href="https://console.spec.whatwg.org/#timing">
</head>
<body>
<p>Open the console inside the developer tools. It should contain entries whose contents are:</p>
<p><code>default: &lt;some time></code></p>
<p><code>default: &lt;some time></code></p>

<p><code>default: &lt;some time></code></p>
<p><code>default: &lt;some time> extra data</code></p>
<p><code>default: &lt;some time></code></p>

<p><code>default: &lt;some time></code></p>
<p><code>default: &lt;some time> extra data</code></p>
<p><code>default: &lt;some time></code></p>

<p><code>default: &lt;some time></code></p>
<p><code>default: &lt;some time> extra data</code></p>
<p><code>default: &lt;some time></code></p>

<p><code>custom toString(): &lt;some time></code></p>
<p><code>custom toString(): &lt;some time> extra data</code></p>
<p><code>custom toString(): &lt;some time></code></p>

<p><code>a label: &lt;some time></code></p>
<p><code>a label: &lt;some time> extra data</code></p>
<p><code>a label: &lt;some time></code></p>

<p style="color:grey;">[some warning message indicating that a timer for label "b" does not exist]</p>

<script>
console.time();
console.timeLog();
console.timeEnd();

console.time(undefined);
console.timeLog(undefined);
console.timeLog(undefined, "extra data");
console.timeEnd(undefined);

console.time("default");
console.timeLog("default");
console.timeLog("default", "extra data");
console.timeEnd("default");

console.time({toString() {return "default"}});
console.timeLog({toString() {return "default"}});
console.timeLog({toString() {return "default"}}, "extra data");
console.timeEnd({toString() {return "default"}});

console.time({toString() {return "custom toString()"}});
console.timeLog({toString() {return "custom toString()"}});
console.timeLog({toString() {return "custom toString()"}}, "extra data");
console.timeEnd({toString() {return "custom toString()"}});

console.time("a label");
console.timeLog("a label");
console.timeLog("a label", "extra data");
console.timeEnd("a label");

console.timeLog("b"); // should produce a warning
console.timeEnd("b"); // should produce a warning
</script>
</body>
</html>
