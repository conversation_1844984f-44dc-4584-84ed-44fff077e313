// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/objects/js-number-format.h'

extern class JSNumberFormat extends JSObject {
  locale: String;
  icu_number_formatter:
      Foreign;  // Managed<icu::number::LocalizedNumberFormatter>
  bound_format: JSFunction|Undefined;
}
