/* MIT License
 *
 * Copyright (c) <PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice (including the next
 * paragraph) shall be included in all copies or substantial portions of the
 * Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *
 * SPDX-License-Identifier: MIT
 */

#ifndef ARES__VERSION_H
#define ARES__VERSION_H

/* This is the global package copyright */
#define ARES_COPYRIGHT "2004 - 2024 Daniel Stenberg, <<EMAIL>>."

#define ARES_VERSION_MAJOR 1
#define ARES_VERSION_MINOR 34
#define ARES_VERSION_PATCH 3
#define ARES_VERSION_STR "1.34.3"

/* NOTE: We cannot make the version string a C preprocessor stringify operation
 *       due to assumptions made by integrators that aren't properly using
 *       pkgconf or cmake and are doing their own detection based on parsing
 *       this header */

#define ARES_VERSION                                        \
  ((ARES_VERSION_MAJOR << 16) | (ARES_VERSION_MINOR << 8) | \
   (ARES_VERSION_PATCH))

#endif
