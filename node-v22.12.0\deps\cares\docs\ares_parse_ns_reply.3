.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\" SPDX-License-Identifier: MIT
.\"
.TH ARES_PARSE_NS_REPLY 3 "10 February 2007"
.SH NAME
ares_parse_ns_reply \- Parse a reply to a DNS query of type NS into a hostent
.SH SYNOPSIS
.nf
#include <ares.h>

int ares_parse_ns_reply(const unsigned char *\fIabuf\fP, int \fIalen\fP,
                        struct hostent **\fIhost\fP);
.fi
.SH DESCRIPTION
The
.B ares_parse_ns_reply
function parses the response to a query of type NS into a
.BR "struct hostent" .
The parameters
.I abuf
and
.I alen
give the contents of the response.  The result is stored in allocated
memory and a pointer to it stored into the variable pointed to by
.IR host . 
The nameservers are stored into the 
.BR aliases 
field of the 
.IR host 
structure. 
It is the caller's responsibility to free the resulting host structure
using
.BR ares_free_hostent (3)
when it is no longer needed.
.SH RETURN VALUES
.B ares_parse_ns_reply
can return any of the following values:
.TP 15
.B ARES_SUCCESS
The response was successfully parsed.
.TP 15
.B ARES_EBADRESP
The response was malformatted.
.TP 15
.B ARES_ENODATA
The response did not contain an answer to the query.
.TP 15
.B ARES_ENOMEM
Memory was exhausted.
.SH SEE ALSO
.BR ares_query (3),
.BR ares_free_hostent (3)
