<!DOCTYPE html>
<title>non-passive mousewheel event listener on div</title>
<link rel="help" href="https://w3c.github.io/uievents/#cancelability-of-wheel-events">
<link rel="help" href="https://github.com/w3c/uievents/issues/331">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-actions.js"></script>
<script src="/resources/testdriver-vendor.js"></script>
<script src="resources/scrolling.js"></script>
<style>
  html, body {
    overflow: hidden;
    margin: 0;
  }
  #div {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: scroll;
  }
</style>
<div class=remove-on-cleanup id=div>
  <div style="height: 200vh"></div>
</div>
<script>
  document.body.onload = () => runTest({
    target: document.getElementById('div'),
    eventName: 'mousewheel',
    passive: false,
    expectCancelable: true,
  });
</script>
