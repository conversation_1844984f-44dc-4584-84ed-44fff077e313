TAP version 13
# Subtest: Coverage Print Fixed Width Infinity
ok 1 - Coverage Print Fixed Width Infinity
  ---
  duration_ms: *
  ...
1..1
# tests 1
# suites 0
# pass 1
# fail 0
# cancelled 0
# skipped 0
# todo 0
# duration_ms *
# start of coverage report
# -------------------------------------------------------------------------------------------------------------
# file                            | line % | branch % | funcs % | uncovered lines
# -------------------------------------------------------------------------------------------------------------
# test                            |        |          |         | 
#  fixtures                       |        |          |         | 
#   test-runner                   |        |          |         | 
#    coverage-snap                |        |          |         | 
#     a.js                        |  55.77 |   100.00 |    0.00 | 5-7 9-11 13-15 17-19 29-30 40-42 45-47 50-52
#     b.js                        |  45.45 |   100.00 |    0.00 | 5-7 9-11
#    output                       |        |          |         | 
#     coverage-width-infinity.mjs | 100.00 |   100.00 |  100.00 | 
# -------------------------------------------------------------------------------------------------------------
# all files                       |  60.81 |   100.00 |    0.00 | 
# -------------------------------------------------------------------------------------------------------------
# end of coverage report
