// Copyright 2022 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@abstract
extern class JSSynchronizationPrimitive extends AlwaysSharedSpaceJSObject {
  waiter_queue_head: ExternalPointer;
  state: uint32;
}

extern class JSAtomicsMutex extends JSSynchronizationPrimitive {
  owner_thread_id: int32;
}

extern class JSAtomicsCondition extends JSSynchronizationPrimitive {
  @if(TAGGED_SIZE_8_BYTES) optional_padding: uint32;
  @ifnot(TAGGED_SIZE_8_BYTES) optional_padding: void;
}
