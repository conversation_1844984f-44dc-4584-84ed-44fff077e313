// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

type SourceTextModuleInfo extends FixedArray;

bitfield struct SourceTextModuleFlags extends uint31 {
  async: bool: 1 bit;
  async_evaluating_ordinal: uint32: 30 bit;
}

extern class SourceTextModule extends Module {
  // The code representing this module, or an abstraction thereof.
  code: SharedFunctionInfo|JSFunction|JSGeneratorObject;

  // Arrays of cells corresponding to regular exports and regular imports.
  // A cell's position in the array is determined by the cell index of the
  // associated module entry (which coincides with the variable index of the
  // associated variable).
  regular_exports: FixedArray;
  regular_imports: FixedArray;

  // Modules imported or re-exported by this module.
  // Corresponds 1-to-1 to the module specifier strings in
  // SourceTextModuleInfo::module_requests.
  requested_modules: FixedArray;

  // The value of import.meta inside of this module.
  // Lazily initialized on first access. It's the hole before first access and
  // a JSObject afterwards.
  @cppAcquireLoad @cppReleaseStore import_meta: TheHole|JSObject;

  // The first visited module of a cycle. For modules not in a cycle, this is
  // the module itself. It's the hole before the module state transitions to
  // kEvaluated.
  cycle_root: SourceTextModule|TheHole;

  async_parent_modules: ArrayList;

  // TODO(neis): Don't store those in the module object?
  dfs_index: Smi;
  dfs_ancestor_index: Smi;

  // The number of currently evaluating async dependencies of this module.
  pending_async_dependencies: Smi;

  flags: SmiTagged<SourceTextModuleFlags>;
}

extern class ModuleRequest extends Struct {
  specifier: String;

  // Import attributes are stored in this array in the form:
  // [key1, value1, location1, key2, value2, location2, ...]
  import_attributes: FixedArray;

  // Source text position of the module request
  position: Smi;
}

extern class SourceTextModuleInfoEntry extends Struct {
  export_name: String|Undefined;
  local_name: String|Undefined;
  import_name: String|Undefined;
  module_request: Smi;
  cell_index: Smi;
  beg_pos: Smi;
  end_pos: Smi;
}
