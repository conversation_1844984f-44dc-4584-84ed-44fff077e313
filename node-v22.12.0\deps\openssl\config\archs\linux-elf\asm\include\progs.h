/*
 * WARNING: do not edit!
 * Generated by apps/progs.pl
 *
 * Copyright 1995-2024 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#include "function.h"

extern int asn1parse_main(int argc, char *argv[]);
extern int ca_main(int argc, char *argv[]);
extern int ciphers_main(int argc, char *argv[]);
extern int cmp_main(int argc, char *argv[]);
extern int cms_main(int argc, char *argv[]);
extern int crl_main(int argc, char *argv[]);
extern int crl2pkcs7_main(int argc, char *argv[]);
extern int dgst_main(int argc, char *argv[]);
extern int dhparam_main(int argc, char *argv[]);
extern int dsa_main(int argc, char *argv[]);
extern int dsaparam_main(int argc, char *argv[]);
extern int ec_main(int argc, char *argv[]);
extern int ecparam_main(int argc, char *argv[]);
extern int enc_main(int argc, char *argv[]);
extern int engine_main(int argc, char *argv[]);
extern int errstr_main(int argc, char *argv[]);
extern int fipsinstall_main(int argc, char *argv[]);
extern int gendsa_main(int argc, char *argv[]);
extern int genpkey_main(int argc, char *argv[]);
extern int genrsa_main(int argc, char *argv[]);
extern int help_main(int argc, char *argv[]);
extern int info_main(int argc, char *argv[]);
extern int kdf_main(int argc, char *argv[]);
extern int list_main(int argc, char *argv[]);
extern int mac_main(int argc, char *argv[]);
extern int nseq_main(int argc, char *argv[]);
extern int ocsp_main(int argc, char *argv[]);
extern int passwd_main(int argc, char *argv[]);
extern int pkcs12_main(int argc, char *argv[]);
extern int pkcs7_main(int argc, char *argv[]);
extern int pkcs8_main(int argc, char *argv[]);
extern int pkey_main(int argc, char *argv[]);
extern int pkeyparam_main(int argc, char *argv[]);
extern int pkeyutl_main(int argc, char *argv[]);
extern int prime_main(int argc, char *argv[]);
extern int rand_main(int argc, char *argv[]);
extern int rehash_main(int argc, char *argv[]);
extern int req_main(int argc, char *argv[]);
extern int rsa_main(int argc, char *argv[]);
extern int rsautl_main(int argc, char *argv[]);
extern int s_client_main(int argc, char *argv[]);
extern int s_server_main(int argc, char *argv[]);
extern int s_time_main(int argc, char *argv[]);
extern int sess_id_main(int argc, char *argv[]);
extern int smime_main(int argc, char *argv[]);
extern int speed_main(int argc, char *argv[]);
extern int spkac_main(int argc, char *argv[]);
extern int srp_main(int argc, char *argv[]);
extern int storeutl_main(int argc, char *argv[]);
extern int ts_main(int argc, char *argv[]);
extern int verify_main(int argc, char *argv[]);
extern int version_main(int argc, char *argv[]);
extern int x509_main(int argc, char *argv[]);

extern const OPTIONS asn1parse_options[];
extern const OPTIONS ca_options[];
extern const OPTIONS ciphers_options[];
extern const OPTIONS cmp_options[];
extern const OPTIONS cms_options[];
extern const OPTIONS crl_options[];
extern const OPTIONS crl2pkcs7_options[];
extern const OPTIONS dgst_options[];
extern const OPTIONS dhparam_options[];
extern const OPTIONS dsa_options[];
extern const OPTIONS dsaparam_options[];
extern const OPTIONS ec_options[];
extern const OPTIONS ecparam_options[];
extern const OPTIONS enc_options[];
extern const OPTIONS engine_options[];
extern const OPTIONS errstr_options[];
extern const OPTIONS fipsinstall_options[];
extern const OPTIONS gendsa_options[];
extern const OPTIONS genpkey_options[];
extern const OPTIONS genrsa_options[];
extern const OPTIONS help_options[];
extern const OPTIONS info_options[];
extern const OPTIONS kdf_options[];
extern const OPTIONS list_options[];
extern const OPTIONS mac_options[];
extern const OPTIONS nseq_options[];
extern const OPTIONS ocsp_options[];
extern const OPTIONS passwd_options[];
extern const OPTIONS pkcs12_options[];
extern const OPTIONS pkcs7_options[];
extern const OPTIONS pkcs8_options[];
extern const OPTIONS pkey_options[];
extern const OPTIONS pkeyparam_options[];
extern const OPTIONS pkeyutl_options[];
extern const OPTIONS prime_options[];
extern const OPTIONS rand_options[];
extern const OPTIONS rehash_options[];
extern const OPTIONS req_options[];
extern const OPTIONS rsa_options[];
extern const OPTIONS rsautl_options[];
extern const OPTIONS s_client_options[];
extern const OPTIONS s_server_options[];
extern const OPTIONS s_time_options[];
extern const OPTIONS sess_id_options[];
extern const OPTIONS smime_options[];
extern const OPTIONS speed_options[];
extern const OPTIONS spkac_options[];
extern const OPTIONS srp_options[];
extern const OPTIONS storeutl_options[];
extern const OPTIONS ts_options[];
extern const OPTIONS verify_options[];
extern const OPTIONS version_options[];
extern const OPTIONS x509_options[];

extern FUNCTION functions[];
