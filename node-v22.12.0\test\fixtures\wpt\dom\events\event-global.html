<!DOCTYPE html>
<title>window.event tests</title>
<link rel="author" title="<PERSON>" href="mailto:<EMAIL>">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<div id=log></div>
<script>
setup({allow_uncaught_exception: true});

test(t => {
  assert_own_property(window, "event");
  assert_equals(window.event, undefined);
}, "event exists on window, which is initially set to undefined");

async_test(t => {
  let target = document.createElement("div");
  assert_equals(window.event, undefined, "undefined before dispatch");

  let clickEvent = new Event("click");
  target.addEventListener("click", t.step_func_done(e => {
    assert_equals(window.event, clickEvent, "window.event set to current event during dispatch");
  }));

  target.dispatchEvent(clickEvent);
  assert_equals(window.event, undefined, "undefined after dispatch");
}, "window.event is only defined during dispatch");

async_test(t => {
  let parent = document.createElement("div");
  let root = parent.attachShadow({mode: "closed"});
  let span = document.createElement("span");
  root.appendChild(span);

  span.addEventListener("test", t.step_func(e => {
    assert_equals(window.event, undefined);
    assert_not_equals(window.event, e);
  }));

  parent.addEventListener("test", t.step_func_done(e => {
    assert_equals(window.event, e);
    assert_not_equals(window.event, undefined);
  }));

  parent.dispatchEvent(new Event("test", {composed: true}));
}, "window.event is undefined if the target is in a shadow tree (event dispatched outside shadow tree)");

async_test(t => {
  let parent = document.createElement("div");
  let root = parent.attachShadow({mode: "closed"});
  let span = document.createElement("span");
  root.appendChild(span);
  let shadowNode = root.firstElementChild;

  shadowNode.addEventListener("test", t.step_func((e) => {
    assert_not_equals(window.event, e);
    assert_equals(window.event, undefined);
  }));

  parent.addEventListener("test", t.step_func_done(e => {
    assert_equals(window.event, e);
    assert_not_equals(window.event, undefined);
  }));

  shadowNode.dispatchEvent(new Event("test", {composed: true, bubbles: true}));
}, "window.event is undefined if the target is in a shadow tree (event dispatched inside shadow tree)");

async_test(t => {
  let parent = document.createElement("div");
  let root = parent.attachShadow({mode: "open"});
  document.body.append(parent)
  let span = document.createElement("span");
  root.append(span);
  let shadowNode = root.firstElementChild;

  shadowNode.addEventListener("error", t.step_func(e => {
    assert_not_equals(window.event, e);
    assert_equals(window.event, undefined);
  }));

  let windowOnErrorCalled = false;
  window.onerror = t.step_func_done(() => {
    windowOnErrorCalled = true;
    assert_equals(typeof window.event, "object");
    assert_equals(window.event.type, "error");
  });

  shadowNode.dispatchEvent(new ErrorEvent("error", {composed: true, bubbles: true}));
  assert_true(windowOnErrorCalled);
}, "window.event is undefined inside window.onerror if the target is in a shadow tree (ErrorEvent dispatched inside shadow tree)");

async_test(t => {
  let target1 = document.createElement("div");
  let target2 = document.createElement("div");

  target2.addEventListener("dude", t.step_func(() => {
    assert_equals(window.event.type, "dude");
  }));

  target1.addEventListener("cool", t.step_func_done(() => {
    assert_equals(window.event.type, "cool", "got expected event from global event during dispatch");
    target2.dispatchEvent(new Event("dude"));
    assert_equals(window.event.type, "cool", "got expected event from global event after handling a different event handler callback");
  }));

  target1.dispatchEvent(new Event("cool"));
}, "window.event is set to the current event during dispatch");

async_test(t => {
  let target = document.createElement("div");

  target.addEventListener("click", t.step_func_done(e => {
    assert_equals(e, window.event);
  }));

  target.dispatchEvent(new Event("click"));
}, "window.event is set to the current event, which is the event passed to dispatch");

async_test(t => {
  let target = new XMLHttpRequest();

  target.onload = t.step_func_done(e => {
    assert_equals(e, window.event);
  });

  target.dispatchEvent(new Event("load"));
}, "window.event is set to the current event, which is the event passed to dispatch (2)");
</script>
