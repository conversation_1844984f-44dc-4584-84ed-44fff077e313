<!DOCTYPE html>
<meta charset="utf-8">
<title>Prefixed CSS Animation end events</title>
<link rel="help" href="https://dom.spec.whatwg.org/#concept-event-listener-invoke">

<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>

<body>

<script src="resources/prefixed-animation-event-tests.js"></script>
<script>
'use strict';

runAnimationEventTests({
  unprefixedType: 'animationend',
  prefixedType: 'webkitAnimationEnd',
  animationCssStyle: '1ms',
});
</script>
