<!DOCTYPE html>
<meta charset=utf-8>
<title> Determined event propagation path - target moved </title>
<link rel="help" href="https://dom.spec.whatwg.org/#concept-event-dispatch">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<div id=log></div>
<table id="table" border="1" style="display: none">
    <tbody id="table-body">
    <tr id="table-row">
        <td id="table-cell">Shady Grove</td>
        <td>Aeolian</td>
    </tr>
    <tr id="parent">
        <td id="target">Over the river, Charlie</td>
        <td>Dorian</td>
    </tr>
    </tbody>
</table>
<script>
test(function() {
    var event_type = "foo";
    var target = document.getElementById("target");
    var parent = document.getElementById("parent");
    var tbody = document.getElementById("table-body");
    var table = document.getElementById("table");
    var body = document.body;
    var html = document.documentElement;
    var targets = [window, document, html, body, table, tbody, parent, target];
    var expected_targets = targets.concat([target, parent, tbody, table, body, html, document, window]);
    var phases = [
        Event.CAPTURING_PHASE,
        Event.CAPTURING_PHASE,
        Event.CAPTURING_PHASE,
        Event.CAPTURING_PHASE,
        Event.CAPTURING_PHASE,
        Event.CAPTURING_PHASE,
        Event.CAPTURING_PHASE,
        Event.AT_TARGET,
        Event.AT_TARGET,
        Event.BUBBLING_PHASE,
        Event.BUBBLING_PHASE,
        Event.BUBBLING_PHASE,
        Event.BUBBLING_PHASE,
        Event.BUBBLING_PHASE,
        Event.BUBBLING_PHASE,
        Event.BUBBLING_PHASE,
    ];

    var actual_targets = [], actual_phases = [];
    var test_event = this.step_func(function(evt) {
        if (parent === target.parentNode) {
            var table_row = document.getElementById("table-row");
            table_row.appendChild(parent.removeChild(target));
        }

        actual_targets.push(evt.currentTarget);
        actual_phases.push(evt.eventPhase);
    });

    for (var i = 0; i < targets.length; i++) {
        targets[i].addEventListener(event_type, test_event, true);
        targets[i].addEventListener(event_type, test_event, false);
    }

    var evt = document.createEvent("Event");
    evt.initEvent(event_type, true, true);
    target.dispatchEvent(evt);

    assert_array_equals(actual_targets, expected_targets, "targets");
    assert_array_equals(actual_phases, phases, "phases");
}, "Event propagation path when an element in it is moved within the DOM");
</script>
