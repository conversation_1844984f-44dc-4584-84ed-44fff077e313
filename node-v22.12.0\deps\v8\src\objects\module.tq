// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@abstract
extern class Module extends HeapObject {
  // The complete export table, mapping an export name to its cell.
  exports: ObjectHashTable;
  // Hash for this object (a random non-zero Smi).
  hash: Smi;
  status: Smi;
  module_namespace: JSModuleNamespace|Undefined;
  // The exception in the case {status} is kErrored.
  exception: Object;
  // The top level promise capability of this module. Will only be defined
  // for cycle roots.
  top_level_capability: JSPromise|Undefined;
}

extern class JSModuleNamespace extends JSSpecialObject {
  module: Module;
}

extern class ScriptOrModule extends Struct {
  resource_name: Object;
  host_defined_options: FixedArray;
}
