// Copyright 2018 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef V8_OBJECTS_PROMISE_INL_H_
#define V8_OBJECTS_PROMISE_INL_H_

#include "src/objects/promise.h"

#include "src/objects/js-promise-inl.h"
#include "src/objects/microtask-inl.h"

// Has to be the last include (doesn't have include guards):
#include "src/objects/object-macros.h"

namespace v8 {
namespace internal {

#include "torque-generated/src/objects/promise-tq-inl.inc"

TQ_OBJECT_CONSTRUCTORS_IMPL(PromiseReactionJobTask)
TQ_OBJECT_CONSTRUCTORS_IMPL(PromiseFulfillReactionJobTask)
TQ_OBJECT_CONSTRUCTORS_IMPL(PromiseRejectReactionJobTask)
TQ_OBJECT_CONSTRUCTORS_IMPL(PromiseResolveThenableJobTask)
TQ_OBJECT_CONSTRUCTORS_IMPL(PromiseCapability)
TQ_OBJECT_CONSTRUCTORS_IMPL(PromiseReaction)

}  // namespace internal
}  // namespace v8

#include "src/objects/object-macros-undef.h"

#endif  // V8_OBJECTS_PROMISE_INL_H_
