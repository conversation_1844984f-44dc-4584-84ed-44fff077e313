{"protocol": {"package": "src/inspector/protocol", "output": "protocol", "namespace": ["v8_inspector", "protocol"], "options": [{"domain": "<PERSON><PERSON><PERSON>", "exported": ["Domain"]}, {"domain": "Runtime", "async": ["evaluate", "await<PERSON><PERSON><PERSON>", "callFunctionOn", "runScript", "terminateExecution"], "exported": ["StackTrace", "StackTraceId", "RemoteObject", "ExecutionContextId"]}, {"domain": "Debugger", "exported": ["SearchMatch", "paused.reason"]}, {"domain": "<PERSON><PERSON><PERSON>"}, {"domain": "Profiler"}, {"domain": "HeapProfiler", "async": ["collectGarbage", "takeHeapSnapshot"]}]}, "exported": {"package": "include/inspector", "output": "../../include/inspector", "string_header": "v8-inspector.h", "string_in": "StringView", "string_out": "std::unique_ptr<StringBuffer>", "to_string_out": "StringBufferFrom(std::move(%s))", "export_macro": "V8_EXPORT"}, "lib": {"package": "src/inspector/protocol", "output": "protocol", "string_header": "src/inspector/string-util.h"}, "crdtp": {"namespace": "v8_crdtp"}}