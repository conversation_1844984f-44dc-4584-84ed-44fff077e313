<!DOCTYPE html>
<html>
<head>
<title>Console String Format Specifier on Symbols</title>
<meta name="author" title="<PERSON>" href="mailto:<EMAIL>">
<meta name="assert" content="Console format specifiers on Symbols">
<link rel="help" href="https://console.spec.whatwg.org/#formatter">
</head>
<body>
<p>Open the console inside the developer tools. It should contain five entries, each of which are:</p>
<p><code>Symbol(description)</code></p>

<script>
console.log("%s", Symbol.for("description"));
console.dirxml("%s", Symbol.for("description"));
console.trace("%s", Symbol.for("description"));
console.group("%s", Symbol.for("description"));
console.groupEnd();
console.groupCollapsed("%s", Symbol.for("description"));
console.groupEnd();
</script>
</body>
</html>
