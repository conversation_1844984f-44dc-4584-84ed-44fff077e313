<?xml version="1.0" encoding="utf-8"?>
<testsuites>
	<testcase name="sync pass todo" time="*" classname="test">
		<skipped type="todo" message="true"/>
	</testcase>
	<testcase name="sync pass todo with message" time="*" classname="test">
		<skipped type="todo" message="this is a passing todo"/>
	</testcase>
	<testcase name="sync fail todo" time="*" classname="test" failure="thrown from sync fail todo">
		<skipped type="todo" message="true"/>
		<failure type="testCodeFailure" message="thrown from sync fail todo">
[Error [ERR_TEST_FAILURE]: thrown from sync fail todo] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: thrown from sync fail todo
      *
      *
      *
      *
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="sync fail todo with message" time="*" classname="test" failure="thrown from sync fail todo with message">
		<skipped type="todo" message="this is a failing todo"/>
		<failure type="testCodeFailure" message="thrown from sync fail todo with message">
[Error [ERR_TEST_FAILURE]: thrown from sync fail todo with message] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: thrown from sync fail todo with message
      *
      *
      *
      *
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="sync skip pass" time="*" classname="test">
		<skipped type="skipped" message="true"/>
	</testcase>
	<testcase name="sync skip pass with message" time="*" classname="test">
		<skipped type="skipped" message="this is skipped"/>
	</testcase>
	<testcase name="sync pass" time="*" classname="test"/>
	<!-- this test should pass -->
	<testcase name="sync throw fail" time="*" classname="test" failure="thrown from sync throw fail">
		<failure type="testCodeFailure" message="thrown from sync throw fail">
[Error [ERR_TEST_FAILURE]: thrown from sync throw fail] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: thrown from sync throw fail
      *
      *
      *
      *
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="async skip pass" time="*" classname="test">
		<skipped type="skipped" message="true"/>
	</testcase>
	<testcase name="async pass" time="*" classname="test"/>
	<testcase name="async throw fail" time="*" classname="test" failure="thrown from async throw fail">
		<failure type="testCodeFailure" message="thrown from async throw fail">
[Error [ERR_TEST_FAILURE]: thrown from async throw fail] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: thrown from async throw fail
      *
      *
      *
      *
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="async skip fail" time="*" classname="test" failure="thrown from async throw fail">
		<skipped type="skipped" message="true"/>
		<failure type="testCodeFailure" message="thrown from async throw fail">
[Error [ERR_TEST_FAILURE]: thrown from async throw fail] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: thrown from async throw fail
      *
      *
      *
      *
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="async assertion fail" time="*" classname="test" failure="Expected values to be strictly equal:true !== false">
		<failure type="testCodeFailure" message="Expected values to be strictly equal:true !== false">
[Error [ERR_TEST_FAILURE]: Expected values to be strictly equal:

true !== false
] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: AssertionError [ERR_ASSERTION]: Expected values to be strictly equal:
  
  true !== false
  
      *
      *
      *
      *
      *
      *
      * {
    generatedMessage: true,
    code: 'ERR_ASSERTION',
    actual: true,
    expected: false,
    operator: 'strictEqual'
  }
}
		</failure>
	</testcase>
	<testcase name="resolve pass" time="*" classname="test"/>
	<testcase name="reject fail" time="*" classname="test" failure="rejected from reject fail">
		<failure type="testCodeFailure" message="rejected from reject fail">
[Error [ERR_TEST_FAILURE]: rejected from reject fail] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: rejected from reject fail
      *
      *
      *
      *
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="unhandled rejection - passes but warns" time="*" classname="test"/>
	<testcase name="async unhandled rejection - passes but warns" time="*" classname="test"/>
	<testcase name="immediate throw - passes but warns" time="*" classname="test"/>
	<testcase name="immediate reject - passes but warns" time="*" classname="test"/>
	<testcase name="immediate resolve pass" time="*" classname="test"/>
	<testsuite name="subtest sync throw fail" time="*" disabled="0" errors="0" tests="1" failures="1" skipped="0" hostname="HOSTNAME">
		<testcase name="+sync throw fail" time="*" classname="test" failure="thrown from subtest sync throw fail">
			<failure type="testCodeFailure" message="thrown from subtest sync throw fail">
Error [ERR_TEST_FAILURE]: thrown from subtest sync throw fail
    *
    * {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: thrown from subtest sync throw fail
      *
      *
      *
      *
      *
      *
      *
      *
      *
      *
}
			</failure>
		</testcase>
		<!-- this subtest should make its parent test fail -->
	</testsuite>
	<testcase name="sync throw non-error fail" time="*" classname="test" failure="Symbol(thrown symbol from sync throw non-error fail)">
		<failure type="testCodeFailure" message="Symbol(thrown symbol from sync throw non-error fail)">
[Error [ERR_TEST_FAILURE]: Symbol(thrown symbol from sync throw non-error fail)] { code: 'ERR_TEST_FAILURE', failureType: 'testCodeFailure', cause: Symbol(thrown symbol from sync throw non-error fail) }
		</failure>
	</testcase>
	<testsuite name="level 0a" time="*" disabled="0" errors="0" tests="4" failures="0" skipped="0" hostname="HOSTNAME">
		<testcase name="level 1a" time="*" classname="test"/>
		<testcase name="level 1b" time="*" classname="test"/>
		<testcase name="level 1c" time="*" classname="test"/>
		<testcase name="level 1d" time="*" classname="test"/>
	</testsuite>
	<testsuite name="top level" time="*" disabled="0" errors="0" tests="2" failures="1" skipped="0" hostname="HOSTNAME">
		<testcase name="+long running" time="*" classname="test" failure="test did not finish before its parent and was cancelled">
			<failure type="cancelledByParent" message="test did not finish before its parent and was cancelled">
[Error [ERR_TEST_FAILURE]: test did not finish before its parent and was cancelled] { code: 'ERR_TEST_FAILURE', failureType: 'cancelledByParent', cause: 'test did not finish before its parent and was cancelled' }
			</failure>
		</testcase>
		<testsuite name="+short running" time="*" disabled="0" errors="0" tests="1" failures="0" skipped="0" hostname="HOSTNAME">
			<testcase name="++short running" time="*" classname="test"/>
		</testsuite>
	</testsuite>
	<testcase name="invalid subtest - pass but subtest fails" time="*" classname="test"/>
	<testcase name="sync skip option" time="*" classname="test">
		<skipped type="skipped" message="true"/>
	</testcase>
	<testcase name="sync skip option with message" time="*" classname="test">
		<skipped type="skipped" message="this is skipped"/>
	</testcase>
	<testcase name="sync skip option is false fail" time="*" classname="test" failure="this should be executed">
		<failure type="testCodeFailure" message="this should be executed">
[Error [ERR_TEST_FAILURE]: this should be executed] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: this should be executed
      *
      *
      *
      *
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="&lt;anonymous>" time="*" classname="test"/>
	<testcase name="functionOnly" time="*" classname="test"/>
	<testcase name="&lt;anonymous>" time="*" classname="test"/>
	<testcase name="test with only a name provided" time="*" classname="test"/>
	<testcase name="&lt;anonymous>" time="*" classname="test"/>
	<testcase name="&lt;anonymous>" time="*" classname="test">
		<skipped type="skipped" message="true"/>
	</testcase>
	<testcase name="test with a name and options provided" time="*" classname="test">
		<skipped type="skipped" message="true"/>
	</testcase>
	<testcase name="functionAndOptions" time="*" classname="test">
		<skipped type="skipped" message="true"/>
	</testcase>
	<testcase name="callback pass" time="*" classname="test"/>
	<testcase name="callback fail" time="*" classname="test" failure="callback failure">
		<failure type="testCodeFailure" message="callback failure">
[Error [ERR_TEST_FAILURE]: callback failure] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: callback failure
      *
      *
}
		</failure>
	</testcase>
	<testcase name="sync t is this in test" time="*" classname="test"/>
	<testcase name="async t is this in test" time="*" classname="test"/>
	<testcase name="callback t is this in test" time="*" classname="test"/>
	<testcase name="callback also returns a Promise" time="*" classname="test" failure="passed a callback but also returned a Promise">
		<failure type="callbackAndPromisePresent" message="passed a callback but also returned a Promise">
[Error [ERR_TEST_FAILURE]: passed a callback but also returned a Promise] { code: 'ERR_TEST_FAILURE', failureType: 'callbackAndPromisePresent', cause: 'passed a callback but also returned a Promise' }
		</failure>
	</testcase>
	<testcase name="callback throw" time="*" classname="test" failure="thrown from callback throw">
		<failure type="testCodeFailure" message="thrown from callback throw">
[Error [ERR_TEST_FAILURE]: thrown from callback throw] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: thrown from callback throw
      *
      *
      *
      *
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="callback called twice" time="*" classname="test" failure="callback invoked multiple times">
		<failure type="multipleCallbackInvocations" message="callback invoked multiple times">
Error [ERR_TEST_FAILURE]: callback invoked multiple times
    *
    * {
  code: 'ERR_TEST_FAILURE',
  failureType: 'multipleCallbackInvocations',
  cause: 'callback invoked multiple times'
}
		</failure>
	</testcase>
	<testcase name="callback called twice in different ticks" time="*" classname="test"/>
	<testcase name="callback called twice in future tick" time="*" classname="test" failure="callback invoked multiple times">
		<failure type="uncaughtException" message="callback invoked multiple times">
Error [ERR_TEST_FAILURE]: callback invoked multiple times
    * {
  code: 'ERR_TEST_FAILURE',
  failureType: 'uncaughtException',
  cause: Error [ERR_TEST_FAILURE]: callback invoked multiple times
      * {
    code: 'ERR_TEST_FAILURE',
    failureType: 'multipleCallbackInvocations',
    cause: 'callback invoked multiple times'
  }
}
		</failure>
	</testcase>
	<testcase name="callback async throw" time="*" classname="test" failure="thrown from callback async throw">
		<failure type="uncaughtException" message="thrown from callback async throw">
Error [ERR_TEST_FAILURE]: thrown from callback async throw
    * {
  code: 'ERR_TEST_FAILURE',
  failureType: 'uncaughtException',
  cause: Error: thrown from callback async throw
      *
      *
}
		</failure>
	</testcase>
	<testcase name="callback async throw after done" time="*" classname="test"/>
	<testsuite name="only is set but not in only mode" time="*" disabled="0" errors="0" tests="4" failures="0" skipped="0" hostname="HOSTNAME">
		<testcase name="running subtest 1" time="*" classname="test"/>
		<testcase name="running subtest 2" time="*" classname="test"/>
		<!-- 'only' and 'runOnly' require the &#45;&#45;test-only command-line option. -->
		<testcase name="running subtest 3" time="*" classname="test"/>
		<!-- 'only' and 'runOnly' require the &#45;&#45;test-only command-line option. -->
		<testcase name="running subtest 4" time="*" classname="test"/>
	</testsuite>
	<!-- 'only' and 'runOnly' require the &#45;&#45;test-only command-line option. -->
	<testcase name="custom inspect symbol fail" time="*" classname="test" failure="customized">
		<failure type="testCodeFailure" message="customized">
[Error [ERR_TEST_FAILURE]: customized] { code: 'ERR_TEST_FAILURE', failureType: 'testCodeFailure', cause: customized }
		</failure>
	</testcase>
	<testcase name="custom inspect symbol that throws fail" time="*" classname="test" failure="{  foo: 1,  [Symbol(nodejs.util.inspect.custom)]: [Function: [nodejs.util.inspect.custom]]}">
		<failure type="testCodeFailure" message="{  foo: 1,  [Symbol(nodejs.util.inspect.custom)]: [Function: [nodejs.util.inspect.custom]]}">
[Error [ERR_TEST_FAILURE]: {
  foo: 1,
  [Symbol(nodejs.util.inspect.custom)]: [Function: [nodejs.util.inspect.custom]]
}] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: { foo: 1, [Symbol(nodejs.util.inspect.custom)]: [Function: [nodejs.util.inspect.custom]] }
}
		</failure>
	</testcase>
	<testsuite name="subtest sync throw fails" time="*" disabled="0" errors="0" tests="2" failures="2" skipped="0" hostname="HOSTNAME">
		<testcase name="sync throw fails at first" time="*" classname="test" failure="thrown from subtest sync throw fails at first">
			<failure type="testCodeFailure" message="thrown from subtest sync throw fails at first">
Error [ERR_TEST_FAILURE]: thrown from subtest sync throw fails at first
    *
    * {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: thrown from subtest sync throw fails at first
      *
      *
      *
      *
      *
      *
      *
      *
      *
      *
}
			</failure>
		</testcase>
		<testcase name="sync throw fails at second" time="*" classname="test" failure="thrown from subtest sync throw fails at second">
			<failure type="testCodeFailure" message="thrown from subtest sync throw fails at second">
Error [ERR_TEST_FAILURE]: thrown from subtest sync throw fails at second
    * {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: Error: thrown from subtest sync throw fails at second
      *
      *
      *
      *
      *
      *
      *
      *
}
			</failure>
		</testcase>
	</testsuite>
	<testcase name="timed out async test" time="*" classname="test" failure="test timed out after 5ms">
		<failure type="testTimeoutFailure" message="test timed out after 5ms">
[Error [ERR_TEST_FAILURE]: test timed out after 5ms] { code: 'ERR_TEST_FAILURE', failureType: 'testTimeoutFailure', cause: 'test timed out after 5ms' }
		</failure>
	</testcase>
	<testcase name="timed out callback test" time="*" classname="test" failure="test timed out after 5ms">
		<failure type="testTimeoutFailure" message="test timed out after 5ms">
[Error [ERR_TEST_FAILURE]: test timed out after 5ms] { code: 'ERR_TEST_FAILURE', failureType: 'testTimeoutFailure', cause: 'test timed out after 5ms' }
		</failure>
	</testcase>
	<testcase name="large timeout async test is ok" time="*" classname="test"/>
	<testcase name="large timeout callback test is ok" time="*" classname="test"/>
	<testcase name="successful thenable" time="*" classname="test"/>
	<testcase name="rejected thenable" time="*" classname="test" failure="custom error">
		<failure type="testCodeFailure" message="custom error">
[Error [ERR_TEST_FAILURE]: custom error] { code: 'ERR_TEST_FAILURE', failureType: 'testCodeFailure', cause: 'custom error' }
		</failure>
	</testcase>
	<testcase name="unfinished test with uncaughtException" time="*" classname="test" failure="foo">
		<failure type="uncaughtException" message="foo">
Error [ERR_TEST_FAILURE]: foo
    * {
  code: 'ERR_TEST_FAILURE',
  failureType: 'uncaughtException',
  cause: Error: foo
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="unfinished test with unhandledRejection" time="*" classname="test" failure="bar">
		<failure type="unhandledRejection" message="bar">
Error [ERR_TEST_FAILURE]: bar
    * {
  code: 'ERR_TEST_FAILURE',
  failureType: 'unhandledRejection',
  cause: Error: bar
      *
      *
      *
}
		</failure>
	</testcase>
	<testcase name="assertion errors display actual and expected properly" time="*" classname="test" failure="Expected values to be loosely deep-equal:{  bar: 1,  baz: {    date: 1970-01-01T00:00:00.000Z,    null: null,    number: 1,    string: 'Hello',    undefined: undefined  },  boo: [    1  ],  foo: 1}should loosely deep-equal{  baz: {    date: 1970-01-01T00:00:00.000Z,    null: null,    number: 1,    string: 'Hello',    undefined: undefined  },  boo: [    1  ],  circular: &lt;ref *1> {    bar: 2,    c: [Circular *1]  }}">
		<failure type="testCodeFailure" message="Expected values to be loosely deep-equal:{  bar: 1,  baz: {    date: 1970-01-01T00:00:00.000Z,    null: null,    number: 1,    string: 'Hello',    undefined: undefined  },  boo: [    1  ],  foo: 1}should loosely deep-equal{  baz: {    date: 1970-01-01T00:00:00.000Z,    null: null,    number: 1,    string: 'Hello',    undefined: undefined  },  boo: [    1  ],  circular: &lt;ref *1> {    bar: 2,    c: [Circular *1]  }}">
[Error [ERR_TEST_FAILURE]: Expected values to be loosely deep-equal:

{
  bar: 1,
  baz: {
    date: 1970-01-01T00:00:00.000Z,
    null: null,
    number: 1,
    string: 'Hello',
    undefined: undefined
  },
  boo: [
    1
  ],
  foo: 1
}

should loosely deep-equal

{
  baz: {
    date: 1970-01-01T00:00:00.000Z,
    null: null,
    number: 1,
    string: 'Hello',
    undefined: undefined
  },
  boo: [
    1
  ],
  circular: &lt;ref *1> {
    bar: 2,
    c: [Circular *1]
  }
}] {
  code: 'ERR_TEST_FAILURE',
  failureType: 'testCodeFailure',
  cause: AssertionError [ERR_ASSERTION]: Expected values to be loosely deep-equal:
  
  {
    bar: 1,
    baz: {
      date: 1970-01-01T00:00:00.000Z,
      null: null,
      number: 1,
      string: 'Hello',
      undefined: undefined
    },
    boo: [
      1
    ],
    foo: 1
  }
  
  should loosely deep-equal
  
  {
    baz: {
      date: 1970-01-01T00:00:00.000Z,
      null: null,
      number: 1,
      string: 'Hello',
      undefined: undefined
    },
    boo: [
      1
    ],
    circular: &lt;ref *1> {
      bar: 2,
      c: [Circular *1]
    }
  }
      * {
    generatedMessage: true,
    code: 'ERR_ASSERTION',
    actual: [Object],
    expected: [Object],
    operator: 'deepEqual'
  }
}
		</failure>
	</testcase>
	<testcase name="invalid subtest fail" time="*" classname="test" failure="test could not be started because its parent finished">
		<failure type="parentAlreadyFinished" message="test could not be started because its parent finished">
Error [ERR_TEST_FAILURE]: test could not be started because its parent finished
    * {
  code: 'ERR_TEST_FAILURE',
  failureType: 'parentAlreadyFinished',
  cause: 'test could not be started because its parent finished'
}
		</failure>
	</testcase>
	<!-- Error: Test "unhandled rejection - passes but warns" at test/fixtures/test-runner/output/output.js:72:1 generated asynchronous activity after the test ended. This activity created the error "Error: rejected from unhandled rejection fail" and would have caused the test to fail, but instead triggered an unhandledRejection event. -->
	<!-- Error: Test "async unhandled rejection - passes but warns" at test/fixtures/test-runner/output/output.js:76:1 generated asynchronous activity after the test ended. This activity created the error "Error: rejected from async unhandled rejection fail" and would have caused the test to fail, but instead triggered an unhandledRejection event. -->
	<!-- Error: A resource generated asynchronous activity after the test ended. This activity created the error "Error: uncaught from outside of a test" which triggered an uncaughtException event, caught by the test runner. -->
	<!-- Error: Test "immediate throw - passes but warns" at test/fixtures/test-runner/output/output.js:80:1 generated asynchronous activity after the test ended. This activity created the error "Error: thrown from immediate throw fail" and would have caused the test to fail, but instead triggered an uncaughtException event. -->
	<!-- Error: Test "immediate reject - passes but warns" at test/fixtures/test-runner/output/output.js:86:1 generated asynchronous activity after the test ended. This activity created the error "Error: rejected from immediate reject fail" and would have caused the test to fail, but instead triggered an unhandledRejection event. -->
	<!-- Error: Test "callback called twice in different ticks" at test/fixtures/test-runner/output/output.js:251:1 generated asynchronous activity after the test ended. This activity created the error "Error [ERR_TEST_FAILURE]: callback invoked multiple times" and would have caused the test to fail, but instead triggered an uncaughtException event. -->
	<!-- Error: Test "callback async throw after done" at test/fixtures/test-runner/output/output.js:269:1 generated asynchronous activity after the test ended. This activity created the error "Error: thrown from callback async throw after done" and would have caused the test to fail, but instead triggered an uncaughtException event. -->
	<!-- tests 76 -->
	<!-- suites 0 -->
	<!-- pass 35 -->
	<!-- fail 25 -->
	<!-- cancelled 3 -->
	<!-- skipped 9 -->
	<!-- todo 4 -->
	<!-- duration_ms * -->
</testsuites>
