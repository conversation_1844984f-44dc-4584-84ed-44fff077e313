<!DOCTYPE html>
<title>non-passive touchstart event listener on body</title>
<link rel="help" href="https://w3c.github.io/touch-events/#cancelability">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-actions.js"></script>
<script src="/resources/testdriver-vendor.js"></script>
<script src="resources/wait-for.js"></script>
<script src="resources/touching.js"></script>
<style>
#touchDiv {
  width: 100px;
  height: 100px;
}
</style>
<div id="touchDiv"></div>
<script>
  document.body.onload = () => runTest({
    target: document.body,
    eventName: 'touchstart',
    passive: false,
    expectCancelable: true,
  });
</script>
