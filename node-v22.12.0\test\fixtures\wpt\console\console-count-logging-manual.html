<!DOCTYPE html>
<html>
<head>
<title>Console Count - Logging Manual Test</title>
<meta name="author" title="<PERSON>" href="mailto:<EMAIL>">
<meta name="assert" content="Console count method default parameter should work">
<link rel="help" href="https://console.spec.whatwg.org/#count">
</head>
<body>
<p>Open the console inside the developer tools. It should contain four entries whose contents are:</p>
<p><code>default: 1</code></p>
<p><code>default: 2</code></p>
<p><code>default: 3</code></p>
<p><code>default: 4</code></p>

<script>
console.count();
console.count(undefined);
console.count("default");
console.count({toString() {return "default"}});
</script>
</body>
</html>
