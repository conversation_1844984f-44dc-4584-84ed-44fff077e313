// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// TODO(ni<PERSON><PERSON><PERSON>): Discuss whether types used by multiple builtins should be
// in global namespace
@cppObjectLayoutDefinition
extern class BigIntBase extends PrimitiveHeapObject
    generates 'TNode<BigInt>';

type BigInt extends BigIntBase;

@cppObjectLayoutDefinition
@hasSameInstanceTypeAsParent
@doNotGenerateCast
extern class MutableBigInt extends BigIntBase generates 'TNode<BigInt>';

Convert<BigInt, MutableBigInt>(i: MutableBigInt): BigInt {
  dcheck(bigint::IsCanonicalized(i));
  return %RawDownCast<BigInt>(Convert<BigIntBase>(i));
}
