<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1">
<meta name="variant" content="?include=passive-false"/>
<meta name="variant" content="?include=passive-true"/>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-actions.js"></script>
<script src="/resources/testdriver-vendor.js"></script>
<script src="/common/subset-tests-by-key.js"></script>
<script src="scroll_support.js"></script>
<style>
body {
  margin: 0;
  padding: 0;
  height: 200vh;
}

.spacer {
  width: 100%;
  height: 25px;
  padding: 0;
  margin: 0;
}

</style>
<head>
<body onload=runTest()>
<div id="initialTarget" class="spacer" style="background: red"></div>
<div id="firstRootSpacer" class="spacer" style="background: green"></div>
<div id="secondRootSpacer" class="spacer" style="background: blue"></div>
</body>

<script>

let variants = [
  {
    key: "passive-false",
    passive: false,
  },
  {
    key: "passive-true",
    passive: true,
  },
];

function runTest() {
  async function testResizeTarget(passive, t) {
    await waitForCompositorReady();

    await waitForCompositorCommit();

    let wheelEventTargets = [];
    let resizedInitialTarget = false;

    // Resize the initial element the wheel event is targetted at once we fire the
    // first wheel event, then log the subsequent wheel event targets.
    function resizeInitialElement(e) {
      wheelEventTargets.push(e.target);
      if (!resizedInitialTarget) {
        resizedInitialTarget = true;
        e.target.style.height = '10px';
      }
    }
    window.addEventListener("wheel", resizeInitialElement, {passive: passive});

    await waitForCompositorCommit();

    await new test_driver.Actions()
      .addWheel("wheel1")
      .scroll(2, 2, 0, 30, {origin: "viewport"})
      .pause(1)
      .scroll(2, 2, 0, 30, {origin: "viewport"})
      .send();

    // TODO(dlrobertson): Use the scrollend event here to wait for the
    // wheel scroll to finish instead of waitForAnimationEnd().
    await waitForAnimationEnd(() => { return document.scrollingElement.scrollTop; });
    await waitForCompositorCommit();

    wheelEventTargets.forEach((wheelEventTarget, i) => {
      assert_equals(wheelEventTarget, initialTarget,
                    "Wheel event at `" + i + "` does not match the expected target")
    });
    assert_greater_than(document.scrollingElement.scrollTop, 0, "The document has scrolled");
  }

  variants.forEach((variant) => {
    subsetTestByKey(variant.key, promise_test, t => testResizeTarget(variant.passive, t),
                    "Resize the initial target and use a passive:" + variant.passive + " listener");
  });
}
</script>
</html>
