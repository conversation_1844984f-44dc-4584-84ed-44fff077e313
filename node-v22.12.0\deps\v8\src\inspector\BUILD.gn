# Copyright 2016 the V8 project authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("../../gni/v8.gni")

_inspector_protocol = v8_path_prefix + "/third_party/inspector_protocol"
import("$_inspector_protocol/inspector_protocol.gni")

_protocol_generated = [
  "protocol/Forward.h",
  "protocol/Protocol.cpp",
  "protocol/Protocol.h",
  "protocol/Console.cpp",
  "protocol/Console.h",
  "protocol/Debugger.cpp",
  "protocol/Debugger.h",
  "protocol/HeapProfiler.cpp",
  "protocol/HeapProfiler.h",
  "protocol/Profiler.cpp",
  "protocol/Profiler.h",
  "protocol/Runtime.cpp",
  "protocol/Runtime.h",
  "protocol/Schema.cpp",
  "protocol/Schema.h",
  "../../include/inspector/Debugger.h",
  "../../include/inspector/Runtime.h",
  "../../include/inspector/Schema.h",
]

action("protocol_compatibility") {
  visibility = [ ":*" ]  # Only targets in this file can depend on this.
  script = "$_inspector_protocol/check_protocol_compatibility.py"
  inputs = [ v8_inspector_js_protocol ]
  _stamp = "$target_gen_dir/js_protocol.stamp"
  outputs = [ _stamp ]
  args = [
    "--stamp",
    rebase_path(_stamp, root_build_dir),
    rebase_path(v8_inspector_js_protocol, root_build_dir),
  ]
}

inspector_protocol_generate("protocol_generated_sources") {
  deps = [ ":protocol_compatibility" ]

  inspector_protocol_dir = _inspector_protocol
  out_dir = target_gen_dir
  _protocol_path = rebase_path(v8_inspector_js_protocol, root_build_dir)
  config_values = [ "protocol.path=$_protocol_path" ]
  config_file = v8_path_prefix + "/src/inspector/inspector_protocol_config.json"
  inputs = [
    v8_inspector_js_protocol,
    config_file,
  ]
  outputs = _protocol_generated
}

config("inspector_config") {
  # Only targets in this file and v8_internal_headers can depend on this.
  visibility = [
    ":*",
    "../..:v8_internal_headers",
  ]
  include_dirs = [ "$target_gen_dir/../../include" ]
}

config("inspector_internal_config") {
  visibility = [ ":*" ]  # Only targets in this file can depend on this.

  configs = [ "../../:internal_config" ]
  include_dirs = [ "../../include" ]
}

v8_header_set("inspector_test_headers") {
  configs = [ ":inspector_internal_config" ]

  public_deps = [ "../..:v8_headers" ]

  sources = [ "test-interface.h" ]
}

v8_source_set("inspector_string_conversions") {
  sources = [
    "v8-string-conversions.cc",
    "v8-string-conversions.h",
  ]
  configs = [ "../..:internal_config_base" ]
  deps = [ "../..:v8_libbase" ]
}

v8_source_set("inspector") {
  deps = [
    ":inspector_string_conversions",
    "../..:v8_tracing",
    "../..:v8_version",
    "../../third_party/inspector_protocol:crdtp",
  ]

  public_deps = [
    ":inspector_test_headers",
    ":protocol_generated_sources",
    "../../:v8_libbase",
  ]

  configs = [ ":inspector_internal_config" ]

  public_configs = [ ":inspector_config" ]

  sources = rebase_path(_protocol_generated, ".", target_gen_dir)
  sources += [
    "../../include/v8-inspector-protocol.h",
    "../../include/v8-inspector.h",
  ]
  sources += [
    "crc32.cc",
    "crc32.h",
    "custom-preview.cc",
    "custom-preview.h",
    "injected-script.cc",
    "injected-script.h",
    "inspected-context.cc",
    "inspected-context.h",
    "remote-object-id.cc",
    "remote-object-id.h",
    "search-util.cc",
    "search-util.h",
    "string-16.cc",
    "string-16.h",
    "string-util.cc",
    "string-util.h",
    "test-interface.cc",
    "v8-console-agent-impl.cc",
    "v8-console-agent-impl.h",
    "v8-console-message.cc",
    "v8-console-message.h",
    "v8-console.cc",
    "v8-console.h",
    "v8-debugger-agent-impl.cc",
    "v8-debugger-agent-impl.h",
    "v8-debugger-barrier.cc",
    "v8-debugger-barrier.h",
    "v8-debugger-id.cc",
    "v8-debugger-id.h",
    "v8-debugger-script.cc",
    "v8-debugger-script.h",
    "v8-debugger.cc",
    "v8-debugger.h",
    "v8-deep-serializer.cc",
    "v8-deep-serializer.h",
    "v8-heap-profiler-agent-impl.cc",
    "v8-heap-profiler-agent-impl.h",
    "v8-inspector-impl.cc",
    "v8-inspector-impl.h",
    "v8-inspector-session-impl.cc",
    "v8-inspector-session-impl.h",
    "v8-profiler-agent-impl.cc",
    "v8-profiler-agent-impl.h",
    "v8-regex.cc",
    "v8-regex.h",
    "v8-runtime-agent-impl.cc",
    "v8-runtime-agent-impl.h",
    "v8-schema-agent-impl.cc",
    "v8-schema-agent-impl.h",
    "v8-serialization-duplicate-tracker.cc",
    "v8-serialization-duplicate-tracker.h",
    "v8-stack-trace-impl.cc",
    "v8-stack-trace-impl.h",
    "v8-value-utils.cc",
    "v8-value-utils.h",
    "value-mirror.cc",
    "value-mirror.h",
  ]
}

#Target to generate all .cc files.
group("v8_generated_cc_files") {
  testonly = true

  deps = [ ":protocol_generated_sources" ]
}
