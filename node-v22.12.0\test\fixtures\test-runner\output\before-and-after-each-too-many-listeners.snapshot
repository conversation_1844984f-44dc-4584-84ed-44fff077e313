TAP version 13
# Subtest: 1
ok 1 - 1
  ---
  duration_ms: *
  ...
# Subtest: 2
ok 2 - 2
  ---
  duration_ms: *
  ...
# Subtest: 3
ok 3 - 3
  ---
  duration_ms: *
  ...
# Subtest: 4
ok 4 - 4
  ---
  duration_ms: *
  ...
# Subtest: 5
ok 5 - 5
  ---
  duration_ms: *
  ...
# Subtest: 6
ok 6 - 6
  ---
  duration_ms: *
  ...
# Subtest: 7
ok 7 - 7
  ---
  duration_ms: *
  ...
# Subtest: 8
ok 8 - 8
  ---
  duration_ms: *
  ...
# Subtest: 9
ok 9 - 9
  ---
  duration_ms: *
  ...
# Subtest: 10
ok 10 - 10
  ---
  duration_ms: *
  ...
# Subtest: 11
ok 11 - 11
  ---
  duration_ms: *
  ...
1..11
# tests 11
# suites 0
# pass 11
# fail 0
# cancelled 0
# skipped 0
# todo 0
# duration_ms *
