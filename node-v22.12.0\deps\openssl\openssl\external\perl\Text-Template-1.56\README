
Text::Template v1.46

This is a library for generating form letters, building HTML pages, or
filling in templates generally.  A `template' is a piece of text that
has little Perl programs embedded in it here and there.  When you
`fill in' a template, you evaluate the little programs and replace
them with their values.  

Here's an example of a template:

	Dear {$title} {$lastname},

	It has come to our attention that you are delinquent in your
	{$monthname[$last_paid_month]} payment.  Please remit
	${sprintf("%.2f", $amount)} immediately, or your patellae may
	be needlessly endangered.

			<PERSON>,

			<PERSON> "{nickname(rand 20)}" Dominus


The result of filling in this template is a string, which might look
something like this:

	Dear Mr. <PERSON>,

	It has come to our attention that you are delinquent in your
	February payment.  Please remit
	$392.12 immediately, or your patellae may
	be needlessly endangered.


			Love,

			<PERSON> "Vizopteryx" Dominus

You can store a template in a file outside your program.  People can
modify the template without modifying the program.  You can separate
the formatting details from the main code, and put the formatting
parts of the program into the template.  That prevents code bloat and
encourages functional separation.

You can fill in the template in a `Safe' compartment.  This means that
if you don't trust the person who wrote the code in the template, you
won't have to worry that they are tampering with your program when you
execute it.  

----------------------------------------------------------------

Text::Template was originally released some time in late 1995 or early
1996.  After three years of study and investigation, I rewrote it from
scratch in January 1999.  The new version, 1.0, was much faster,
delivered better functionality and was almost 100% backward-compatible
with the previous beta versions.

I have added a number of useful features and conveniences since the
1.0 release, while still retaining backward compatibility.  With one
merely cosmetic change, the current version of Text::Template passes
the test suite that the old beta versions passed.

