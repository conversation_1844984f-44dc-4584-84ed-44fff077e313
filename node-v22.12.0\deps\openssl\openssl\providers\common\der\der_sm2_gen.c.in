/*
 * {- join("\n * ", @autowarntext) -}
 *
 * Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#include "prov/der_sm2.h"

/* Well known OIDs precompiled */
{-
    $OUT = oids_to_c::process_leaves('providers/common/der/SM2.asn1',
                                     { dir => $config{sourcedir},
                                       filter => \&oids_to_c::filter_to_C });
-}
