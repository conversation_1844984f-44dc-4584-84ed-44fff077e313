# This file is used by GN for building, which is NOT the build system used for
# building official binaries.
# Please edit the gyp files if you are making changes to build system.

# The actual configurations are put inside a template in unofficial.gni to
# prevent accidental edits from contributors.
template("postject_gn_build") {
  config("postject_config") {
    include_dirs = [ "." ]
  }

  source_set(target_name) {
    forward_variables_from(invoker, "*")
    public_configs = [ ":postject_config" ]
    sources = [ "postject-api.h" ]
  }
}
