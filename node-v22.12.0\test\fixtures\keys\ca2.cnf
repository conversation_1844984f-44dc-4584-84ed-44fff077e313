[ ca ]
default_ca      = CA_default

[ CA_default ]
serial = ca2-serial
crl = ca2-crl.pem
database = ca2-database.txt
name_opt = CA_default
cert_opt = CA_default
default_crl_days = 9999
default_md = sha512


[ req ]
default_bits           = 2048
days                   = 9999
distinguished_name     = req_distinguished_name
attributes             = req_attributes
prompt                 = no
output_password        = password

[ req_distinguished_name ]
C                      = US
ST                     = CA
L                      = SF
O                      = Joyent
OU                     = Node.js
CN                     = ca2
emailAddress           = <EMAIL>

[ req_attributes ]
challengePassword              = A challenge password

