%(import)s

if ('DedicatedWorkerGlobalScope' in self &&
    self instanceof DedicatedWorkerGlobalScope) {
  self.onmessage = event => onMessageFromParent(event, self);
} else if ('SharedWorkerGlobalScope' in self &&
    self instanceof SharedWorkerGlobalScope) {
  onconnect = event => {
    const port = event.ports[0];
    port.onmessage = event => onMessageFromParent(event, port);
  };
}

// Receive a message from the parent and start the test.
function onMessageFromParent(event, port) {
  const configurationError = "%(error)s";
  if (configurationError.length > 0) {
    port.postMessage({error: configurationError});
    return;
  }

  invokeRequest(event.data.subresource,
                event.data.sourceContextList)
    .then(result => port.postMessage(result))
    .catch(e => {
        const message = (e.error && e.error.stack) || e.message || "Error";
        port.postMessage({error: message});
      });
}
