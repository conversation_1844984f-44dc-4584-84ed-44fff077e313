// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

bitfield struct FinalizationRegistryFlags extends uint31 {
  scheduled_for_cleanup: bool: 1 bit;
}

extern class JSFinalizationRegistry extends JSObject {
  native_context: NativeContext;
  cleanup: Callable;
  active_cells: Undefined|WeakCell;
  cleared_cells: Undefined|WeakCell;
  key_map: Object;
  // For the linked list of FinalizationRegistries that need cleanup. This
  // link is weak.
  next_dirty: Undefined|JSFinalizationRegistry;
  flags: SmiTagged<FinalizationRegistryFlags>;
}

extern class WeakCell extends HeapObject {
  finalization_registry: Undefined|JSFinalizationRegistry;
  target: Undefined|JSReceiver|Symbol;
  unregister_token: Undefined|JSReceiver|Symbol;
  holdings: JSAny;

  // For storing doubly linked lists of WeakCells in JSFinalizationRegistry's
  // "active_cells" and "cleared_cells" lists.
  prev: Undefined|WeakCell;
  next: Undefined|WeakCell;

  // For storing doubly linked lists of WeakCells per key in
  // JSFinalizationRegistry's key-based hashmap. The key is the identity hash
  // of unregister_token. WeakCell also needs to know its token, so that we
  // can remove its corresponding key from the key_map when we remove the last
  // WeakCell associated with it or when the unregister_token dies. The
  // unregister token is stored above, after target, as both are weak.
  key_list_prev: Undefined|WeakCell;
  key_list_next: Undefined|WeakCell;
}

extern class JSWeakRef extends JSObject {
  target: Undefined|JSReceiver|Symbol;
}
