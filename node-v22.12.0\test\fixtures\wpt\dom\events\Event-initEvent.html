<!DOCTYPE html>
<title>Event.initEvent</title>
<link rel="author" title="Ms2ger" href="mailto:<EMAIL>">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<div id="log"></div>
<script>
var booleans = [true, false];
booleans.forEach(function(bubbles) {
  booleans.forEach(function(cancelable) {
    test(function() {
      var e = document.createEvent("Event")
      e.initEvent("type", bubbles, cancelable)

      // Step 2.
      // Stop (immediate) propagation flag is tested later
      assert_equals(e.defaultPrevented, false, "defaultPrevented")
      assert_equals(e.returnValue, true, "returnValue")
      // Step 3.
      assert_equals(e.isTrusted, false, "isTrusted")
      // Step 4.
      assert_equals(e.target, null, "target")
      assert_equals(e.srcElement, null, "srcElement")
      // Step 5.
      assert_equals(e.type, "type", "type")
      // Step 6.
      assert_equals(e.bubbles, bubbles, "bubbles")
      // Step 7.
      assert_equals(e.cancelable, cancelable, "cancelable")
    }, "Properties of initEvent(type, " + bubbles + ", " + cancelable + ")")
  })
})

test(function() {
  var e = document.createEvent("Event")
  e.initEvent("type 1", true, false)
  assert_equals(e.type, "type 1", "type (first init)")
  assert_equals(e.bubbles, true, "bubbles (first init)")
  assert_equals(e.cancelable, false, "cancelable (first init)")

  e.initEvent("type 2", false, true)
  assert_equals(e.type, "type 2", "type (second init)")
  assert_equals(e.bubbles, false, "bubbles (second init)")
  assert_equals(e.cancelable, true, "cancelable (second init)")
}, "Calling initEvent multiple times (getting type).")

test(function() {
  // https://bugzilla.mozilla.org/show_bug.cgi?id=998809
  var e = document.createEvent("Event")
  e.initEvent("type 1", true, false)
  assert_equals(e.bubbles, true, "bubbles (first init)")
  assert_equals(e.cancelable, false, "cancelable (first init)")

  e.initEvent("type 2", false, true)
  assert_equals(e.type, "type 2", "type (second init)")
  assert_equals(e.bubbles, false, "bubbles (second init)")
  assert_equals(e.cancelable, true, "cancelable (second init)")
}, "Calling initEvent multiple times (not getting type).")

// Step 2.
async_test(function() {
  // https://www.w3.org/Bugs/Public/show_bug.cgi?id=17715

  var e = document.createEvent("Event")
  e.initEvent("type", false, false)
  assert_equals(e.type, "type", "type (first init)")
  assert_equals(e.bubbles, false, "bubbles (first init)")
  assert_equals(e.cancelable, false, "cancelable (first init)")

  var target = document.createElement("div")
  target.addEventListener("type", this.step_func(function() {
    e.initEvent("fail", true, true)
    assert_equals(e.type, "type", "type (second init)")
    assert_equals(e.bubbles, false, "bubbles (second init)")
    assert_equals(e.cancelable, false, "cancelable (second init)")
  }), false)

  assert_equals(target.dispatchEvent(e), true, "dispatchEvent must return true")

  this.done()
}, "Calling initEvent must not have an effect during dispatching.")

test(function() {
  var e = document.createEvent("Event")
  e.stopPropagation()
  e.initEvent("type", false, false)
  var target = document.createElement("div")
  var called = false
  target.addEventListener("type", function() { called = true }, false)
  assert_false(e.cancelBubble, "cancelBubble must be false")
  assert_true(target.dispatchEvent(e), "dispatchEvent must return true")
  assert_true(called, "Listener must be called")
}, "Calling initEvent must unset the stop propagation flag.")

test(function() {
  var e = document.createEvent("Event")
  e.stopImmediatePropagation()
  e.initEvent("type", false, false)
  var target = document.createElement("div")
  var called = false
  target.addEventListener("type", function() { called = true }, false)
  assert_true(target.dispatchEvent(e), "dispatchEvent must return true")
  assert_true(called, "Listener must be called")
}, "Calling initEvent must unset the stop immediate propagation flag.")

async_test(function() {
  var e = document.createEvent("Event")
  e.initEvent("type", false, false)

  var target = document.createElement("div")
  target.addEventListener("type", this.step_func(function() {
    e.initEvent("type2", true, true);
    assert_equals(e.type, "type", "initEvent type setter should short-circuit");
    assert_false(e.bubbles, "initEvent bubbles setter should short-circuit");
    assert_false(e.cancelable, "initEvent cancelable setter should short-circuit");
  }), false)
  assert_equals(target.dispatchEvent(e), true, "dispatchEvent must return true")

  this.done()
}, "Calling initEvent during propagation.")

test(function() {
  var e = document.createEvent("Event")
  assert_throws_js(TypeError, function() {
    e.initEvent()
  })
}, "First parameter to initEvent should be mandatory.")

test(function() {
  var e = document.createEvent("Event")
  e.initEvent("type")
  assert_equals(e.type, "type", "type")
  assert_false(e.bubbles, "bubbles")
  assert_false(e.cancelable, "cancelable")
}, "Tests initEvent's default parameter values.")
</script>
