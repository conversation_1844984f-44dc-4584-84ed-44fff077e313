<!DOCTYPE html>
%(generated_disclaimer)s
<html>
  <head>
    <meta charset="utf-8">
    <meta name="timeout" content="long">%(meta_delivery_method)s
    <script src="/resources/testharness.js"></script>
    <script src="/resources/testharnessreport.js"></script>
    <script src="/common/security-features/resources/common.sub.js"></script>
%(helper_js)s  </head>
  <body>
    <script>
      TestCase(
        [
          %(scenarios)s
        ],
        new SanityChecker()
      ).start();
    </script>
    <div id="log"></div>
  </body>
</html>
