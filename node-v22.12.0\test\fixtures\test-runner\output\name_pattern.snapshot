TAP version 13
# Subtest: top level skipped test enabled
ok 1 - top level skipped test enabled # SKIP
  ---
  duration_ms: *
  ...
# Subtest: top level it enabled
ok 2 - top level it enabled
  ---
  duration_ms: *
  ...
# Subtest: top level skipped it enabled
ok 3 - top level skipped it enabled # SKIP
  ---
  duration_ms: *
  ...
# Subtest: top level skipped describe enabled
ok 4 - top level skipped describe enabled # SKIP
  ---
  duration_ms: *
  type: 'suite'
  ...
# Subtest: top level runs because name includes PaTtErN
ok 5 - top level runs because name includes PaTtErN
  ---
  duration_ms: *
  ...
# Subtest: top level test enabled
    # Subtest: nested test runs because name includes PATTERN
    ok 1 - nested test runs because name includes PATTERN
      ---
      duration_ms: *
      ...
    1..1
ok 6 - top level test enabled
  ---
  duration_ms: *
  ...
# Subtest: top level describe enabled
    # Subtest: nested it not disabled
    ok 1 - nested it not disabled
      ---
      duration_ms: *
      ...
    # Subtest: nested it enabled
    ok 2 - nested it enabled
      ---
      duration_ms: *
      ...
    # Subtest: nested describe not disabled
    ok 3 - nested describe not disabled
      ---
      duration_ms: *
      type: 'suite'
      ...
    # Subtest: nested describe enabled
        # Subtest: is enabled
        ok 1 - is enabled
          ---
          duration_ms: *
          ...
        1..1
    ok 4 - nested describe enabled
      ---
      duration_ms: *
      type: 'suite'
      ...
    1..4
ok 7 - top level describe enabled
  ---
  duration_ms: *
  type: 'suite'
  ...
# Subtest: yes
    # Subtest: no
    ok 1 - no
      ---
      duration_ms: *
      ...
    # Subtest: yes
    ok 2 - yes
      ---
      duration_ms: *
      ...
    # Subtest: maybe
        # Subtest: no
        ok 1 - no
          ---
          duration_ms: *
          ...
        # Subtest: yes
        ok 2 - yes
          ---
          duration_ms: *
          ...
        1..2
    ok 3 - maybe
      ---
      duration_ms: *
      type: 'suite'
      ...
    1..3
ok 8 - yes
  ---
  duration_ms: *
  type: 'suite'
  ...
# Subtest: no
    # Subtest: yes
    ok 1 - yes
      ---
      duration_ms: *
      ...
    # Subtest: maybe
        # Subtest: yes
        ok 1 - yes
          ---
          duration_ms: *
          ...
        1..1
    ok 2 - maybe
      ---
      duration_ms: *
      type: 'suite'
      ...
    1..2
ok 9 - no
  ---
  duration_ms: *
  type: 'suite'
  ...
# Subtest: no with todo
    # Subtest: yes
    ok 1 - yes
      ---
      duration_ms: *
      ...
    # Subtest: maybe
        # Subtest: yes
        ok 1 - yes
          ---
          duration_ms: *
          ...
        1..1
    ok 2 - maybe
      ---
      duration_ms: *
      type: 'suite'
      ...
    1..2
ok 10 - no with todo # TODO
  ---
  duration_ms: *
  type: 'suite'
  ...
# Subtest: DescribeForMatchWithAncestors
    # Subtest: NestedDescribeForMatchWithAncestors
        # Subtest: NestedTest
        ok 1 - NestedTest
          ---
          duration_ms: *
          ...
        1..1
    ok 1 - NestedDescribeForMatchWithAncestors
      ---
      duration_ms: *
      type: 'suite'
      ...
    1..1
ok 11 - DescribeForMatchWithAncestors
  ---
  duration_ms: *
  type: 'suite'
  ...
1..11
# tests 18
# suites 12
# pass 16
# fail 0
# cancelled 0
# skipped 2
# todo 0
# duration_ms *
