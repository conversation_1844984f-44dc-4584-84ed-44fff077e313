// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

bitfield struct PrototypeInfoFlags extends uint31 {
  should_be_fast: bool: 1 bit;
}

extern class PrototypeInfo extends Struct {
  // [module_namespace]: A backpointer to JSModuleNamespace from its
  // PrototypeInfo (or undefined). This field is only used for JSModuleNamespace
  // maps.  TODO(jkummerow): Figure out if there's a way to store the namespace
  // pointer elsewhere to save memory.
  module_namespace: JSModuleNamespace|Undefined;

  // [prototype_users]: WeakArrayList containing weak references to maps using
  // this prototype, or Smi(0) if uninitialized.
  prototype_users: WeakArrayList|Zero;

  prototype_chain_enum_cache: FixedArray|Zero|Undefined;

  // [registry_slot]: Slot in prototype's user registry where this user
  // is stored. Returns UNREGISTERED if this prototype has not been registered.
  registry_slot: Smi;

  bit_field: SmiTagged<PrototypeInfoFlags>;

  // [derived_maps]: A field caching derived maps. The first entry is the map
  // for Object.create. The following contain entries used by GetDerivedMap
  // which feeds Reflect.construct, proxies and similar.
  derived_maps: WeakArrayList|Undefined;
}
