// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@abstract
extern class JSCollection extends JSObject {
  // The backing hash table.
  //
  // Use StableOrderedHasSet or StableOrderedHashMap to read.
  table: Object;
}
extern class JSSet extends JSCollection {}
extern class JSMap extends JSCollection {}

@abstract
extern class JSWeakCollection extends JSObject {
  // The backing hash table mapping keys to values.
  table: Object;
}
extern class JSWeakSet extends JSWeakCollection {}
extern class JSWeakMap extends JSWeakCollection {}

@abstract extern class JSMapIterator extends JSCollectionIterator;
extern class JSMapKeyIterator extends JSMapIterator
    generates 'TNode<JSMapIterator>';
extern class JSMapKeyValueIterator extends JSMapIterator
    generates 'TNode<JSMapIterator>';
extern class JSMapValueIterator extends JSMapIterator
    generates 'TNode<JSMapIterator>';

@abstract extern class JSSetIterator extends JSCollectionIterator;
extern class JSSetKeyValueIterator extends JSSetIterator
    generates 'TNode<JSSetIterator>';
extern class JSSetValueIterator extends JSSetIterator
    generates 'TNode<JSSetIterator>';

transient type JSSetWithNoCustomIteration extends JSSet;
transient type JSMapWithNoCustomIteration extends JSMap;

// Use these types to read. Used to track user code modifying the underlying
// table.
transient type StableOrderedHashSet extends OrderedHashSet;
transient type StableOrderedHashMap extends OrderedHashMap;
