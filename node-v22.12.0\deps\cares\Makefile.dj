#
# c-ares Makefile for djgpp/gcc/Watt-32.
# Copyright (C) G<PERSON><PERSON>em <<EMAIL>>
# SPDX-License-Identifier: MIT
#
include src/lib/Makefile.inc

OBJ_DIR = djgpp
OBJECTS = $(addprefix $(OBJ_DIR)/, \
            $(CSOURCES:.c=.o))

CSRC = $(addprefix src/lib/, $(CSOURCES))
#CSRC := $(filter-out src/lib/windows_port.c, $(CSOURCES))

OBJ_SUBDIRS = $(OBJ_DIR)/dsa $(OBJ_DIR)/event $(OBJ_DIR)/legacy $(OBJ_DIR)/record $(OBJ_DIR)/str $(OBJ_DIR)/util

VPATH = src/lib src/tools

#
# Root directory for Waterloo tcp/ip.
# WATT_ROOT should be set during Watt-32 install.
#
WATT32_ROOT = $(realpath $(WATT_ROOT))
WATT32_LIB  = $(WATT32_ROOT)/lib/libwatt.a

CFLAGS = -g -O2 -I./include -I./src/lib -I./src/lib/include \
         -I$(WATT32_ROOT)/inc \
         -Wall \
         -Wextra \
         -Waggregate-return \
         -Wcast-align \
         -Wcast-qual \
         -Wconversion \
         -Wdeclaration-after-statement \
         -Wdouble-promotion \
         -Wfloat-equal \
         -Winit-self \
         -Wjump-misses-init \
         -Wlogical-op \
         -Wmissing-braces \
         -Wmissing-declarations \
         -Wmissing-format-attribute \
         -Wmissing-include-dirs \
         -Wmissing-prototypes \
         -Wnested-externs \
         -Wno-coverage-mismatch \
         -Wold-style-definition \
         -Wpacked \
         -Wpointer-arith \
         -Wshadow \
         -Wsign-conversion \
         -Wstrict-overflow \
         -Wstrict-prototypes \
         -Wtrampolines \
         -Wundef \
         -Wunreachable-code \
         -Wunused \
         -Wvariadic-macros \
         -Wvla \
         -Wwrite-strings \
         -Werror=implicit-int \
         -Werror=implicit-function-declaration \
         -Wno-long-long \
         -DWATT32 -DHAVE_CONFIG_H   \
         -D_REENTRANT \
         -DCARES_NO_DEPRECATED \
         -Dselect=select_s

# Can't enable -Wredundant-decls due to WATT32 issues


LDFLAGS = -s

ifeq ($(OS),Windows_NT)
  #
  # Windows hosted djgpp cross compiler. Get it from:
  #   https://github.com/andrewwutw/build-djgpp/releases
  #
  DJ_PREFIX ?= c:/some-path/djgpp/bin/i586-pc-msdosdjgpp-
  CC = $(DJ_PREFIX)gcc

else
  #
  # The normal djgpp 'gcc' for MSDOS.
  #
  CC = gcc
endif

GENERATED = src/lib/ares_config.h \
            include/ares_build.h

TARGETS = libcares.a adig.exe ahost.exe

.SECONDARY: $(OBJ_DIR)/ares_getopt.o

all: $(OBJ_DIR) $(OBJ_SUBDIRS) $(GENERATED) $(TARGETS)
	@echo Welcome to c-ares.

libcares.a: $(OBJECTS)
	ar rs $@ $(OBJECTS)

src/lib/ares_config.h: src/lib/config-dos.h
	cp --update $< $@

include/ares_build.h: include/ares_build.h.dist
	cp --update $< $@

%.exe: src/tools/%.c $(OBJ_DIR)/ares_getopt.o libcares.a
	$(call compile_and_link, $@, $^ $(WATT32_LIB))

# Clean generated files and objects.
#
clean:
	- rm -f depend.dj $(GENERATED) $(OBJ_DIR)/*.o
	- rmdir $(OBJ_SUBDIRS)

# Clean everything
#
realclean vclean: clean
	- rm -f $(TARGETS) $(TARGETS:.exe=.map)

.PHONY: obj_subdirs $(OBJ_SUBDIRS)

obj_subdirs: $(OBJ_SUBDIRS)

$(OBJ_SUBDIRS):
	mkdir $@

$(OBJ_DIR)/%.o: %.c
	$(CC) $(CFLAGS) -o $@ -c $<
	@echo

define compile_and_link
  $(CC) -o $(1) $(CFLAGS) $(LDFLAGS) -Wl,--print-map,--sort-common $(2) > $(1:.exe=.map)
  @echo
endef

DEP_REPLACE = sed -e 's@\(.*\)\.o: @\n$$(OBJ_DIR)\/\1.o: @' \
                  -e 's@$(WATT32_ROOT)@$$(WATT32_ROOT)@g'

#
# One may have to do 'make -f Makefile.dj clean' first in case
# a foreign 'curl_config.h' is making trouble.
#
depend: $(GENERATED) Makefile.dj
	$(CC) -MM $(CFLAGS) $(CSRC) | $(DEP_REPLACE) > depend.dj

-include depend.dj

