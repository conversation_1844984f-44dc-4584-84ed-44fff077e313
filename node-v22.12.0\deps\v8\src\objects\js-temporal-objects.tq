// Copyright 2021 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/objects/js-temporal-objects.h'

bitfield struct JSTemporalCalendarFlags extends uint31 {
  calendar_index: int32: 5 bit;
}

bitfield struct JSTemporalYearMonthDay extends uint31 {
  iso_year: int32: 20 bit;
  iso_month: int32: 4 bit;
  iso_day: int32: 5 bit;
}

bitfield struct JSTemporalHourMinuteSecond extends uint31 {
  iso_hour: int32: 5 bit;
  iso_minute: int32: 6 bit;
  iso_second: int32: 6 bit;
}

bitfield struct JSTemporalSecondParts extends uint31 {
  iso_millisecond: int32: 10 bit;
  iso_microsecond: int32: 10 bit;
  iso_nanosecond: int32: 10 bit;
}

bitfield struct JSTemporalTimeZoneFlags extends uint31 {
  is_offset: bool: 1 bit;
  offset_milliseconds_or_time_zone_index: int32: 28 bit;
}
bitfield struct JSTemporalTimeZoneSubMilliseconds extends uint31 {
  offset_sub_milliseconds: int32: 21 bit;
}

extern class JSTemporalCalendar extends JSObject {
  flags: SmiTagged<JSTemporalCalendarFlags>;
}

extern class JSTemporalDuration extends JSObject {
  years: Number;
  months: Number;
  weeks: Number;
  days: Number;
  hours: Number;
  minutes: Number;
  seconds: Number;
  milliseconds: Number;
  microseconds: Number;
  nanoseconds: Number;
}

extern class JSTemporalInstant extends JSObject {
  nanoseconds: BigInt;
}

extern class JSTemporalPlainDateTime extends JSObject {
  year_month_day: SmiTagged<JSTemporalYearMonthDay>;
  hour_minute_second: SmiTagged<JSTemporalHourMinuteSecond>;
  second_parts: SmiTagged<JSTemporalSecondParts>;
  calendar: JSReceiver;
}

extern class JSTemporalPlainDate extends JSObject {
  year_month_day: SmiTagged<JSTemporalYearMonthDay>;
  calendar: JSReceiver;
}

extern class JSTemporalPlainMonthDay extends JSObject {
  year_month_day: SmiTagged<JSTemporalYearMonthDay>;
  calendar: JSReceiver;
}

extern class JSTemporalPlainTime extends JSObject {
  hour_minute_second: SmiTagged<JSTemporalHourMinuteSecond>;
  second_parts: SmiTagged<JSTemporalSecondParts>;
  calendar: JSReceiver;
}

extern class JSTemporalPlainYearMonth extends JSObject {
  year_month_day: SmiTagged<JSTemporalYearMonthDay>;
  calendar: JSReceiver;
}

extern class JSTemporalTimeZone extends JSObject {
  flags: SmiTagged<JSTemporalTimeZoneFlags>;
  details: SmiTagged<JSTemporalTimeZoneSubMilliseconds>;
}

extern class JSTemporalZonedDateTime extends JSObject {
  nanoseconds: BigInt;
  time_zone: JSReceiver;
  calendar: JSReceiver;
}
