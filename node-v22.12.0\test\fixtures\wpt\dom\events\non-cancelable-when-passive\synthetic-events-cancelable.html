<!DOCTYPE html>
<title>Synthetic events are always cancelable by default</title>
<link rel="help" href="https://dom.spec.whatwg.org/#dictdef-eventinit">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script>
const eventsMap = {
  wheel: 'WheelEvent',
  mousewheel: 'WheelEvent',
  touchstart: 'TouchEvent',
  touchmove: 'TouchEvent',
  touchend: 'TouchEvent',
  touchcancel: 'TouchEvent',
}
function isCancelable(eventName, interfaceName) {
  test(() => {
    assert_implements(interfaceName in self, `${interfaceName} should be supported`);
    let defaultPrevented = null;
    addEventListener(eventName, event => {
      event.preventDefault();
      defaultPrevented = event.defaultPrevented;
    });
    const event = new self[interfaceName](eventName);
    assert_false(event.cancelable, 'cancelable');
    const dispatchEventReturnValue = dispatchEvent(event);
    assert_false(defaultPrevented, 'defaultPrevented');
    assert_true(dispatchEventReturnValue, 'dispatchEvent() return value');
  }, `Synthetic ${eventName} event with interface ${interfaceName} is not cancelable`);
}
for (const eventName in eventsMap) {
  isCancelable(eventName, eventsMap[eventName]);
  isCancelable(eventName, 'Event');
}
</script>
