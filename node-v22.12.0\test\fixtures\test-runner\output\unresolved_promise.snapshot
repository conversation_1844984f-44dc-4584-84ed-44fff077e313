TAP version 13
# Subtest: pass
ok 1 - pass
  ---
  duration_ms: *
  ...
# Subtest: never resolving promise
not ok 2 - never resolving promise
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/unresolved_promise.js:(LINE):1'
  failureType: 'cancelledByParent'
  error: 'Promise resolution is still pending but the event loop has already resolved'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
  ...
# Subtest: fail
not ok 3 - fail
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/unresolved_promise.js:(LINE):1'
  failureType: 'cancelledByParent'
  error: 'Promise resolution is still pending but the event loop has already resolved'
  code: 'ERR_TEST_FAILURE'
  stack: |-
    *
  ...
1..3
# tests 3
# suites 0
# pass 1
# fail 0
# cancelled 2
# skipped 0
# todo 0
# duration_ms *
