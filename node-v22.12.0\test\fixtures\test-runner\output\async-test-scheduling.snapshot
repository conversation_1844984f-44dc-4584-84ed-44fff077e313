TAP version 13
# Subtest: test
ok 1 - test
  ---
  duration_ms: *
  ...
# Subtest: suite
    # Subtest: test
    ok 1 - test
      ---
      duration_ms: *
      ...
    # Subtest: scheduled async
    ok 2 - scheduled async
      ---
      duration_ms: *
      ...
    1..2
ok 2 - suite
  ---
  duration_ms: *
  type: 'suite'
  ...
# Subtest: scheduled async
ok 3 - scheduled async
  ---
  duration_ms: *
  ...
1..3
# tests 4
# suites 1
# pass 4
# fail 0
# cancelled 0
# skipped 0
# todo 0
# duration_ms *
