<!DOCTYPE html>
<html>
<head>
<meta charset="euc-kr"> <!-- test breaks if the server overrides this -->
<title>EUC-KR encoding errors (form, misc)</title>
<meta name="timeout" content="long">
<meta name="variant" content="?1-1000">
<meta name="variant" content="?1001-2000">
<meta name="variant" content="?2001-3000">
<meta name="variant" content="?3001-last">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/common/subset-tests.js"></script>
<script src="euckr_index.js"></script>
<script src="euckr-encoder.js"></script>
<link rel="author" title="Richard Ishida" href="mailto:<EMAIL>">
<link rel="help" href="https://encoding.spec.whatwg.org/#euc-kr">
<meta name="assert" content="The browser produces percent-escaped character references for a URL produced by a form when encoding miscellaneous characters that are not in the euc-kr encoding.">
<style>
 iframe { display:none }
 form { display:none }
</style>
</head>
<body>
<div id="log"></div>
<script src="../../resources/ranges.js"></script>
<script>
var errors = true;
var encoder = euckrEncoder;
var ranges = rangesMisc;
var separator = ",";
function expect(result, codepoint) {
  return "%26%23" + codepoint + "%3B";
}
// Overwrite normalizeStr
function normalizeStr(str) {
  return str;
}
</script>
<script src="../../resources/encode-form-common.js"></script>
</body>
</html>
