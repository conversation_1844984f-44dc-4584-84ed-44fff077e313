TAP version 13
# Subtest: promise timeout signal
    # Subtest: ok 1
    ok 1 - ok 1
      ---
      duration_ms: *
      ...
    # Subtest: ok 2
    ok 2 - ok 2
      ---
      duration_ms: *
      ...
    # Subtest: ok 3
    ok 3 - ok 3
      ---
      duration_ms: *
      ...
    # Subtest: ok 4
    ok 4 - ok 4
      ---
      duration_ms: *
      ...
    # Subtest: not ok 1
    not ok 5 - not ok 1
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):7'
      failureType: 'cancelledByParent'
      error: 'test did not finish before its parent and was cancelled'
      code: 'ERR_TEST_FAILURE'
      ...
    # Subtest: not ok 2
    not ok 6 - not ok 2
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):7'
      failureType: 'cancelledByParent'
      error: 'test did not finish before its parent and was cancelled'
      code: 'ERR_TEST_FAILURE'
      ...
    # Subtest: not ok 3
    not ok 7 - not ok 3
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):7'
      failureType: 'testAborted'
      error: 'This operation was aborted'
      code: 20
      name: 'AbortError'
      stack: |-
        *
        *
        *
        *
        *
        *
        *
        *
        *
        *
      ...
    # Subtest: not ok 4
    not ok 8 - not ok 4
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):7'
      failureType: 'testAborted'
      error: 'This operation was aborted'
      code: 20
      name: 'AbortError'
      stack: |-
        *
        *
        *
        *
        *
        *
        *
        *
        *
        *
      ...
    # Subtest: not ok 5
    not ok 9 - not ok 5
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):7'
      failureType: 'testAborted'
      error: 'This operation was aborted'
      code: 20
      name: 'AbortError'
      stack: |-
        *
        *
        *
        *
        *
        *
        *
        *
        *
        *
      ...
    1..9
not ok 1 - promise timeout signal
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/abort.js:(LINE):1'
  failureType: 'testAborted'
  error: 'The operation was aborted due to timeout'
  code: 23
  name: 'TimeoutError'
  stack: |-
    *
    *
    *
    *
  ...
# Subtest: promise abort signal
not ok 2 - promise abort signal
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/abort.js:(LINE):1'
  failureType: 'testAborted'
  error: 'This operation was aborted'
  code: 20
  name: 'AbortError'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
    *
    *
    *
  ...
# Subtest: callback timeout signal
    # Subtest: ok 1
    ok 1 - ok 1
      ---
      duration_ms: *
      ...
    # Subtest: ok 2
    ok 2 - ok 2
      ---
      duration_ms: *
      ...
    # Subtest: ok 3
    ok 3 - ok 3
      ---
      duration_ms: *
      ...
    # Subtest: ok 4
    ok 4 - ok 4
      ---
      duration_ms: *
      ...
    # Subtest: not ok 1
    not ok 5 - not ok 1
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):5'
      failureType: 'cancelledByParent'
      error: 'test did not finish before its parent and was cancelled'
      code: 'ERR_TEST_FAILURE'
      ...
    # Subtest: not ok 2
    not ok 6 - not ok 2
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):5'
      failureType: 'cancelledByParent'
      error: 'test did not finish before its parent and was cancelled'
      code: 'ERR_TEST_FAILURE'
      ...
    # Subtest: not ok 3
    not ok 7 - not ok 3
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):5'
      failureType: 'testAborted'
      error: 'This operation was aborted'
      code: 20
      name: 'AbortError'
      stack: |-
        *
        *
        *
        *
        *
        *
        *
        *
        *
        *
      ...
    # Subtest: not ok 4
    not ok 8 - not ok 4
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):5'
      failureType: 'testAborted'
      error: 'This operation was aborted'
      code: 20
      name: 'AbortError'
      stack: |-
        *
        *
        *
        *
        *
        *
        *
        *
        *
        *
      ...
    # Subtest: not ok 5
    not ok 9 - not ok 5
      ---
      duration_ms: *
      location: '/test/fixtures/test-runner/output/abort.js:(LINE):5'
      failureType: 'testAborted'
      error: 'This operation was aborted'
      code: 20
      name: 'AbortError'
      stack: |-
        *
        *
        *
        *
        *
        *
        *
        *
        *
        *
      ...
    1..9
not ok 3 - callback timeout signal
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/abort.js:(LINE):1'
  failureType: 'testAborted'
  error: 'The operation was aborted due to timeout'
  code: 23
  name: 'TimeoutError'
  stack: |-
    *
    *
    *
    *
  ...
# Subtest: callback abort signal
not ok 4 - callback abort signal
  ---
  duration_ms: *
  location: '/test/fixtures/test-runner/output/abort.js:(LINE):1'
  failureType: 'testAborted'
  error: 'This operation was aborted'
  code: 20
  name: 'AbortError'
  stack: |-
    *
    *
    *
    *
    *
    *
    *
    *
    *
    *
  ...
1..4
# tests 22
# suites 0
# pass 8
# fail 0
# cancelled 14
# skipped 0
# todo 0
# duration_ms *
